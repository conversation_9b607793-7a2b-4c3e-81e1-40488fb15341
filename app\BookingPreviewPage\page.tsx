"use client";
import React, { useCallback, useEffect, useState, useRef } from "react";
import "./BookingPreviewPage.scss";
import FareSummary from "./components/FareSummary/FareSummary";
import OffersSection from "./components/OffersSection/OffersSection";
import Link from "next/link";
import GuestDetailsForm, { GuestDetailsFormRef } from "./components/GuestDetailsForm/GuestDetailsForm";
import PropertyDetails from "./components/PropertyDetails/PropertyDetails";
import CancellationPolicy from "./components/CancellationPolicy/CancellationPolicy";
import SpecialRequest from "./components/SpecialRequest/SpecialRequest";
import MobileBookingHeader from "./components/MobileBookingHeader/MobileBookingHeader";
import FareSummaryShimmer from "./components/FareSummaryShimmer/FareSummaryShimmer";
import Image from "next/image";
import OffersSectonShimmr from "./components/OffersSectionShimmer/OffersSectonShimmr";
import GuestDetailsFormShimmer from "./components/GuestDetailsFormShimmer/GuestDetailsFormShimmer";
import PropertyDetailsShimmer from "./components/ProperyDetailsShimmer/PropertyDetailsShimmer";
import CancellationPolicyShimmer from "./components/CancellationPolicyShimmer/CancellationPolicyShimmer";
import useScrollLock from "../components/utilities/ScrollLock/useScrollLock";
import { useRouter } from "next/navigation";
import { BookingDetail } from "./hotel-booking-details.model";
import { useCommonContext } from "../contexts/commonContext";
import { ShieldCheck } from "lucide-react";
import visa from 'public/assets/img/payment-methods/visa.webp'
import master from 'public/assets/img/payment-methods/mastercard.webp'
import paypal from 'public/assets/img/payment-methods/paypal.webp'
import { getSelectedRoom } from "@/utils/selectedRoomStorage";
import { getStoredHotelDetails } from "@/utils/hotelDetailsStorage";
import { HotelBookingResponse } from "./booking-init.model";
import { storeBookingInitData, getBookingSummary, isBookingInitDataValid } from "./utils/bookingInitUtils";
import { initializeHotelBooking } from "@/api/hotel/booking-init-service";
import { createHotelBooking } from "@/api/hotel/hotel-booking-service";
import { transformToBookingRequest, validateBookingDataAvailability, validateBookingRequest } from "./utils/bookingApiUtils";

function Page() {
  const navigation = useRouter();
  //const [isLoading, setIsLoading] = useState<boolean>(true);
  const [isMobile, setIsMobile] = useState<boolean>(false);
  // const [cancellationBenefits, setCancellationBenefits] =
  //   useState<CancellationBenefits>();
  const [isMobileBenfitsActive, setIsMobileBenefitsActive] = useState<boolean>(false);
  const [hotelBookingDetails, setHotelBookingDetails] = useState<BookingDetail>();
  const [bookingInitData, setBookingInitData] = useState<HotelBookingResponse | null>(null);
  const [bookingInitError, setBookingInitError] = useState<string | null>(null);
  const [isBookingInitLoading, setIsBookingInitLoading] = useState<boolean>(false);
  const [dataUpdatedNotification, setDataUpdatedNotification] = useState<string | null>(null);
  const notificationTimerRef = useRef<ReturnType<typeof setTimeout> | null>(null);
  const hasSyncedWithInitDataRef = useRef<boolean>(false);
  
  // Booking API states
  const [isBookingLoading, setIsBookingLoading] = useState<boolean>(false);
  const [bookingError, setBookingError] = useState<string | null>(null);
  const [bookingSuccess, setBookingSuccess] = useState<boolean>(false);
  const {  setIsLoading, isLoading, hotelDetailsResponse, hotelSearchFormData, searchKey , selectedHotelId } = useCommonContext();

  // Ref for guest details form validation
  const guestDetailsFormRef = useRef<GuestDetailsFormRef>(null);

  // Derive guest count and all guests requirement
  const totalGuestsCount = (hotelSearchFormData?.travelers?.adults || 0) + (hotelSearchFormData?.travelers?.children || 0) || 1;
  const isAllGuestsInfoRequired = !!(bookingInitData?.data?.hotel?.rates?.[0]?.allGuestsInfoRequired);
  const isPassportMandatory = !!(bookingInitData?.data?.hotel?.rates?.[0]?.IsPassportMandatory);

  // Booking initialization API function using service
  const initializeBooking = useCallback(async () => {
    try {
      setIsBookingInitLoading(true);
      setBookingInitError(null);

      // Get required data for booking initialization
      const selectedRoom = getSelectedRoom();
      const hotelDetails = getStoredHotelDetails();

      if (!selectedRoom || !hotelDetails) {
        throw new Error('Missing required booking data');
      }

      const searchKeyToUse = searchKey || localStorage.getItem('searchKey') || "";
      const hotelIdToUse =  hotelDetails.hotel_id || "";
      const recommendationIdToUse = selectedRoom.recommendationId || "";

      console.log('🚀 Initializing booking with parameters:', {
        searchKey: searchKey,
        hotelId: hotelIdToUse,
        recommendationId: recommendationIdToUse
      });

      // Use the booking initialization service
      const response = await initializeHotelBooking(
        searchKeyToUse,
        hotelIdToUse,
        recommendationIdToUse
      );

      console.log('✅ Booking initialization successful via service');

      // Log detailed booking summary
      const bookingSummary = getBookingSummary(response);
      console.log('� Booking Summary:', bookingSummary);

      // Store in state and localStorage
      setBookingInitData(response);
      storeBookingInitData(response);

      return response;
    } catch (error) {
      console.error('❌ Booking initialization failed:', error);
      const errorMessage = error instanceof Error
        ? error.message
        : 'Failed to initialize booking';
      setBookingInitError(errorMessage);
      throw error;
    } finally {
      setIsBookingInitLoading(false);
    }
  }, [searchKey]);



  useEffect(() => {
    const handleResize = () => {
      setIsMobile(window.innerWidth <= 780);
    };
    handleResize(); // initial check
    window.addEventListener("resize", handleResize);
    return () => window.removeEventListener("resize", handleResize);
  }, []);

  useEffect(() => {
    setTimeout(() => {
      setIsLoading(false);
    }, 2000);
  }, []);

  // Compare and update component data with booking init API data
  const compareAndUpdateComponentData = useCallback(() => {
    if (!bookingInitData) return;

    const selectedRoom = getSelectedRoom();
    const hotelDetails = getStoredHotelDetails();
    const bookingRoom = bookingInitData.data.hotel.rooms[0];
    const bookingRate = bookingInitData.data.hotel.rates[0];

    console.log('🔍 Comparing booking init data with existing component data...');

    let hasUpdates = false;
    const updates: any = {};

    // Compare room details
    if (selectedRoom && bookingRoom) {
      if (selectedRoom.room.name !== bookingRoom.name) {
        console.log('🔄 Room name difference detected:', {
          current: selectedRoom.room.name,
          bookingInit: bookingRoom.name
        });
        updates.roomName = bookingRoom.name;
        hasUpdates = true;
      }

      if (selectedRoom.room.description !== bookingRoom.description) {
        console.log('🔄 Room description difference detected:', {
          current: selectedRoom.room.description,
          bookingInit: bookingRoom.description
        });
        updates.roomDescription = bookingRoom.description;
        hasUpdates = true;
      }

      if (selectedRoom.room.maxGuestAllowed !== bookingRoom.maxGuestAllowed) {
        console.log('🔄 Max guest capacity difference detected:', {
          current: selectedRoom.room.maxGuestAllowed,
          bookingInit: bookingRoom.maxGuestAllowed
        });
        updates.maxGuests = bookingRoom.maxGuestAllowed;
        hasUpdates = true;
      }

      // Compare facilities
      const currentFacilities = selectedRoom.room.facilities?.map(f => f.name) || [];
      const bookingFacilities = bookingRoom.facilities?.map(f => f.name) || [];

      if (JSON.stringify(currentFacilities.sort()) !== JSON.stringify(bookingFacilities.sort())) {
        console.log('🔄 Room facilities difference detected:', {
          current: currentFacilities,
          bookingInit: bookingFacilities
        });
        updates.facilities = bookingRoom.facilities;
        hasUpdates = true;
      }
    }

    // Compare pricing (base rate, total rate)
    if (selectedRoom && bookingRate) {
      if (selectedRoom.rate.baseRate !== bookingRate.baseRate) {
        console.log('🔄 Base rate difference detected:', {
          current: selectedRoom.rate.baseRate,
          bookingInit: bookingRate.baseRate
        });
        updates.baseRate = bookingRate.baseRate;
        hasUpdates = true;
      }

      if (selectedRoom.rate.totalRate !== bookingRate.totalRate) {
        console.log('🔄 Total rate difference detected:', {
          current: selectedRoom.rate.totalRate,
          bookingInit: bookingRate.totalRate
        });
        updates.totalRate = bookingRate.totalRate;
        hasUpdates = true;
      }
    }

    if (hasUpdates) {
      console.log('⚠️ Data discrepancies found! Updating components with booking init data...');
      updateComponentsWithBookingInitData(updates);

      // Show notification to user
      const updateTypes = Object.keys(updates);
      setDataUpdatedNotification(`Updated: ${updateTypes.join(', ')}`);

      // Hide notification after 5 seconds
      if (notificationTimerRef.current) {
        clearTimeout(notificationTimerRef.current);
      }
      notificationTimerRef.current = setTimeout(() => {
        setDataUpdatedNotification(null);
        notificationTimerRef.current = null;
      }, 5000);
    } else {
      console.log('✅ All component data matches booking init API - no updates needed');
    }
  }, [bookingInitData]);

  // Update components with booking init data
  const updateComponentsWithBookingInitData = useCallback((updates: any) => {
    // Update the hotelBookingDetails state with corrected data
    setHotelBookingDetails(prevDetails => {
      if (!prevDetails) return prevDetails;

      const updatedDetails = { ...prevDetails };

      // Update room details if changed
      if (updates.roomName || updates.roomDescription || updates.maxGuests || updates.facilities) {
        updatedDetails.roomDetail = {
          ...updatedDetails.roomDetail,
          ...(updates.roomName && { name: updates.roomName }),
          ...(updates.roomDescription && { description: updates.roomDescription }),
          ...(updates.maxGuests && { maxGuestAllowed: updates.maxGuests })
        };

        // Update room benefits with new facilities
        if (updates.facilities) {
          const facilityNames = updates.facilities.map((f: any) => f.name);
          updatedDetails.roomBenefits = {
            ...updatedDetails.roomBenefits,
            otherBenefits: facilityNames
          };
        }
      }

      console.log('✅ Component data updated with booking init API data');
      return updatedDetails;
    });
  }, []);

  // Reset sync flag when booking init data changes
  useEffect(() => {
    hasSyncedWithInitDataRef.current = false;
  }, [bookingInitData]);

  // Trigger comparison when booking init data is available, but only once per init load
  useEffect(() => {
    if (bookingInitData && hotelBookingDetails && !hasSyncedWithInitDataRef.current) {
      compareAndUpdateComponentData();
      hasSyncedWithInitDataRef.current = true;
    }
  }, [bookingInitData, hotelBookingDetails, compareAndUpdateComponentData]);

  // Cleanup notification timer on unmount
  useEffect(() => {
    return () => {
      if (notificationTimerRef.current) {
        clearTimeout(notificationTimerRef.current);
      }
    };
  }, []);

  // Helper function to map facility names to FontAwesome icons
  const getFacilityIcon = (facilityName: string): string => {
    const name = facilityName.toLowerCase();
    if (name.includes('wifi') || name.includes('internet')) return 'fa-wifi';
    if (name.includes('tv') || name.includes('television')) return 'fa-tv';
    if (name.includes('air') || name.includes('conditioning')) return 'fa-snowflake';
    if (name.includes('bath') || name.includes('shower')) return 'fa-shower';
    if (name.includes('bed') || name.includes('bedroom')) return 'fa-bed';
    if (name.includes('kitchen') || name.includes('kitchenette')) return 'fa-utensils';
    if (name.includes('balcony') || name.includes('terrace')) return 'fa-seedling';
    if (name.includes('pool') || name.includes('swimming')) return 'fa-swimming-pool';
    if (name.includes('gym') || name.includes('fitness')) return 'fa-dumbbell';
    if (name.includes('parking')) return 'fa-car';
    if (name.includes('restaurant') || name.includes('dining')) return 'fa-utensils';
    if (name.includes('spa') || name.includes('massage')) return 'fa-spa';
    if (name.includes('safe') || name.includes('security')) return 'fa-lock';
    if (name.includes('desk') || name.includes('work')) return 'fa-desk';
    if (name.includes('coffee') || name.includes('tea')) return 'fa-coffee';
    return 'fa-check'; // Default icon
  };

  // Helper function to format dates
  const formatDate = (dateString: string | null | undefined): string => {
    if (!dateString) return '';
    try {
      const date = new Date(dateString);
      return date.toLocaleDateString('en-US', {
        weekday: 'short',
        day: '2-digit',
        month: 'short'
      });
    } catch {
      return dateString;
    }
  };

  // Helper function to calculate night count
  const calculateNights = (checkIn: string | null | undefined, checkOut: string | null | undefined): number => {
    if (!checkIn || !checkOut) return 1;
    try {
      const checkInDate = new Date(checkIn);
      const checkOutDate = new Date(checkOut);
      const diffTime = checkOutDate.getTime() - checkInDate.getTime();
      const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
      return Math.max(1, diffDays);
    } catch {
      return 1;
    }
  };

  // Helper function to format currency with proper symbol and formatting
  const formatCurrency = (amount: number, currency: string = "INR"): { symbol: string; mainAmount: string; decimal: string } => {
    const currencySymbol = currency === "INR" ? "₹" : currency === "USD" ? "$" : currency === "AED" ? "AED" : currency;
    const formattedAmount = Number(amount || 0).toFixed(2);
    const [mainAmount, decimal] = formattedAmount.split('.');

    return {
      symbol: currencySymbol,
      mainAmount: Number(mainAmount).toLocaleString('en-IN'),
      decimal: decimal
    };
  };

  // Helper function to get total amount for display
  const getTotalAmount = (): { symbol: string; mainAmount: string; decimal: string; isLoading: boolean } => {
    // Priority: bookingInitData (most accurate) > hotelBookingDetails > fallback
    if (bookingInitData?.data?.hotel?.rates?.[0]?.totalRate) {
      const formatted = formatCurrency(
        bookingInitData.data.hotel.rates[0].totalRate,
        bookingInitData.data.currency
      );
      return { ...formatted, isLoading: false };
    } else if (hotelBookingDetails?.fareDetail?.totalBookingAmount) {
      const formatted = formatCurrency(hotelBookingDetails.fareDetail.totalBookingAmount);
      return { ...formatted, isLoading: false };
    } else {
      // Show loading state when data is not yet available
      return { symbol: "₹", mainAmount: "---", decimal: "--", isLoading: true };
    }
  };

  // Helper function to transform cancellation policies
  const transformCancellationPolicies = (cancellationPolicies: any[], checkInDate: string | null | undefined) => {
    if (!cancellationPolicies || cancellationPolicies.length === 0) {
      return [];
    }

    const policies: any[] = [];

    cancellationPolicies.forEach((policy) => {
      if (policy.rules && Array.isArray(policy.rules)) {
        policy.rules.forEach((rule: any) => {
          let code = 'NON_REFUNDABLE';
          let title = 'Non-refundable';
          let subTitle = '100% amount will be deducted on cancellation';

          // Handle different value types
          if (rule.valueType === 'PERCENTAGE') {
            if (rule.value === 0) {
              // 0% penalty = fully refundable
              code = 'FULLY_REFUNDABLE';
              title = `Fully refundable before ${formatDate(rule.end)}`;
              subTitle = `Cancel your reservation before ${formatDate(rule.end)}, to get a full refund`;
            } else if (rule.value > 0 && rule.value < 100) {
              // Partial penalty = partially refundable
              code = 'PARTIALLY_REFUNDABLE';
              const refundPercentage = 100 - rule.value;
              title = `${refundPercentage}% refundable before ${formatDate(rule.end)}`;
              subTitle = `Cancel before ${formatDate(rule.end)}, ${rule.value}% penalty applies (${refundPercentage}% refund)`;
            } else if (rule.value >= 100) {
              // 100% penalty = non-refundable
              code = 'NON_REFUNDABLE';
              title = `Non-refundable after ${formatDate(rule.start)}`;
              subTitle = `100% amount will be deducted on cancellation after ${formatDate(rule.start)}`;
            }
          } else if (rule.valueType === 'AMOUNT') {
            // Fixed amount penalty
            if (rule.value === 0) {
              code = 'FULLY_REFUNDABLE';
              title = `Fully refundable before ${formatDate(rule.end)}`;
              subTitle = `Cancel your reservation before ${formatDate(rule.end)}, to get a full refund`;
            } else {
              code = 'PARTIALLY_REFUNDABLE';
              title = `Cancellation fee applies before ${formatDate(rule.end)}`;
              subTitle = `Cancel before ${formatDate(rule.end)}, ${rule.value} cancellation fee applies`;
            }
          } else if (rule.valueType === 'Nights') {
            // Nights penalty
            if (rule.value === 0) {
              code = 'FULLY_REFUNDABLE';
              title = `Free cancellation before ${formatDate(rule.end)}`;
              subTitle = `Cancel your reservation before ${formatDate(rule.end)} for free`;
            } else if (rule.value === 1) {
              code = 'PARTIALLY_REFUNDABLE';
              title = `1 night penalty after ${formatDate(rule.start)}`;
              subTitle = `Cancel after ${formatDate(rule.start)}, 1 night charge applies`;
            } else {
              code = 'PARTIALLY_REFUNDABLE';
              title = `${rule.value} nights penalty after ${formatDate(rule.start)}`;
              subTitle = `Cancel after ${formatDate(rule.start)}, ${rule.value} nights charge applies`;
            }
          }

          policies.push({
            code,
            title,
            subTitle,
            cancellationDate: rule.end || '',
            checkInDate: checkInDate || '',
            startDate: rule.start || '',
            endDate: rule.end || '',
            cancellationAmount: rule.estimatedValue || 0
          });
        });
      }
    });

    return policies;
  };

  const getHotelBookingDetails = useCallback(async () => {
    try {
      // Try to get real data - prioritize context, fallback to localStorage
      const selectedRoom = getSelectedRoom();
      const storedHotelDetails = getStoredHotelDetails();

      // Debug logs (can be removed in production)
      console.log('🔍 Debug - Data availability:', {
        selectedRoom: selectedRoom ? 'Available' : 'Missing',
        hotelDetailsResponse: hotelDetailsResponse ? 'Available' : 'Missing',
        storedHotelDetails: storedHotelDetails ? 'Available' : 'Missing',
        hotelSearchFormData: hotelSearchFormData ? 'Available' : 'Missing'
      });

      if (selectedRoom && (hotelDetailsResponse || storedHotelDetails)) {
        // Use hotel details (prioritize context, fallback to localStorage)
        const hotelDetails = hotelDetailsResponse || storedHotelDetails;

        // 🔍 RAW API DATA LOGGING (Before Transformation)
        console.log('📊 Raw Selected Room Data:', selectedRoom);
        console.log('🏨 Raw Hotel Details Data:', hotelDetails);
        console.log('📅 Raw Search Form Data:', hotelSearchFormData);

        // Debug specific data structures
        if (selectedRoom?.rate?.cancellationPolicies) {
          console.log('❌ Raw Cancellation Policies:', selectedRoom.rate.cancellationPolicies);
        }
        if (selectedRoom?.room?.facilities) {
          console.log('🛏️ Raw Room Facilities:', selectedRoom.room.facilities);
        }
        if (selectedRoom?.rate) {
          console.log('💰 Raw Rate Data:', selectedRoom.rate);
        }

        console.log("🏨 Using real booking data:", {
          selectedRoom: selectedRoom.room.name,
          hotel: hotelDetails?.name,
          dataSource: hotelDetailsResponse ? 'context (fresh)' : 'localStorage (cached)'
        });

        // Combine real data into BookingDetail format
        const combinedBookingData: BookingDetail = {
          locationId: 0,
          hotelId: parseInt(hotelDetails?.hotel_id || '0'),
          name: hotelDetails?.name || 'Hotel Name',
          locality: hotelDetails?.stateName || '',
          city: hotelDetails?.city || '',
          address: hotelDetails?.address || '',
          starRating: hotelDetails?.starRating?.toString() || '0',
          thumbnailImageURL: hotelDetails?.hero_image || hotelDetails?.images[0].image_path || '',

          // Travel details from search form data
          travelDetails: {
            travelDates: {
              checkinDate: hotelSearchFormData?.checkInDate || '',
              checkoutDate: hotelSearchFormData?.checkOutDate || '',
              formattedCheckinDate: formatDate(hotelSearchFormData?.checkInDate),
              formattedCheckoutDate: formatDate(hotelSearchFormData?.checkOutDate),
              epochCheckinDate: hotelSearchFormData?.checkInDate ? new Date(hotelSearchFormData.checkInDate).getTime() : 0,
              epochCheckoutDate: hotelSearchFormData?.checkOutDate ? new Date(hotelSearchFormData.checkOutDate).getTime() : 0,
              checkinTime: '15:00',
              checkoutTime: '11:00'
            },
            roomCount: hotelSearchFormData?.roomsData?.length || 1,
            adultCount: hotelSearchFormData?.travelers?.adults || 2,
            childCount: hotelSearchFormData?.travelers?.children || 0,
            childAges: hotelSearchFormData?.roomsData?.[0]?.childrenAges?.map(child => child.age) || [],
            roomType: selectedRoom.room.name,
            nightCount: calculateNights(hotelSearchFormData?.checkInDate, hotelSearchFormData?.checkOutDate),
            stayHours: null,
            durationOfStay: `${calculateNights(hotelSearchFormData?.checkInDate, hotelSearchFormData?.checkOutDate)} Night${calculateNights(hotelSearchFormData?.checkInDate, hotelSearchFormData?.checkOutDate) > 1 ? 's' : ''}`,
            nextDayHourlyCheckout: false
          },

          // Fare details from selected room
          fareDetail: {
            displayedBaseFare: selectedRoom.rate.baseRate,
            baseFare: selectedRoom.rate.baseRate,
            markUpFare: 0,
            markupDiscountPercent: null,
            taxesAndFees: selectedRoom.calculatedValues.taxesAndFees,
            totalPrice: selectedRoom.rate.totalRate,
            totalDiscount: selectedRoom.calculatedValues.savings,
            totalPGAmount: selectedRoom.rate.totalRate,
            totalBookingAmount: selectedRoom.rate.totalRate,
            burnMoneyInfo: { burnAmount: 0, burnIxiMoneyAmount: 0, burnIxiMoneyMaxAmount: 0, title: '', text: '' },
            couponCode: '', cashback: null, cashbackText: null, instantDiscount: 0,
            bankOfferTextIncluded: false, offerText: {}, offerMessage: null,
            taxAndChargeMap: { "Hotel Tax": selectedRoom.calculatedValues.taxesAndFees, "Convenience Fee": 0 },
            discountMap: { "Reversal of Convenience fee": 0, "Coupon Discount": 0, "Supplier Discount": selectedRoom.calculatedValues.savings },
            excludedChargeMap: {}, discountViewType: '', method: '',
            totalBaseFare: selectedRoom.rate.baseRate, totalTaxesAndFees: selectedRoom.calculatedValues.taxesAndFees,
            convenienceFee: 0, totalConvenienceFee: 0, paymentType: '', payAtHotelAmount: null, offset: 0
          },

          // Room details
          roomDetail: {
            roomImageUrl: selectedRoom.room.images?.[0]?.links?.[0]?.url || '',
            properties: [
              // Room size (if available)
              ...(selectedRoom.room.maxGuestAllowed ? [{
                code: "fa-user-group",
                data: `Sleeps ${selectedRoom.room.maxGuestAllowed}`
              }] : []),
              // Room facilities with appropriate icons
              ...selectedRoom.room.facilities?.slice(0, 6).map(facility => ({
                code: getFacilityIcon(facility.name),
                data: facility.name || ''
              })) || []
            ]
          },

          // Room benefits
          roomBenefits: {
            mealBenefits: selectedRoom.rate.boardBasis ? [selectedRoom.rate.boardBasis.description] : null,
            otherBenefits: selectedRoom.room.facilities?.map(f => f.name) || []
          },

          // Cancellation benefits
          cancellationBenefits: (() => {
            const transformedPolicies = transformCancellationPolicies(
              selectedRoom.rate.cancellationPolicies,
              hotelSearchFormData?.checkInDate
            );

            // 🔍 Debug transformed cancellation policies
            console.log('🔄 Transformed Cancellation Policies:', transformedPolicies);

            // Determine overall cancellation status
            const hasFreeCancellation = transformedPolicies.some(p => p.code === 'FULLY_REFUNDABLE');
            const hasPartialRefund = transformedPolicies.some(p => p.code === 'PARTIALLY_REFUNDABLE');

            let overallCode = 'NON_REFUNDABLE';
            let overallData = 'Non-refundable booking';

            if (hasFreeCancellation) {
              overallCode = 'FULLY_REFUNDABLE';
              const freePolicy = transformedPolicies.find(p => p.code === 'FULLY_REFUNDABLE');
              overallData = `Free Cancellation till ${formatDate(freePolicy?.endDate)}`;
            } else if (hasPartialRefund) {
              overallCode = 'PARTIALLY_REFUNDABLE';
              overallData = 'Partial refund available';
            }

            const result = {
              code: overallCode,
              data: overallData,
              policy: transformedPolicies,
              remarks: {
                title: 'Cancellation Policy',
                text: null,
                subText: overallData
              }
            };

            console.log('🔄 Final Cancellation Benefits Result:', result);
            return result;
          })(),

          // Default values
          travellerDetails: { primaryGuestName: null },
          metadata: { text: '' }, freeCancellationDate: '', bwzPaymentDueDate: '', paymentOptions: [],
          providerId: parseInt(selectedRoom.rate.providerId?.toString() || '0'), hotelInfoToken: '', accommodationType: '',
          rateChange: { text: '', amount: 0 }, bestPriceGuarantee: false
        };

        setHotelBookingDetails(combinedBookingData);

        // 🔍 DETAILED COMPONENT DATA LOGGING
        console.log("✅ Real booking data loaded successfully");
        console.log("🏨 Hotel Details Component Data:", {
          name: combinedBookingData.name,
          locality: combinedBookingData.locality,
          starRating: combinedBookingData.starRating,
          thumbnailImageURL: combinedBookingData.thumbnailImageURL
        });

        console.log("📅 Travel Details Component Data:", combinedBookingData.travelDetails);

        console.log("🛏️ Room Details Component Data:", combinedBookingData.roomDetail);

        console.log("🎁 Room Benefits Component Data:", combinedBookingData.roomBenefits);

        console.log("❌ Cancellation Benefits Component Data:", combinedBookingData.cancellationBenefits);

        console.log("💰 Pricing Component Data:", {
          fareDetail: combinedBookingData.fareDetail,
          totalBookingAmount: combinedBookingData.fareDetail?.totalBookingAmount,
          baseFare: combinedBookingData.fareDetail?.baseFare,
          taxesAndFees: combinedBookingData.fareDetail?.taxesAndFees,
          totalDiscount: combinedBookingData.fareDetail?.totalDiscount,
          convenienceFee: combinedBookingData.fareDetail?.convenienceFee,
          totalPrice: combinedBookingData.fareDetail?.totalPrice
        });

        console.log("👤 Traveller Details Component Data:", combinedBookingData.travellerDetails);

        console.log("📋 Complete Combined Booking Data:", combinedBookingData);

      } else {
        // No real data available - leave hotelBookingDetails undefined
        // Components will show loading states instead of fake data
        console.log("⚠️ No real booking data found, components will show loading states");
        setHotelBookingDetails(undefined);
      }

    } catch (err) {
      console.error("❌ Error loading booking details:", err);
      // Leave hotelBookingDetails undefined so components show loading states
      console.log("⚠️ Error occurred, components will show loading states instead of fake data");
      setHotelBookingDetails(undefined);
    } finally {
      setIsLoading(false);
    }

  }, [hotelDetailsResponse, hotelSearchFormData, setIsLoading])

  useEffect(() => {
    const loadBookingData = async () => {
      try {
        // Initialize booking first
        await initializeBooking();

        // Then load booking details
        await getHotelBookingDetails();
      } catch (error) {
        console.error('❌ Error in booking data loading sequence:', error);
        // Continue with booking details even if initialization fails
        await getHotelBookingDetails();
      }
    };

    loadBookingData();
  }, [getHotelBookingDetails, initializeBooking])


  useScrollLock(isMobileBenfitsActive);

  // Handle Pay Now button click with validation and booking API call
  const handlePayNowClick = async () => {
    if (guestDetailsFormRef.current) {
      const isValid = guestDetailsFormRef.current.validateForm();

      if (isValid) {
        try {
          // Get form data
          const formData = guestDetailsFormRef.current.getFormData();
          console.log("Form is valid, proceeding to booking:", formData);

          // 🔍 DATA AVAILABILITY CHECK
          // We only check for booking init API data because:
          // 1. This is the primary data source used for actual booking creation
          // 2. localStorage data (selectedRoom, hotelDetails) is not used in final API payload
          // 3. Form data validation is handled separately by the form component
          const dataValidation = validateBookingDataAvailability();
          if (!dataValidation.isValid) {
            console.error("❌ Missing required booking init API data:", dataValidation.missingData);
            setBookingError(`Missing required data: ${dataValidation.missingData.join(", ")}. Please refresh the page to reinitialize your booking session.`);
            return;
          }

          console.log("✅ All required booking init API data is available");

          // Check if booking initialization data is valid
          if (!isBookingInitDataValid(bookingInitData)) {
            console.warn("⚠️ No valid booking initialization data available");
            setBookingError("Please refresh the page to reinitialize your booking session");
            return;
          }

          console.log("✅ Booking initialization data is valid and ready for use");

          // Transform form data to API payload
          const bookingPayload = transformToBookingRequest(
            formData,
            "search_221688_20250915_114614",  //searchKey
            "65632409",                                 //hotelDetailsResponse?.hotel_id
            123 // Default user ID - you might want to get this from auth context
          );

          console.log("📋 Transformed booking payload:", bookingPayload);

          // Validate the payload
          // if (!validateBookingRequest(bookingPayload)) {
          //   console.error("❌ Invalid booking payload");
          //   setBookingError("Invalid booking data. Please check your details and try again.");
          //   return;
          // }

          // Set loading state
          setIsBookingLoading(true);
          setBookingError(null);

          // Call the booking API
          const bookingResponse = await createHotelBooking(bookingPayload);

          // Set success state
          setBookingSuccess(true);

          // Store booking response for the next page
          localStorage.setItem('bookingResponse', JSON.stringify(bookingResponse));

          // Navigate to booking confirmation with booking reference as search param
          const bookingReference = bookingResponse.booking_reference;
          if (bookingReference) {
            navigation.push(`/BookingConfirmation?bookingReference=${bookingReference}`);
          } else {
            // Fallback to basic URL if no booking reference
            navigation.push('/BookingConfirmation');
          }

        } catch (error) {

          
          console.error("❌ Booking creation failed:", error);
          
          // Handle different types of errors
          let errorMessage = "Failed to create booking. Please try again.";
          
          if (error instanceof Error) {
            errorMessage = error.message;
          } else if (typeof error === 'object' && error !== null) {
            // Handle API error response
            const apiError = error as any;
            if (apiError.response?.data?.message) {
              errorMessage = apiError.response.data.message;
              setBookingError(errorMessage);
            } else if (apiError.message) {
              errorMessage = apiError.message;
            }
          }
          
          setBookingError(errorMessage);
        } finally {
          setIsBookingLoading(false);
        }
      } else {
        console.log("Form validation failed");
        // Scroll to the first error (optional)
        const firstErrorElement = document.querySelector('.error-message');
        if (firstErrorElement) {
          firstErrorElement.scrollIntoView({ behavior: 'smooth', block: 'center' });
        }
      }
    }
  };

  if (isMobile) {
    return (
      <div className="booking-preview-page">
        {/* Mobile Header - Outside container for proper sticky behavior */}
        {!isLoading && hotelBookingDetails && (
          <MobileBookingHeader
            name={hotelBookingDetails?.name}
            travelDetails={hotelBookingDetails?.travelDetails}
          />
        )}

        <div className="booking-preview-container common-container mobile-order" style={{
          paddingTop: bookingInitError ? '80px' : '0',
          position: 'relative'
        }}>
          {/* Booking initialization error display */}
          {bookingInitError && (
            <div className="booking-init-error" style={{
              position: 'absolute',
              top: '0',
              left: '0',
              right: '0',
              backgroundColor: '#fee2e2',
              border: '1px solid #fecaca',
              borderRadius: '8px',
              padding: '12px 16px',
              marginBottom: '20px',
              color: '#dc2626',
              zIndex: 1000,
              boxShadow: '0 2px 8px rgba(0,0,0,0.1)'
            }}>
              <p><strong>Booking Initialization Error:</strong> {bookingInitError}</p>
              <p style={{ fontSize: '14px', marginTop: '8px' }}>
                Please refresh the page or contact support if the issue persists.
              </p>
            </div>
          )}

          {/* Data update notification */}
          {dataUpdatedNotification && (
            <div className="data-update-notification" style={{
              backgroundColor: '#dbeafe',
              border: '1px solid #93c5fd',
              borderRadius: '8px',
              padding: '12px 16px',
              marginBottom: '20px',
              color: '#1e40af'
            }}>
              <p><strong>📋 Booking Data Updated:</strong> {dataUpdatedNotification}</p>
              <p style={{ fontSize: '14px', marginTop: '8px' }}>
                Your booking details have been updated with the latest information.
              </p>
            </div>
          )}

          {bookingError && (
            <div className="booking-error" style={{
              backgroundColor: '#fee2e2',
              border: '1px solid #fecaca',
              borderRadius: '8px',
              padding: '16px',
              marginBottom: '20px',
              color: '#991b1b'
            }}>
              <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start' }}>
                <div style={{ flex: 1 }}>
                  <h4 style={{ margin: '0 0 8px 0', fontSize: '16px', fontWeight: '600' }}>
                    ❌ Booking Error
                  </h4>
                  <p style={{ margin: '0 0 12px 0', fontSize: '14px' }}>
                    {bookingError}
                  </p>
                  {bookingError.includes('Missing required data') && (
                    <div style={{ 
                      backgroundColor: '#fef3c7', 
                      border: '1px solid #fde68a', 
                      borderRadius: '6px', 
                      padding: '12px',
                      fontSize: '13px'
                    }}>
                      <p style={{ margin: '0 0 8px 0', fontWeight: '500' }}>
                        💡 How to fix this:
                      </p>
                      <ol style={{ margin: '0', paddingLeft: '20px' }}>
                        <li>Refresh the page to reinitialize your booking session</li>
                        <li>Make sure you're starting from the hotel details page</li>
                        <li>Check your internet connection</li>
                        <li>Try selecting the room again</li>
                      </ol>
                    </div>
                  )}
                </div>
                <button
                  onClick={() => setBookingError(null)}
                  style={{
                    background: 'none',
                    border: 'none',
                    fontSize: '20px',
                    cursor: 'pointer',
                    color: '#991b1b',
                    padding: '4px',
                    marginLeft: '12px'
                  }}
                >
                  ×
                </button>
              </div>
            </div>
          )}

          {isLoading ? (
            <>
              <PropertyDetailsShimmer />
              <OffersSectonShimmr />
              <FareSummaryShimmer />
              <GuestDetailsFormShimmer />
            </>
          ) : (
            <>
              {!hotelBookingDetails ? (
                <PropertyDetailsShimmer />
              ) : (
                <PropertyDetails setIsMobileBenefitsActive={setIsMobileBenefitsActive} thumbnailImageURL={hotelBookingDetails?.thumbnailImageURL} name={hotelBookingDetails?.name} address={hotelBookingDetails?.address} starRating={hotelBookingDetails?.starRating} travelDetails={hotelBookingDetails?.travelDetails} roomDetail={hotelBookingDetails?.roomDetail} roomBenefits={hotelBookingDetails?.roomBenefits} />
              )}

              {isBookingInitLoading || !bookingInitData ? (
                <CancellationPolicyShimmer />
              ) : (
                <CancellationPolicy
                  cancellationPolicies={bookingInitData.data.hotel.rates[0].cancellationPolicies}
                  refundable={bookingInitData.data.hotel.rates[0].refundable}
                  totalRate={bookingInitData.data.hotel.rates[0].totalRate}
                  currency={bookingInitData.data.currency}
                />
              )}

              <OffersSection />
              {isBookingInitLoading || !bookingInitData ? (
                <FareSummaryShimmer />
              ) : (
                <FareSummary
                  bookingRate={bookingInitData.data.hotel.rates[0]}
                  roomCount={hotelSearchFormData?.roomsData?.length || 1}
                  nightCount={calculateNights(hotelSearchFormData?.checkInDate, hotelSearchFormData?.checkOutDate)}
                  currency={bookingInitData.data.currency}
                />
              )}
              {isBookingInitLoading || !bookingInitData ? (
                <CancellationPolicyShimmer />
              ) : (
                <CancellationPolicy
                  cancellationPolicies={bookingInitData.data.hotel.rates[0].cancellationPolicies}
                  refundable={bookingInitData.data.hotel.rates[0].refundable}
                  totalRate={bookingInitData.data.hotel.rates[0].totalRate}
                  currency={bookingInitData.data.currency}
                />
              )}
              <GuestDetailsForm ref={guestDetailsFormRef} guestCount={totalGuestsCount} allGuestsInfoRequired={isAllGuestsInfoRequired} isPassportMandatory={isPassportMandatory} />
              <SpecialRequest />
              <div className="payment-disclaimer-section">
                <p>
                  By clicking on Pay Now/Book Now, I confirm that I have read,
                  understood, and agree with the{" "}
                  <Link href={"#"}>Cancellation Policy</Link>,{" "}
                  <Link href={"#"}>Privacy Policy</Link> and{" "}
                  <Link href={"#"}>User Agreement</Link>.
                </p>
                <p>
                  Please note that KindAli will not provide a tax invoice. You
                  will be given a commercial receipt to serve as proof of
                  transaction.
                </p>
                <div className="bg-gray-100 p-4 rounded-lg shadow-sm">
                  <p className="text-gray-700 text-center font-medium mb-3 flex items-center justify-center gap-2">
                    <ShieldCheck className="h-5 w-5 text-green-600" />
                    100% Safe Payment Process
                  </p>
                  <div className="flex justify-center items-center gap-4">
                    {/* Payment method logos */}
                    <div className="bg-white p-2 rounded-md shadow-sm">
                      <Image width={60} height={40} src={visa.src} alt="Visa" className="h-8 w-auto" />
                    </div>
                    <div className="bg-white p-2 rounded-md shadow-sm">
                      <Image width={60} height={40} src={master.src} alt="Mastercard" className="h-8 w-auto" />
                    </div>
                    <div className="bg-white p-2 rounded-md shadow-sm">
                      <Image width={60} height={40} src={paypal.src} alt="PayPal" className="h-8 w-auto" />
                    </div>
                  </div>
                  <p className="text-xs text-gray-500 text-center mt-2">
                    All payment methods are secure and encrypted
                  </p>
                </div>
              </div>
              <div className="booking-preview-footer-bottom-0">
                <div className="totalAmtLabelValue">
                  {(() => {
                    const totalAmount = getTotalAmount();
                    return (
                      <p className="value" style={{
                        opacity: totalAmount.isLoading ? 0.6 : 1,
                        transition: 'opacity 0.3s ease'
                      }}>
                        {totalAmount.isLoading && (
                          <i className="fa-solid fa-spinner fa-spin" style={{ marginRight: '4px', fontSize: '14px' }}></i>
                        )}
                        {totalAmount.symbol}{totalAmount.mainAmount}.<span className="xs-text">
                        {totalAmount.decimal}
                        </span>
                      </p>
                    );
                  })()}

                  <span className="label">Total amount</span>
                </div>
                <button 
                  className="footer-btn2" 
                  onClick={handlePayNowClick}
                  disabled={isBookingLoading}
                  style={{
                    opacity: isBookingLoading ? 0.6 : 1,
                    cursor: isBookingLoading ? 'not-allowed' : 'pointer'
                  }}
                >
                  {isBookingLoading ? (
                    <>
                      <i className="fa-solid fa-spinner fa-spin" style={{ marginRight: '8px' }}></i>
                      Creating...
                    </>
                  ) : (
                    'Pay Now'
                  )}
                </button>
              </div>
            </>
          )}
        </div>


        <div className={`mobile-bottom-to-top-modal-container ${isMobileBenfitsActive ? 'active' : ''}`}>

               <div className="mobile-bottom-to-top-modal-header">
          <div
            className="mobile-bottom-to-top-modal-header__close-bttn"
            onClick={() => {
              setIsMobileBenefitsActive(false);
            }}
          >
            <i className="fa-solid fa-xmark"></i>
          </div>
              <h3 className="mobile-bottom-to-top-modal-heading">Room Benefits:</h3>
        </div>



     <div className="mobile-bottom-to-top-content">

     <div className="room-benefits-mobile-container " style={{ borderTop: '1px solid rgba(0, 0, 0, 0.1)'}}>
       <div className="mobile-room-benefits">
         <div className="mobile-room-benefits__list">
           {hotelBookingDetails?.roomBenefits ? (
             <>
               {/* Render meal benefits if they exist */}
               {hotelBookingDetails.roomBenefits.mealBenefits && hotelBookingDetails.roomBenefits.mealBenefits.map((benefit: string, index: number) => (
                 <div key={`meal-${index}`} className="list-item">
                   <i className="fa-solid fa-check"></i>
                   <span className="label">{benefit}</span>
                 </div>
               ))}

               {/* Render other benefits */}
               {hotelBookingDetails.roomBenefits.otherBenefits && hotelBookingDetails.roomBenefits.otherBenefits.map((benefit: string, index: number) => (
                 <div key={`other-${index}`} className="list-item">
                   <i className="fa-solid fa-check"></i>
                   <span className="label">{benefit}</span>
                 </div>
               ))}
             </>
           ) : (
             // Show loading message instead of fake benefits
             <div style={{ padding: '20px', textAlign: 'center', color: '#666' }}>
               <i className="fa-solid fa-spinner fa-spin" style={{ marginRight: '8px' }}></i>
               Loading room benefits...
             </div>
           )}
         </div>
       </div>


                 </div>

     </div>






 </div>

      </div>
    );
  }

  // Default layout for desktop/tablet
  return (
    <div className="booking-preview-page">
      <div className="booking-preview-container common-container" style={{
        paddingTop: bookingInitError ? '80px' : '0',
        position: 'relative'
      }}>
        {/* Booking initialization error display */}
        {bookingInitError && (
          <div className="booking-init-error" style={{
            position: 'absolute',
            top: '0',
            left: '0',
            right: '0',
            backgroundColor: '#fee2e2',
            border: '1px solid #fecaca',
            borderRadius: '8px',
            padding: '12px 16px',
            marginBottom: '20px',
            color: '#dc2626',
            zIndex: 240,
            boxShadow: '0 2px 8px rgba(0,0,0,0.1)'
          }}>
            <p><strong>Booking Initialization Error:</strong> {bookingInitError}</p>
            <p style={{ fontSize: '14px', marginTop: '8px' }}>
              Please refresh the page or contact support if the issue persists.
            </p>
          </div>
        )}

        {/* Data update notification */}
        {dataUpdatedNotification && (
          <div className="data-update-notification" style={{
            backgroundColor: '#dbeafe',
            border: '1px solid #93c5fd',
            borderRadius: '8px',
            padding: '12px 16px',
            marginBottom: '20px',
            color: '#1e40af',
            gridColumn: '1 / -1'
          }}>
            <p><strong>📋 Booking Data Updated:</strong> {dataUpdatedNotification}</p>
            <p style={{ fontSize: '14px', marginTop: '8px' }}>
              Your booking details have been updated with the latest information.
            </p>
          </div>
        )}

        {/* Booking error display */}
        {bookingError && (
          <div className="booking-error" style={{
            backgroundColor: '#fee2e2',
            border: '1px solid #fecaca',
            borderRadius: '8px',
            padding: '16px',
            marginBottom: '20px',
            color: '#991b1b'
          }}>
            <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start' }}>
              <div style={{ flex: 1 }}>
                <h4 style={{ margin: '0 0 8px 0', fontSize: '16px', fontWeight: '600' }}>
                  ❌ Booking Error
                </h4>
                <p style={{ margin: '0 0 12px 0', fontSize: '14px' }}>
                  {bookingError}
                </p>
                {bookingError.includes('Missing required data') && (
                  <div style={{ 
                    backgroundColor: '#fef3c7', 
                    border: '1px solid #fde68a', 
                    borderRadius: '6px', 
                    padding: '12px',
                    fontSize: '13px'
                  }}>
                    <p style={{ margin: '0 0 8px 0', fontWeight: '500' }}>
                      💡 How to fix this:
                    </p>
                    <ol style={{ margin: '0', paddingLeft: '20px' }}>
                      <li>Refresh the page to reinitialize your booking session</li>
                      <li>Make sure you're starting from the hotel details page</li>
                      <li>Check your internet connection</li>
                      <li>Try selecting the room again</li>
                    </ol>
                  </div>
                )}
              </div>
              <button
                onClick={() => setBookingError(null)}
                style={{
                  background: 'none',
                  border: 'none',
                  fontSize: '20px',
                  cursor: 'pointer',
                  color: '#991b1b',
                  padding: '4px',
                  marginLeft: '12px'
                }}
              >
                ×
              </button>
            </div>
          </div>
        )}

        <div className="left-section">
          {isLoading ? (
            <>
              <GuestDetailsFormShimmer />
               <PropertyDetailsShimmer />
            </>
          ) : (
            <>
              {!hotelBookingDetails ? (
                <PropertyDetailsShimmer />
              ) : (
                <PropertyDetails thumbnailImageURL={hotelBookingDetails?.thumbnailImageURL} name={hotelBookingDetails?.name} address={hotelBookingDetails?.address} starRating={hotelBookingDetails?.starRating} travelDetails={hotelBookingDetails?.travelDetails} roomDetail={hotelBookingDetails?.roomDetail} roomBenefits={hotelBookingDetails?.roomBenefits} />
              )}
              {isBookingInitLoading || !bookingInitData ? (
                <CancellationPolicyShimmer />
              ) : (
                <CancellationPolicy
                  cancellationPolicies={bookingInitData.data.hotel.rates[0].cancellationPolicies}
                  refundable={bookingInitData.data.hotel.rates[0].refundable}
                  totalRate={bookingInitData.data.hotel.rates[0].totalRate}
                  currency={bookingInitData.data.currency}
                />
              )}
                            <GuestDetailsForm ref={guestDetailsFormRef} guestCount={totalGuestsCount} allGuestsInfoRequired={isAllGuestsInfoRequired} isPassportMandatory={isPassportMandatory} />

              <SpecialRequest />
              <div className="booking-preview-footer">
                <button 
                  className="footer-btn" 
                  onClick={handlePayNowClick}
                  disabled={isBookingLoading}
                  style={{
                    opacity: isBookingLoading ? 0.6 : 1,
                    cursor: isBookingLoading ? 'not-allowed' : 'pointer'
                  }}
                >
                  {isBookingLoading ? (
                    <>
                      <i className="fa-solid fa-spinner fa-spin" style={{ marginRight: '8px' }}></i>
                      Creating Booking...
                    </>
                  ) : (
                    'Pay Now'
                  )}
                </button>
              </div>
            </>
          )}
        </div>

        <div className="right-section">
          {isLoading ? (
            <>
              <FareSummaryShimmer /> <OffersSectonShimmr />
            </>
          ) : (
            <>
              {isBookingInitLoading || !bookingInitData ? (
                <FareSummaryShimmer />
              ) : (
                <FareSummary
                  bookingRate={bookingInitData.data.hotel.rates[0]}
                  roomCount={hotelSearchFormData?.roomsData?.length || 1}
                  nightCount={calculateNights(hotelSearchFormData?.checkInDate, hotelSearchFormData?.checkOutDate)}
                  currency={bookingInitData.data.currency}
                />
              )}
              <OffersSection />
              <div className="payment-disclaimer-section">
                <p>
                  By clicking on Pay Now/Book Now, I confirm that I have read,
                  understood, and agree with the{" "}
                  <Link href={"#"}>Cancellation Policy</Link>,{" "}
                  <Link href={"#"}>Privacy Policy</Link> and{" "}
                  <Link href={"#"}>User Agreement</Link>.
                </p>
                <p>
                  Please note that KindAli will not provide a tax invoice. You
                  will be given a commercial receipt to serve as proof of
                  transaction.
                </p>
                <div className="bg-gray-100 p-4 rounded-lg shadow-sm">
                  <p className="text-gray-700 text-center font-medium mb-3 flex items-center justify-center gap-2">
                    <ShieldCheck className="h-5 w-5 text-green-600" />
                    100% Safe Payment Process
                  </p>
                  <div className="flex justify-center items-center gap-4">
                    {/* Payment method logos */}
                    <div className="bg-white p-2 rounded-md shadow-sm">
                      <Image width={60} height={40} src={visa.src} alt="Visa" className="h-8 w-auto" />
                    </div>
                    <div className="bg-white p-2 rounded-md shadow-sm">
                      <Image width={60} height={40} src={master.src} alt="Mastercard" className="h-8 w-auto" />
                    </div>
                    <div className="bg-white p-2 rounded-md shadow-sm">
                      <Image width={60} height={40} src={paypal.src} alt="PayPal" className="h-8 w-auto" />
                    </div>
                  </div>
                  <p className="text-xs text-gray-500 text-center mt-2">
                    All payment methods are secure and encrypted
                  </p>
                </div>
              </div>

               <div className="booking-preview-footer-bottom-0">
                <div className="totalAmtLabelValue">
                  {(() => {
                    const totalAmount = getTotalAmount();
                    return (
                      <p className="value" style={{
                        opacity: totalAmount.isLoading ? 0.6 : 1,
                        transition: 'opacity 0.3s ease'
                      }}>
                        {totalAmount.isLoading && (
                          <i className="fa-solid fa-spinner fa-spin" style={{ marginRight: '4px', fontSize: '14px' }}></i>
                        )}
                        {totalAmount.symbol}{totalAmount.mainAmount}.<span className="xs-text">
                        {totalAmount.decimal}
                        </span>
                      </p>
                    );
                  })()}

                  <span className="label">Total amount</span>
                </div>
                <button 
                  className="footer-btn2" 
                  onClick={handlePayNowClick}
                  disabled={isBookingLoading}
                  style={{
                    opacity: isBookingLoading ? 0.6 : 1,
                    cursor: isBookingLoading ? 'not-allowed' : 'pointer'
                  }}
                >
                  {isBookingLoading ? (
                    <>
                      <i className="fa-solid fa-spinner fa-spin" style={{ marginRight: '8px' }}></i>
                      Creating...
                    </>
                  ) : (
                    'Pay Now'
                  )}
                </button>
              </div>
            </>
          )}
        </div>
      </div>
    </div>
  );
}

export default Page;

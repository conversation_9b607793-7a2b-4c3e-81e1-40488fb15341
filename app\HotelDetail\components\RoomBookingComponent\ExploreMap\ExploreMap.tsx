'use client';

import React from 'react';
import './ExploreMap.scss';
import MapContainer from '@/components/map/MapContainer';
import { useTranslation } from '@/app/hooks/useTranslation';
import { HotelDetailApiResponse } from '@/app/HotelDetail/hotel-detail-api-response.model';

interface ExploreMapProps {
  hotelData?: HotelDetailApiResponse; // Primary data source
  // Legacy props for backward compatibility
  hotelLocation?: {
    latitude: number;
    longitude: number;
    address: string;
  };
  hotelDetails?: {
    name: string;
    rating?: number;
    price?: string;
  };
}

function ExploreMap({
  hotelData,
  hotelLocation = {
    latitude: 15.2108,
    longitude: 73.9506,
    address: '613/D Near Zuri Resort, Pedda Nomeio, Varca South Goa, 403721'
  },
  hotelDetails = {
    name: 'Zuri White Sands Resort',
    rating: 4.5,
    price: '$120 per night'
  }
}: ExploreMapProps) {
  const { t } = useTranslation();

  // Helper function to calculate average rating
  const calculateAverageRating = (reviews: any[]) => {
    if (!reviews || reviews.length === 0) return 0;
    const sum = reviews.reduce((acc: number, review: any) => acc + review.rating, 0);
    return parseFloat((sum / reviews.length).toFixed(1));
  };

  // Helper function to get coordinates for Dubai landmarks
  const getDubaiLandmarkCoordinates = (attraction: string) => {
    const landmarkCoords: { [key: string]: { latitude: number; longitude: number } } = {
      "Dubai Mall": { latitude: 25.1972, longitude: 55.2796 },
      "Burj Khalifa": { latitude: 25.1972, longitude: 55.2744 },
      "Dubai Marina": { latitude: 25.0657, longitude: 55.1393 },
      "Business Bay": { latitude: 25.1877, longitude: 55.2632 },
      "Dubai Opera": { latitude: 25.1951, longitude: 55.2721 },
      "Dubai Fountain": { latitude: 25.1951, longitude: 55.2744 },
      "DIFC": { latitude: 25.2138, longitude: 55.2796 },
      "Emirates Towers": { latitude: 25.2176, longitude: 55.2797 }
    };

    return landmarkCoords[attraction] || {
      latitude: hotelData?.geoLocationInfo?.lat || 25.1972,
      longitude: hotelData?.geoLocationInfo?.lon || 55.2744
    };
  };

  // Extract live data from backend (with fallbacks to legacy props)
  const liveLocation = hotelData?.geoLocationInfo ? {
    latitude: hotelData.geoLocationInfo.lat,
    longitude: hotelData.geoLocationInfo.lon,
    address: `${hotelData.address}, ${hotelData.city}, ${hotelData.country}`
  } : hotelLocation;

  const liveHotelDetails = hotelData ? {
    name: hotelData.name,
    rating: calculateAverageRating(hotelData.reviews || []),
    price: "Contact for pricing" // Can be updated when pricing data is available
  } : hotelDetails;

  // Get nearby attractions from backend location description
  const locationDescription = hotelData?.descriptions?.find(desc => desc.description_type === "location");
  const nearbyAttractions = locationDescription?.content?.nearby || [];

  // Create points of interest from backend data
  const pointsOfInterest = nearbyAttractions.map((attraction, index) => ({
    id: `poi-${index}`,
    name: attraction,
    location: getDubaiLandmarkCoordinates(attraction),
    type: 'attraction'
  }));

  // Add some additional Dubai points of interest if we have few from backend
  const additionalDubaiPOIs = [
    { name: "Dubai Opera", location: getDubaiLandmarkCoordinates("Dubai Opera") },
    { name: "Dubai Fountain", location: getDubaiLandmarkCoordinates("Dubai Fountain") },
    { name: "DIFC", location: getDubaiLandmarkCoordinates("DIFC") }
  ];

  const allPointsOfInterest = [
    ...pointsOfInterest,
    ...(pointsOfInterest.length < 3 ? additionalDubaiPOIs.slice(0, 3 - pointsOfInterest.length) : [])
  ].map((poi, index) => ({
    id: poi.id || `additional-poi-${index}`,
    name: poi.name,
    location: poi.location,
    type: 'attraction'
  }));

  // Only render map if we have valid coordinates
  const hasValidLocation = liveLocation.latitude !== 0 && liveLocation.longitude !== 0;

  return (
    <div className="explore-map-container">
      {hasValidLocation ? (
        <MapContainer
          center={liveLocation}
          zoom={14}
          height="400px"
          width="100%"
          hotels={[
            {
              id: '1',
              name: liveHotelDetails.name,
              location: liveLocation,
              rating: liveHotelDetails.rating,
              price: liveHotelDetails.price
            }
          ]}
          pointsOfInterest={allPointsOfInterest}
          showHeader={true}
          title={t('Explore the Area')}
          showGoogleMapsLink={true}
          googleMapsLinkText={t('View On Google Maps')}
        />
      ) : (
        <div className="map-placeholder" style={{
          height: '400px',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          backgroundColor: '#f5f5f5',
          borderRadius: '8px',
          color: '#666'
        }}>
          <div style={{ textAlign: 'center' }}>
            <i className="fa-solid fa-map-location-dot" style={{ fontSize: '48px', marginBottom: '16px' }}></i>
            <p>{t('Map will be available when location data is loaded')}</p>
          </div>
        </div>
      )}
    </div>
  );
}

export default ExploreMap;
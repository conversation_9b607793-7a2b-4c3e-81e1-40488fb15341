'use client';

import React, { useState, useEffect } from 'react';
import 'mapbox-gl/dist/mapbox-gl.css';
import './ExploreMap.scss';
import { Map, Marker, Popup, ViewState } from 'react-map-gl/mapbox';
import { MapPin, ExternalLink } from 'lucide-react';
import { useTranslation } from '@/app/hooks/useTranslation';
import { HotelDetailApiResponse } from '@/app/HotelDetail/hotel-detail-api-response.model';

interface ExploreMapProps {
  hotelData?: HotelDetailApiResponse; // Primary data source
  // Legacy props for backward compatibility
  hotelLocation?: {
    latitude: number;
    longitude: number;
    address: string;
  };
  hotelDetails?: {
    name: string;
    rating?: number;
    price?: string;
  };
}

function ExploreMap({
  hotelData,
  hotelLocation = {
    latitude: 15.2108,
    longitude: 73.9506,
    address: '613/D Near Zuri Resort, Pedda Nomeio, Varca South Goa, 403721'
  },

}: ExploreMapProps) {
  const { t } = useTranslation();

  // MapBox viewport state
  const [viewport, setViewport] = useState<Partial<ViewState>>({
    latitude: 15.2108,
    longitude: 73.9506,
    zoom: 14,
  });

  // Selected point of interest for popup
  const [selectedPoi, setSelectedPoi] = useState<{
    name: string;
    location: { latitude: number; longitude: number };
    type: string;
  } | null>(null);

  // Update viewport when hotel data changes
  useEffect(() => {
    if (hotelData?.geoLocationInfo) {
      setViewport({
        latitude: hotelData.geoLocationInfo.lat,
        longitude: hotelData.geoLocationInfo.lon,
        zoom: 14,
      });
    } else if (hotelLocation) {
      setViewport({
        latitude: hotelLocation.latitude,
        longitude: hotelLocation.longitude,
        zoom: 14,
      });
    }
  }, [hotelData, hotelLocation]);



  // Helper function to get coordinates for Dubai landmarks
  const getDubaiLandmarkCoordinates = (attraction: string) => {
    const landmarkCoords: { [key: string]: { latitude: number; longitude: number } } = {
      "Dubai Mall": { latitude: 25.1972, longitude: 55.2796 },
      "Burj Khalifa": { latitude: 25.1972, longitude: 55.2744 },
      "Dubai Marina": { latitude: 25.0657, longitude: 55.1393 },
      "Business Bay": { latitude: 25.1877, longitude: 55.2632 },
      "Dubai Opera": { latitude: 25.1951, longitude: 55.2721 },
      "Dubai Fountain": { latitude: 25.1951, longitude: 55.2744 },
      "DIFC": { latitude: 25.2138, longitude: 55.2796 },
      "Emirates Towers": { latitude: 25.2176, longitude: 55.2797 }
    };

    return landmarkCoords[attraction] || {
      latitude: hotelData?.geoLocationInfo?.lat || 25.1972,
      longitude: hotelData?.geoLocationInfo?.lon || 55.2744
    };
  };

  // Extract live data from backend (with fallbacks to legacy props)
  const liveLocation = hotelData?.geoLocationInfo ? {
    latitude: hotelData.geoLocationInfo.lat,
    longitude: hotelData.geoLocationInfo.lon,
    address: `${hotelData.address}, ${hotelData.city}, ${hotelData.country}`
  } : hotelLocation;



  // Get nearby attractions from backend location description
  const locationDescription = hotelData?.descriptions?.find(desc => desc.description_type === "location");
  const nearbyAttractions = locationDescription?.content?.nearby || [];

  // Create points of interest from backend data
  const pointsOfInterest = nearbyAttractions.map((attraction, index) => ({
    id: `poi-${index}`,
    name: attraction,
    location: getDubaiLandmarkCoordinates(attraction),
    type: 'attraction'
  }));

  // Add some additional Dubai points of interest if we have few from backend
  const additionalDubaiPOIs = [
    { name: "Dubai Opera", location: getDubaiLandmarkCoordinates("Dubai Opera") },
    { name: "Dubai Fountain", location: getDubaiLandmarkCoordinates("Dubai Fountain") },
    { name: "DIFC", location: getDubaiLandmarkCoordinates("DIFC") }
  ];

  const allPointsOfInterest = [
    ...pointsOfInterest,
    ...(pointsOfInterest.length < 3 ? additionalDubaiPOIs.slice(0, 3 - pointsOfInterest.length) : [])
  ].map((poi, index) => ({
    id: `poi-${index}`,
    name: poi.name,
    location: poi.location,
    type: 'attraction'
  }));

  // Only render map if we have valid coordinates
  const hasValidLocation = liveLocation.latitude !== 0 && liveLocation.longitude !== 0;

  // Open Google Maps with the hotel location
  const openGoogleMaps = () => {
    const latitude = liveLocation.latitude;
    const longitude = liveLocation.longitude;
    const googleMapsUrl = `https://www.google.com/maps/search/?api=1&query=${latitude},${longitude}`;
    window.open(googleMapsUrl, '_blank');
  };

  return (
    <div className="explore-map-container">
      {hasValidLocation ? (
        <div className="mapbox-explore-container">
          {/* Header */}
          <div className="map-header">
            <h6>{t('Explore the Area')}</h6>
            <div className="location-details">
              <div className="location">
                <p>
                  <i className="fa-solid fa-location-dot mr-1"></i> {liveLocation.address}
                </p>
              </div>
              <div className="view-map-btn" onClick={openGoogleMaps}>
                <ExternalLink size={16} className="mr-1" />
                {t('View On Google Maps')}
              </div>
            </div>
          </div>

          {/* MapBox Map */}
          <div className="mapbox-container" style={{ height: '400px', borderRadius: '8px', overflow: 'hidden' }}>
            <Map
              mapboxAccessToken={process.env.NEXT_PUBLIC_MAPBOX_GL_ACCESS_TOKEN}
              {...viewport}
              style={{ width: '100%', height: '100%' }}
              mapStyle="mapbox://styles/mapbox/streets-v11"
              onMove={(evt: { viewState: Partial<ViewState> }) => setViewport(evt.viewState)}
            >
              {/* Hotel Marker */}
              <Marker
                latitude={liveLocation.latitude}
                longitude={liveLocation.longitude}
                anchor="bottom"
              >
                <div className="hotel-marker">
                  <MapPin size={32} fill="#D9534F" color="white" />
                </div>
              </Marker>

              {/* Points of Interest Markers */}
              {allPointsOfInterest.map((poi, index) => (
                <Marker
                  key={`poi-${index}`}
                  latitude={poi.location.latitude}
                  longitude={poi.location.longitude}
                  anchor="bottom"
                >
                  <div
                    className="poi-marker"
                    onClick={(e: React.MouseEvent) => {
                      e.stopPropagation();
                      setSelectedPoi(poi);
                    }}
                  >
                    <MapPin size={24} fill="#007bff" color="white" />
                  </div>
                </Marker>
              ))}

              {/* POI Popup */}
              {selectedPoi && (
                <Popup
                  latitude={selectedPoi.location.latitude}
                  longitude={selectedPoi.location.longitude}
                  onClose={() => setSelectedPoi(null)}
                  closeOnClick={false}
                  anchor="left"
                  className="poi-popup"
                >
                  <div className="poi-popup-content">
                    <h4>{selectedPoi.name}</h4>
                    <p className="poi-type">{selectedPoi.type || 'Attraction'}</p>
                  </div>
                </Popup>
              )}
            </Map>
          </div>
        </div>
      ) : (
        <div className="map-placeholder" style={{
          height: '400px',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          backgroundColor: '#f5f5f5',
          borderRadius: '8px',
          color: '#666'
        }}>
          <div style={{ textAlign: 'center' }}>
            <i className="fa-solid fa-map-location-dot" style={{ fontSize: '48px', marginBottom: '16px' }}></i>
            <p>{t('Map will be available when location data is loaded')}</p>
          </div>
        </div>
      )}
    </div>
  );
}

export default ExploreMap;
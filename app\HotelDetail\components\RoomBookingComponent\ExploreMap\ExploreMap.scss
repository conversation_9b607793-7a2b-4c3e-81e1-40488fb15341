@use "/styles/variable" as *;

.explore-map-container {
  padding: 20px 0;

  .mapbox-explore-container {
    .map-header {
      margin-bottom: 16px;

      h6 {
        font-size: 18px;
        font-weight: 600;
        margin-bottom: 12px;
        color: #333;
      }

      .location-details {
        display: flex;
        justify-content: space-between;
        align-items: center;
        flex-wrap: wrap;
        gap: 12px;

        .location {
          p {
            font-size: 14px;
            color: #666;
            margin: 0;
            display: flex;
            align-items: center;

            i {
              margin-right: 8px;
              color: #007bff;
            }
          }
        }

        .view-map-btn {
          display: flex;
          align-items: center;
          padding: 8px 16px;
          background: #007bff;
          color: white;
          border-radius: 6px;
          cursor: pointer;
          font-size: 14px;
          font-weight: 500;
          transition: background-color 0.2s ease;

          &:hover {
            background: #0056b3;
          }

          .mr-1 {
            margin-right: 4px;
          }
        }
      }
    }

    .mapbox-container {
      position: relative;
      border: 1px solid #e0e0e0;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

      .hotel-marker {
        background: #D9534F;
        border: 2px solid #ffffff;
        border-radius: 50%;
        padding: 4px;
        cursor: pointer;
        box-shadow: 0 4px 12px rgba(217, 83, 79, 0.4);
        display: flex;
        align-items: center;
        justify-content: center;
        transition: transform 0.2s ease;

        &:hover {
          transform: scale(1.1);
          box-shadow: 0 6px 16px rgba(217, 83, 79, 0.5);
        }
      }

      .poi-marker {
        background: #007bff;
        border: 2px solid #ffffff;
        border-radius: 50%;
        padding: 2px;
        cursor: pointer;
        box-shadow: 0 2px 8px rgba(0, 123, 255, 0.3);
        display: flex;
        align-items: center;
        justify-content: center;
        transition: transform 0.2s ease;

        &:hover {
          transform: scale(1.1);
          background: #0056b3;
          box-shadow: 0 4px 12px rgba(0, 123, 255, 0.4);
        }
      }

      .poi-popup {
        .poi-popup-content {
          padding: 12px;
          min-width: 150px;

          h4 {
            margin: 0 0 8px 0;
            font-size: 14px;
            font-weight: 600;
            color: #333;
          }

          .poi-type {
            margin: 0;
            font-size: 12px;
            color: #666;
            text-transform: capitalize;
          }
        }
      }
    }
  }

  // Responsive design
  @media (max-width: 768px) {
    .mapbox-explore-container {
      .map-header {
        .location-details {
          flex-direction: column;
          align-items: flex-start;

          .view-map-btn {
            align-self: stretch;
            justify-content: center;
          }
        }
      }
    }
  }

  // Map loading state
  .map-placeholder {
    background: #f8f9fa;
    border: 1px solid #e0e0e0;
    border-radius: 8px;
  }
}

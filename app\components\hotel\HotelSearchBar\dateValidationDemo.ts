/**
 * Date Validation Demo
 * 
 * This file demonstrates the date validation functionality implemented in HotelSearchBar.
 * It shows how dates are validated and corrected when loading from sessionStorage.
 */

// Mock translation function for demo
const mockT = (key: string): string => {
  const translations: Record<string, string> = {
    'search.validation.checkin_past': 'Check-in date cannot be in the past',
    'search.validation.checkout_before_checkin': 'Check-out date must be after check-in date'
  };
  return translations[key] || key;
};

// Date validation function (extracted from HotelSearchBar component)
const validateAndCorrectDates = (
  checkInDate: string | null, 
  checkOutDate: string | null
): { 
  checkInDate: string, 
  checkOutDate: string, 
  hasError: boolean, 
  errorMessage?: string 
} => {
  const today = new Date();
  today.setHours(0, 0, 0, 0);
  
  const tomorrow = new Date(today);
  tomorrow.setDate(today.getDate() + 1);

  // If no dates provided, set default dates
  if (!checkInDate || !checkOutDate) {
    return {
      checkInDate: today.toISOString(),
      checkOutDate: tomorrow.toISOString(),
      hasError: false
    };
  }

  const checkIn = new Date(checkInDate);
  const checkOut = new Date(checkOutDate);
  
  // Set time to midnight for accurate comparison
  checkIn.setHours(0, 0, 0, 0);
  checkOut.setHours(0, 0, 0, 0);

  // Check if check-in date is in the past
  if (checkIn < today) {
    return {
      checkInDate: today.toISOString(),
      checkOutDate: tomorrow.toISOString(),
      hasError: true,
      errorMessage: mockT("search.validation.checkin_past")
    };
  }

  // Check if check-out date is before or same as check-in date
  if (checkOut <= checkIn) {
    const correctedCheckOut = new Date(checkIn);
    correctedCheckOut.setDate(checkIn.getDate() + 1);
    return {
      checkInDate: checkIn.toISOString(),
      checkOutDate: correctedCheckOut.toISOString(),
      hasError: true,
      errorMessage: mockT("search.validation.checkout_before_checkin")
    };
  }

  // Dates are valid
  return {
    checkInDate: checkIn.toISOString(),
    checkOutDate: checkOut.toISOString(),
    hasError: false
  };
};

// Demo scenarios
console.log('=== Date Validation Demo ===\n');

// Scenario 1: Past check-in date
console.log('1. Past check-in date:');
const yesterday = new Date();
yesterday.setDate(yesterday.getDate() - 1);
const tomorrow = new Date();
tomorrow.setDate(tomorrow.getDate() + 1);

const result1 = validateAndCorrectDates(yesterday.toISOString(), tomorrow.toISOString());
console.log('Input:', { checkIn: yesterday.toDateString(), checkOut: tomorrow.toDateString() });
console.log('Output:', { 
  checkIn: new Date(result1.checkInDate).toDateString(), 
  checkOut: new Date(result1.checkOutDate).toDateString(),
  hasError: result1.hasError,
  errorMessage: result1.errorMessage
});
console.log('');

// Scenario 2: Check-out before check-in
console.log('2. Check-out before check-in:');
const dayAfterTomorrow = new Date();
dayAfterTomorrow.setDate(dayAfterTomorrow.getDate() + 2);

const result2 = validateAndCorrectDates(dayAfterTomorrow.toISOString(), tomorrow.toISOString());
console.log('Input:', { checkIn: dayAfterTomorrow.toDateString(), checkOut: tomorrow.toDateString() });
console.log('Output:', { 
  checkIn: new Date(result2.checkInDate).toDateString(), 
  checkOut: new Date(result2.checkOutDate).toDateString(),
  hasError: result2.hasError,
  errorMessage: result2.errorMessage
});
console.log('');

// Scenario 3: Same check-in and check-out dates
console.log('3. Same check-in and check-out dates:');
const futureDate = new Date();
futureDate.setDate(futureDate.getDate() + 5);

const result3 = validateAndCorrectDates(futureDate.toISOString(), futureDate.toISOString());
console.log('Input:', { checkIn: futureDate.toDateString(), checkOut: futureDate.toDateString() });
console.log('Output:', { 
  checkIn: new Date(result3.checkInDate).toDateString(), 
  checkOut: new Date(result3.checkOutDate).toDateString(),
  hasError: result3.hasError,
  errorMessage: result3.errorMessage
});
console.log('');

// Scenario 4: Valid dates
console.log('4. Valid dates:');
const validCheckIn = new Date();
validCheckIn.setDate(validCheckIn.getDate() + 1);
const validCheckOut = new Date();
validCheckOut.setDate(validCheckOut.getDate() + 3);

const result4 = validateAndCorrectDates(validCheckIn.toISOString(), validCheckOut.toISOString());
console.log('Input:', { checkIn: validCheckIn.toDateString(), checkOut: validCheckOut.toDateString() });
console.log('Output:', { 
  checkIn: new Date(result4.checkInDate).toDateString(), 
  checkOut: new Date(result4.checkOutDate).toDateString(),
  hasError: result4.hasError,
  errorMessage: result4.errorMessage
});
console.log('');

// Scenario 5: No dates provided
console.log('5. No dates provided:');
const result5 = validateAndCorrectDates(null, null);
console.log('Input:', { checkIn: null, checkOut: null });
console.log('Output:', { 
  checkIn: new Date(result5.checkInDate).toDateString(), 
  checkOut: new Date(result5.checkOutDate).toDateString(),
  hasError: result5.hasError,
  errorMessage: result5.errorMessage
});

export { validateAndCorrectDates };

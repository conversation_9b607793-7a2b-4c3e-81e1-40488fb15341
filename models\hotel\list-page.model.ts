export interface ImageInfo {
    pictureId: string;
    url: string;
    caption: string;
    imageCategory: string;
    rank: number;
}



export interface RoomInfo {
    type: string;
    bedroom: number;
    livingRoom: number;
    bathroom: number;
    size: string;
    bed: string;
}

export interface BookingInfo {
    nights: number;
    adults: number;
}

export interface Amenity {
    name: string;
    amenityUrl: string;
}

export interface FomoTag {
    data: string;
    fomoType: string;
}

export interface GeoLocation {
    lat: number;
    lon: number;
}

export interface FareDetail {
    displayedBaseFare?: number;
    totalPrice: number;
    board_basis?: string;
    currency_code?: string;
    displayedBaseRate?: number;
    offer_description?: string | null;
    offer_title?: string | null;
    refundable?: boolean;
    taxes?: number;
}

export interface Offer {
    couponCode: string;
    instantDiscount: number;
    displayedInstantDiscount: number;
    cashback: number;
    displayedCashback: number;
    applyMessage: string;
}

export interface Hotel {
    specialAmenities: string[];
    locationId: number;
    hotelId: number;
    id: number;
    offer: Offer;
    name: string;
    address: string;
    city: string;
    userRating: string;
    userRatingCategory: string;
    starRating: number;
    userRatingCount: number;
    imageInfoList: ImageInfo[];
    roomDetails: RoomInfo[];
    comfortRating: number;
    taxesAndCharges: number;
    category: string;
    hotelType: string;
    accommodationType: string;
    topOfferings: string[];
    roomsCountLeft: number;
    distanceFromSearchedEntity: string;
    fomoTags: FomoTag[];
    amenities: Amenity[];
    geoLocationInfo: GeoLocation;
    fareDetail: FareDetail;
    isVisible: boolean;
    about: string;

}

export interface HotelListResponse {
    data: {
        result: {
            inventoryInfoList: Hotel[];
        };
    };
}

// New interfaces for Search Init API Response
export interface HotelAttribute {
    key: string;
    value: string;
}

export interface ApiImage {
  alt_text: string;
  id: number;
  image_category_type: string;
  image_height: number;
  image_path: string;
  image_width: number;
  is_hero_image: boolean;
  sort_order: number;
}

export interface SearchInitHotel {
    specialAmenities: string[];
    HotelType: string;
    about: string | null;
    address: string;
    amenities: string[];
    attributes: HotelAttribute[];
    category: string;
    city: string;
    comfortRating: number | null;
    country: string;
    fareDetail: FareDetail[];
    geoLocationInfo: GeoLocation;
    heroImage: string;
    hotelId: string;
    id: number;
    isVisible: boolean;
    name: string;
    images: ApiImage[];
    roomCountLeft: number | null;
    roomDetails: {
        bathroom: string | null;
        bed: string | null;
        bedroom: string | null;
        livingRoom: string | null;
        size: string | null;
        type: string | null;
    };
    starRating: string;
    stateName: string;
    userRating: number;
    userRatingCategoty: string | null;
}

export interface BatchInfo {
    batch_number: number;
    batch_size: number;
    batch_status: string;
    is_last_batch: boolean;
    total_batches: number;
}

export interface SearchParameters {
    boundaries: Array<Array<{lat: number; long: number}>>;
    coordinates: {
        lat: number;
        long: number;
    };
    radius_degrees: number | null;
    search_type: string;
}

export interface HotelBatch {
    batch_info: BatchInfo;
    hotels: SearchInitHotel[];
    hotels_in_batch: number;
    is_pool_completed: boolean;
    location_id: string;
    search_parameters: SearchParameters;
    status: string;
    total_hotels_found: number;
}

export interface SearchResultBatch {
  _id: string;
  batch_info: BatchInfo;
  hotels: SearchInitHotel[];
  hotels_in_batch: number;
  is_pool_completed: boolean;
  location_id: string;
  search_parameters: SearchParameters; // You can define this more strictly if needed
  status: string;
  timestamp: string;
  total_hotels_found: number;
  type: string;
}

export interface SearchInitApiResponse {
  isCompleted: boolean;
  results: SearchResultBatch[];
  searchKey: string;
  total_results: number;
}

// Legacy interface for backward compatibility
export interface SearchLocationInfo {
    batch_info: BatchInfo;
    hotels: SearchInitHotel[];
    coordinates: {
        lat: number;
        long: number;
    };
    radius_degrees: number | null;
    search_type: string;
    status: string;
    total_hotels: number;
    total_hotels_found: number;
}

"use client";
import React, { useState, useEffect } from 'react';
import { createPortal } from 'react-dom';
import { GroupedRate } from '@/models/hotel/rooms-and-rates.model';

interface RoomDetailsModalProps {
  isOpen: boolean;
  onClose: () => void;
  roomData: GroupedRate | null;
}

function RoomDetailsModal({ isOpen, onClose, roomData }: RoomDetailsModalProps) {
  const [currentImageIndex, setCurrentImageIndex] = useState(0);

  // Disable body scroll when modal is open
  useEffect(() => {
    if (isOpen) {
      document.body.style.overflow = 'hidden';
    } else {
      document.body.style.overflow = 'unset';
    }

    // Cleanup on unmount
    return () => {
      document.body.style.overflow = 'unset';
    };
  }, [isOpen]);

  if (!isOpen || !roomData) return null;

  const room = roomData.room;
  const rate = roomData.rate;
  const images = room.images || [];
  const hasImages = images.length > 0 && images[0]?.links && images[0].links.length > 0;

  // Helper function to check if facility exists
  const hasFacility = (facilityName: string) => {
    return room.facilities.some(facility =>
      facility.name.toLowerCase().includes(facilityName.toLowerCase())
    );
  };

  // Helper function to get occupancy info
  const getOccupancyInfo = () => {
    if (rate.occupancies && rate.occupancies.length > 0) {
      const occupancy = rate.occupancies[0]; // Use first occupancy
      return {
        adults: parseInt(occupancy.numOfAdults),
        children: parseInt(occupancy.numOfChildren)
      };
    }
    return { adults: 2, children: 0 }; // Default fallback
  };

  const occupancyInfo = getOccupancyInfo();

  const nextImage = () => {
    if (hasImages) {
      setCurrentImageIndex((prev) => (prev + 1) % images[0].links!.length);
    }
  };

  const prevImage = () => {
    if (hasImages) {
      setCurrentImageIndex((prev) => (prev - 1 + images[0].links!.length) % images[0].links!.length);
    }
  };

  const goToImage = (index: number) => {
    setCurrentImageIndex(index);
  };

  // Helper function to clean up HTML description
  const cleanDescription = (description: string) => {
    if (!description) return '';

    // Remove HTML tags and decode entities
    let cleaned = description
      .replace(/<[^>]*>/g, '') // Remove HTML tags
      .replace(/&lt;/g, '<')
      .replace(/&gt;/g, '>')
      .replace(/&amp;/g, '&')
      .replace(/&quot;/g, '"')
      .replace(/&#39;/g, "'")
      .replace(/&nbsp;/g, ' ')
      .replace(/\s+/g, ' ') // Replace multiple spaces with single space
      .trim();

    // Limit description length to prevent very long text
    if (cleaned.length > 500) {
      cleaned = cleaned.substring(0, 500) + '...';
    }

    return cleaned;
  };

  // Helper function to format text with proper capitalization
  const formatText = (text: string) => {
    if (!text) return '';

    return text
      .toLowerCase()
      .split(/[\s-_]+/) // Split on spaces, hyphens, underscores
      .map(word => word.charAt(0).toUpperCase() + word.slice(1))
      .join(' ');
  };

  // Debug logging
  console.log('🏨 Room Data:', room);
  console.log('📝 Room Description:', room.description);
  console.log('🧹 Cleaned Description:', cleanDescription(room.description));

  return (
    <div
      className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-[9999] p-4"
      onClick={onClose}
      style={{ top: 0, left: 0, right: 0, bottom: 0 }}
    >
      <div
        className="bg-white rounded-lg max-w-4xl w-full max-h-[90vh] flex flex-col"
        onClick={(e) => e.stopPropagation()}
      >
        {/* Fixed Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200 flex-shrink-0">
          <h2 className="text-2xl font-bold text-primary-color">{room.name}</h2>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600 text-2xl font-bold"
          >
            ×
          </button>
        </div>

        {/* Scrollable Content */}
        <div className="flex-1 overflow-y-auto hide-scrollbar">
          <div className="flex flex-col lg:flex-row">
          {/* Left Side - Image Gallery */}
          <div className="lg:w-1/2 p-6">
            {hasImages ? (
              <div className="space-y-4">
                {/* Main Image */}
                <div className="relative">
                  <img
                    src={images[0].links![currentImageIndex].url}
                    alt={room.name}
                    className="w-full h-64 object-cover rounded-lg"
                  />
                  
                  {/* Navigation Arrows */}
                  {images[0].links!.length > 1 && (
                    <>
                      <button
                        onClick={prevImage}
                        className="absolute left-2 top-1/2 transform -translate-y-1/2 bg-white bg-opacity-80 hover:bg-opacity-100 rounded-full p-2 shadow-md"
                      >
                        <svg className="w-5 h-5 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
                        </svg>
                      </button>
                      <button
                        onClick={nextImage}
                        className="absolute right-2 top-1/2 transform -translate-y-1/2 bg-white bg-opacity-80 hover:bg-opacity-100 rounded-full p-2 shadow-md"
                      >
                        <svg className="w-5 h-5 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                        </svg>
                      </button>
                    </>
                  )}

                  {/* Image Dots */}
                  {images[0].links!.length > 1 && (
                    <div className="absolute bottom-4 left-1/2 transform -translate-x-1/2 flex space-x-2">
                      {images[0].links!.map((_, index) => (
                        <button
                          key={index}
                          onClick={() => goToImage(index)}
                          className={`w-2 h-2 rounded-full ${
                            index === currentImageIndex ? 'bg-white' : 'bg-white bg-opacity-50'
                          }`}
                        />
                      ))}
                    </div>
                  )}
                </div>

                {/* Thumbnail Gallery */}
                {images[0].links!.length > 1 && (
                  <div className="grid grid-cols-6 gap-2">
                    {images[0].links!.slice(0, 6).map((link, index) => (
                      <button
                        key={index}
                        onClick={() => goToImage(index)}
                        className={`relative overflow-hidden rounded border-2 ${
                          index === currentImageIndex ? 'border-primary-color' : 'border-gray-200'
                        }`}
                      >
                        <img
                          src={link.url}
                          alt={`${room.name} ${index + 1}`}
                          className="w-full h-12 object-cover"
                        />
                      </button>
                    ))}
                  </div>
                )}
              </div>
            ) : (
              <div className="w-full h-64 bg-gray-200 rounded-lg flex items-center justify-center">
                <span className="text-gray-400">No images available</span>
              </div>
            )}
          </div>

          {/* Right Side - Room Details */}
          <div className="lg:w-1/2 p-6 space-y-6">
            {/* Room Amenities - Dynamic based on facilities */}
            <div className="flex flex-wrap items-center gap-4 text-sm text-gray-600">
              {/* Air conditioning */}
              {hasFacility('air conditioning') && (
                <div className="flex items-center gap-1">
                  <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                    <path d="M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zM3 10a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H4a1 1 0 01-1-1v-6zM14 9a1 1 0 00-1 1v6a1 1 0 001 1h2a1 1 0 001-1v-6a1 1 0 00-1-1h-2z" />
                  </svg>
                  <span>Air conditioning</span>
                </div>
              )}

              {/* Private bathroom */}
              {hasFacility('bathroom') && (
                <div className="flex items-center gap-1">
                  <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                    <path d="M10.394 2.08a1 1 0 00-.788 0l-7 3a1 1 0 000 1.84L5.25 8.051a.999.999 0 01.356-.257l4-1.714a1 1 0 11.788 1.838L7.667 9.088l1.94.831a1 1 0 00.787 0l7-3a1 1 0 000-1.838l-7-3zM3.31 9.397L5 10.12v4.102a8.969 8.969 0 00-1.05-.174 1 1 0 01-.89-.89 11.115 11.115 0 01.25-3.762zM9.3 16.573A9.026 9.026 0 007 14.935v-3.957l1.818.78a3 3 0 002.364 0l5.508-2.361a11.026 11.026 0 01.25 3.762 1 1 0 01-.89.89 8.968 8.968 0 00-5.35 2.524 1 1 0 01-1.4 0zM6 18a1 1 0 001-1v-2.065a8.935 8.935 0 00-2-.712V17a1 1 0 001 1z" />
                  </svg>
                  <span>Private bathroom</span>
                </div>
              )}

              {/* TV */}
              {hasFacility('tv') && (
                <div className="flex items-center gap-1">
                  <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                    <path d="M4 3a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V5a2 2 0 00-2-2H4zm12 12H4l4-8 3 6 2-4 3 6z" />
                  </svg>
                  <span>Flat-screen TV</span>
                </div>
              )}
            </div>

            {/* WiFi - Dynamic */}
            {hasFacility('wifi') && (
              <div className="flex items-center gap-2 text-sm text-gray-600">
                <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M17.778 8.222c-4.296-4.296-11.26-4.296-15.556 0A1 1 0 01.808 6.808c5.076-5.077 13.308-5.077 18.384 0a1 1 0 01-1.414 1.414zM14.95 11.05a7 7 0 00-9.9 0 1 1 0 01-1.414-1.414 9 9 0 0112.728 0 1 1 0 01-1.414 1.414zM12.12 13.88a3 3 0 00-4.242 0 1 1 0 01-1.415-1.415 5 5 0 017.072 0 1 1 0 01-1.415 1.415zM9 16a1 1 0 011-1h.01a1 1 0 110 2H10a1 1 0 01-1-1z" clipRule="evenodd" />
                </svg>
                <span>Free WiFi</span>
              </div>
            )}

            {/* Room Size */}
            <div className="space-y-2">
              <h3 className="font-semibold text-gray-800">Room size</h3>
              <p className="text-sm text-gray-600">
                {/* Try to extract size from description or use a default */}
                {room.description && room.description.includes('m²')
                  ? room.description.match(/\d+\s*m²/)?.[0] || '25 m²'
                  : '25 m²'
                }
              </p>
            </div>

            {/* Bed Info - Dynamic */}
            <div className="space-y-2">
              <div className="flex items-center gap-2">
                <svg className="w-4 h-4 text-gray-600" fill="currentColor" viewBox="0 0 20 20">
                  <path d="M4 4a2 2 0 00-2 2v8a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2H4zm0 2h12v8H4V6z" />
                </svg>
                <span className="text-sm text-gray-600">
                  {/* Try to extract bed info from description or use occupancy-based default */}
                  {(() => {
                    if (room.description && (room.description.includes('bed') || room.description.includes('Bed'))) {
                      // More specific regex to extract just bed information
                      const bedMatch = room.description.match(/\d+\s+(single|double|queen|king|twin|extra\s+large\s+double|large\s+double)\s+bed/i);
                      if (bedMatch) {
                        return bedMatch[0];
                      }
                      // Fallback to simpler bed extraction
                      const simpleBedMatch = room.description.match(/\d+\s+bed/i);
                      if (simpleBedMatch) {
                        return simpleBedMatch[0];
                      }
                    }
                    return `Sleeps ${occupancyInfo.adults + occupancyInfo.children} guests`;
                  })()}
                </span>
              </div>
            </div>

            {/* Room Description */}
            <div className="space-y-2">
              <h3 className="font-semibold text-gray-800">Room Description:</h3>
              <p className="text-sm text-gray-600 leading-relaxed">
                {cleanDescription(room.description) || 'This double room features air conditioning and a tea and coffee maker, as well as a private bathroom boasting a shower. This double room has a wardrobe, a sofa and a flat-screen TV. The unit has 1 bed.'}
              </p>
            </div>

            {/* Bathroom Amenities - Dynamic based on facilities */}
            {hasFacility('bathroom') && (
              <div className="space-y-3">
                <h3 className="font-semibold text-gray-800">In your private bathroom:</h3>
                <div className="grid grid-cols-2 gap-2 text-sm text-gray-600">
                  {/* Standard bathroom amenities - show if bathroom facility exists */}
                  <div className="flex items-center gap-2">
                    <svg className="w-3 h-3 text-secondary-color" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                    </svg>
                    <span>Free toiletries</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <svg className="w-3 h-3 text-secondary-color" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                    </svg>
                    <span>Towels</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <svg className="w-3 h-3 text-secondary-color" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                    </svg>
                    <span>{hasFacility('shower') ? 'Shower' : 'Bathroom'}</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <svg className="w-3 h-3 text-secondary-color" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                    </svg>
                    <span>Toilet paper</span>
                  </div>
                </div>
              </div>
            )}

            {/* Facilities */}
            <div className="space-y-3">
              <h3 className="font-semibold text-gray-800">Facilities:</h3>
              <div className="grid grid-cols-2 gap-2 text-sm text-gray-600">
                {room.facilities.map((facility, index) => (
                  <div key={index} className="flex items-center gap-2">
                    <svg className="w-3 h-3 text-secondary-color" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                    </svg>
                    <span>{formatText(facility.name)}</span>
                  </div>
                ))}
              </div>
            </div>

            {/* Smoking Policy */}
            <div className="space-y-2">
              <h3 className="font-semibold text-gray-800">Smoking:</h3>
              <p className="text-sm text-gray-600">
                {room.smokingAllowed ? 'Smoking allowed' : 'No smoking'}
              </p>
            </div>
          </div>
          </div>
        </div>
      </div>
    </div>
  );
}

export default RoomDetailsModal;

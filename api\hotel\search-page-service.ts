import { autoSuggestionsResponse, LocationApiResponse } from "@/models/hotel/search-page.model"
import apiService from "../api-service"


export const getAutoSuggest = async (term:string) : Promise<autoSuggestionsResponse> => {
    return apiService.get<autoSuggestionsResponse>(`autosuggest?term=${term}`)
}

export const selectLocationAPi = async (locationId: string) : Promise<LocationApiResponse> => {
    return apiService.get(`location?location_id=${locationId}`)
}




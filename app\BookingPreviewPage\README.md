# Hotel Booking Preview Page - API Integration

This page now includes a complete hotel booking API integration that allows users to create bookings when they click the "Pay Now" button.

## 🚀 New Features

### 1. Booking API Service
- **File**: `api/hotel/hotel-booking-service.ts`
- **Endpoint**: `/api/v1/booking/zentrumhub/hotel/book`
- **Method**: POST
- **Base URL**: `http://*************:8081`

### 2. Data Models
- **File**: `app/BookingPreviewPage/hotel-booking-api.model.ts`
- **Includes**: Request/Response interfaces for the booking API
- **Covers**: Guest details, room allocations, billing contact, and address information

### 3. Utility Functions
- **File**: `app/BookingPreviewPage/utils/bookingApiUtils.ts`
- **Functions**:
  - `transformToBookingRequest()` - Converts form data to API payload
  - `validateBookingDataAvailability()` - Checks if all required data is present
  - `getDefaultBookingData()` - Provides fallback values for testing

## 🔧 Recent Improvements

### Data Availability Check Fix (Latest)
- **Problem**: Previous implementation checked localStorage data that wasn't used in final booking
- **Solution**: Refactored to only validate booking init API data (primary source)
- **Benefits**:
  - Eliminates logical inconsistency
  - Clearer error messages
  - More focused validation
  - Better user guidance
- **Files Modified**:
  - `utils/bookingApiUtils.ts` - Simplified validation logic
  - `page.tsx` - Improved error handling and user guidance
  - `README.md` - Updated documentation

## 🔄 How It Works

### 1. User Flow
1. User fills out guest details form
2. User clicks "Pay Now" button
3. Form validation occurs
4. **Data availability check** - validates booking init API data (primary source)
5. Data transformation to API format
6. API call to create booking
7. Success: Redirect to confirmation page
8. Error: Display error message

### 2. Data Flow & Sources
The system uses data from multiple sources with clear priorities:

**Primary Data Source (Booking Init API):**
- Room IDs and Rate IDs
- Hotel IDs and Search Keys
- Pricing and availability information

**User Input (Form Data):**
- Guest details and contact information
- Billing address and preferences

**Validation Layer:**
- Only checks for data that's actually used in booking creation
- Form validation handles user input validation
- API data validation ensures system data integrity

### 3. Data Availability Check
**What it checks:**
- ✅ Booking initialization API data (primary source)
- ❌ ~~localStorage data~~ (removed - not used in final booking)

**Why this approach:**
- Eliminates logical inconsistency
- Only validates data that's actually consumed
- Clearer error messages and user guidance
- More maintainable and focused code

## 📋 API Payload Structure

```typescript
{
  "search_key": "dubai-06-august-async-test-8",
  "hotel_id": "70480726",
  "rateIds": ["be385717-3047-42e1-891e-971f06310cc3"],
  "user_id": 123,
  "roomsAllocations": [
    {
      "roomid": "room-id-from-booking-init",
      "rateid": "rate-id-from-booking-init",
      "guests": [
        {
          "type": "Adult",
          "title": "Mr",
          "firstname": "John",
          "lastname": "Doe",
          "age": 25,
          "email": "<EMAIL>"
        }
      ]
    }
  ],
  "billingContact": {
    "title": "Mr",
    "firstName": "John",
    "lastName": "Doe",
    "age": 30,
    "contact": {
      "phone": "1234567890",
      "email": "<EMAIL>",
      "address": { /* address details */ }
    }
  }
}
```

## 🎯 Key Components

### 1. Enhanced Pay Now Button
- Shows loading state during API call
- Disabled during processing
- Displays progress text ("Creating Booking...")

### 2. Error Handling
- Displays booking errors prominently
- Dismissible error messages
- Graceful fallback for API failures

### 3. Loading States
- Visual feedback during API calls
- Prevents multiple submissions
- Clear user communication

## 🧪 Testing

### 1. Unit Tests
- **File**: `app/BookingPreviewPage/utils/__tests__/bookingApiUtils.test.ts`
- **Coverage**: Data transformation and validation functions

### 2. Manual Testing
- Fill out guest form completely
- Click Pay Now button
- Verify API call in network tab
- Check error handling with invalid data

## 🔧 Configuration

### 1. API Endpoints
Update these values in `hotel-booking-service.ts`:
```typescript
const response = await apiService.post<HotelBookingResponse>(
  "api/v1/booking/zentrumhub/hotel/book", // Update this endpoint
  payload
);
```

### 2. Base URL
Update in `axiosInstance.ts`:
```typescript
baseURL: process.env.NEXT_PUBLIC_API_BASE_URL || 'http://*************:8081/'
```

### 3. Default Values
Update fallback values in `bookingApiUtils.ts`:
```typescript
const finalSearchKey = searchKey || "your-default-search-key";
const finalHotelId = hotelId || "your-default-hotel-id";
const finalUserId = userId || 123; // Update with actual user ID logic
```

## 🚨 Error Scenarios

### 1. Missing Data
- **Cause**: Required booking data not available
- **Solution**: Refresh page to reinitialize booking session

### 2. API Failures
- **Cause**: Network issues, server errors, validation failures
- **Solution**: Display error message, allow retry

### 3. Form Validation
- **Cause**: Incomplete or invalid form data
- **Solution**: Highlight errors, scroll to first error

## 🔮 Future Enhancements

### 1. User Authentication
- Integrate with user management system
- Get actual user ID from auth context
- Handle user-specific booking data

### 2. Address Fields
- Add address input fields to guest form
- Validate address data
- Support multiple address formats

### 3. Age Fields
- Add age input for each guest
- Validate age requirements
- Handle child/infant pricing

### 4. Payment Integration
- Integrate with payment gateway
- Handle payment confirmations
- Support multiple payment methods

## 📞 Support

For issues or questions about the booking API integration:
1. Check console logs for detailed error information
2. Verify API endpoint configuration
3. Ensure all required data is available
4. Check network connectivity to API server

// Models for Rooms and Rates API

export interface RoomsAndRatesRequest {
  search_key: string;
  hotel_id: string;
}

export interface Tax {
  amount: number;
  description: string;
  isIncludedInBaseRate: boolean;
}

export interface DailyRate {
  amount: number;
  date: string;
  discount: number;
  taxIncluded: boolean;
}

export interface Policy {
  text: string;
  type: string;
}

export interface BoardBasis {
  description: string;
  type: string;
}

export interface CancellationRule {
  end: string;
  estimatedValue: number;
  start: string;
  value: number;
  valueType: string;
}

export interface CancellationPolicy {
  rules: CancellationRule[];
}

export interface Charge {
  amount: number;
  currency: string;
  description: string;
  frequency: string;
  type: string;
  unit: string;
}

export interface AdditionalCharge {
  charge: Charge;
  text: string;
}

export interface Offer {
  description: string;
  discountOffer: string;
  percentageDiscountOffer: string;
  title: string;
}

export interface TraderInformation {
  trader: any[];
}

export interface Occupancy {
  roomId: string;
  stdRoomId: string;
  numOfAdults: string;
  numOfChildren: string;
}

export interface Rate {
  id: string;
  availability: string;
  needsPriceCheck: boolean;
  isPackageRate: boolean;
  providerId: string;
  providerName: string;
  isContractedRate: boolean;
  type: string;
  baseRate: number;
  totalRate: number;
  minSellingRate: number;
  publishedRate: number;
  currency: string;
  taxes: Tax[];
  fees: any;
  dailyRates: DailyRate[];
  refundable: boolean;
  refundability: string;
  allGuestsInfoRequired: boolean;
  onlineCancellable: boolean;
  specialRequestSupported: boolean;
  payAtHotel: boolean;
  cardRequired: boolean;
  policies: Policy[];
  boardBasis: BoardBasis;
  offers: Offer[];
  cancellationPolicies: CancellationPolicy[];
  includes: string[] | null;
  additionalCharges: AdditionalCharge[];
  depositRequired: boolean;
  guaranteeRequired: boolean;
  distributionType: string;
  distributionChannel: string;
  publishedRateProviderId: string;
  publishedRateProviderName: string;
  IsPassportMandatory: boolean;
  IsPANMandatory: boolean;
  providerHotelId: string;
  isChildConvertedToAdult: boolean;
  traderInformation: TraderInformation;
  deposits: any[];
  occupancies: Occupancy[];
}

export interface Facility {
  name: string;
}

export interface ImageLink {
  url: string;
}

export interface RoomImage {
  links: ImageLink[] | null;
}

export interface Room {
  id: string;
  name: string;
  description: string;
  smokingAllowed: boolean;
  maxGuestAllowed: string;
  maxAdultAllowed: string;
  maxChildrenAllowed: string;
  beds: any[];
  facilities: Facility[];
  images: RoomImage[];
  views: string[];
}

export interface GroupedRate {
  rate: Rate;
  room: Room;
}

export interface RoomRecommendation {
  recommendationId: string;
  groupedRates: GroupedRate[];
}

// Updated response structure to match actual API
export interface RoomsAndRatesApiResponse {
  provider: string;
  data: RoomRecommendation[];
}

export type RoomsAndRatesResponse = RoomRecommendation[];




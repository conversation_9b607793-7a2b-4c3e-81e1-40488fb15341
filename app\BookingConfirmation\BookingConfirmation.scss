$primary: #003b95;
$secondary: #006ce4;
$white: #fff;
$light-bg: #f8f8f8;
$border-color: #e5e5e5;
$text-color: #333;
$text-muted: #777;
$success-color: #4CAF50;
$error-color: #F44336;
$box-shadow: 0 2px 10px rgba(0, 0, 0, 0.08);

* {
  box-sizing: border-box;
}

.booking-confirmation {
  max-width: 800px;
  margin: 30px auto;
  background-color: $white;
  border-radius: 8px;
  box-shadow: $box-shadow;
  overflow: hidden;
  
  &.failed {
    .confirmation-header {
      background-color: $error-color;
    }
  }
}

.confirmation-header {
  padding: 30px;
  text-align: center;
  position: relative;
  color: $white;
  
  &.success {
    background-color: $success-color;
  }
  
  &.failed {
    background-color: $error-color;
  }
  
  i {
    font-size: 50px;
    margin-bottom: 15px;
    animation: icon-appear 0.8s cubic-bezier(0.19, 1, 0.22, 1) forwards;
    opacity: 0;
    transform: scale(0.5);
  }
  
  h1 {
    font-size: 24px;
    margin: 0 0 10px;
    font-weight: 600;
    animation: fade-in-up 0.5s ease forwards;
    animation-delay: 0.3s;
    opacity: 0;
    transform: translateY(10px);
  }
  
  p {
    margin: 0;
    font-size: 16px;
    opacity: 0;
    animation: fade-in-up 0.5s ease forwards;
    animation-delay: 0.5s;
    transform: translateY(10px);
  }
}

@keyframes icon-appear {
  0% {
    opacity: 0;
    transform: scale(0.5) rotate(-15deg);
  }
  50% {
    opacity: 1;
    transform: scale(1.2) rotate(5deg);
  }
  70% {
    transform: scale(0.9) rotate(0deg);
  }
  100% {
    opacity: 1;
    transform: scale(1) rotate(0deg);
  }
}

@keyframes fade-in-up {
  0% {
    opacity: 0;
    transform: translateY(10px);
  }
  100% {
    opacity: 0.9;
    transform: translateY(0);
  }
}

.confirmation-code {
  background-color: $light-bg;
  padding: 15px;
  text-align: center;
  border-bottom: 1px solid $border-color;
  
  p {
    margin: 0;
    font-size: 16px;
    
    strong {
      font-size: 18px;
      color: $primary;
    }
  }
}

.error-message {
  background-color: lighten($error-color, 38%);
  padding: 15px;
  text-align: center;
  border-bottom: 1px solid $border-color;
  
  p {
    margin: 0;
    font-size: 16px;
    color: darken($error-color, 10%);
    font-weight: 500;
  }
}

.confirmation-details {
  padding: 25px;
}

.section {
  margin-bottom: 30px;
  
  h2 {
    font-size: 18px;
    color: $secondary;
    margin: 0 0 20px;
    padding-bottom: 10px;
    border-bottom: 1px solid $border-color;
  }
}

.detail-row {
  display: flex;
  flex-wrap: wrap;
  margin-bottom: 15px;
  
  &:last-child {
    margin-bottom: 0;
  }
}

.detail-col {
  flex: 1;
  min-width: 250px;
  padding: 0 10px;
  margin-bottom: 15px;
  
  &:first-child {
    padding-left: 0;
  }
  
  &:last-child {
    padding-right: 0;
  }
}

.detail-item {
  display: flex;
  align-items: flex-start;
  
  i {
    color: $primary;
    font-size: 18px;
    margin-right: 15px;
    min-width: 20px;
    text-align: center;
  }
  
  .detail-text {
    flex: 1;
    
    label {
      display: block;
      font-size: 14px;
      color: $text-muted;
      margin-bottom: 5px;
    }
    
    p {
      margin: 0;
      font-size: 16px;
      font-weight: 500;
    }
  }
}

.payment-summary {
  background-color: $light-bg;
  padding: 20px;
  border-radius: 6px;
}

.payment-row {
  display: flex;
  justify-content: space-between;
  padding: 10px 0;
  font-size: 15px;
  border-bottom: 1px solid $border-color;
  
  &:last-of-type {
    border-bottom: none;
  }
  
  &.total {
    font-weight: 600;
    font-size: 17px;
    color: $primary;
    margin-top: 5px;
    padding-top: 15px;
    border-top: 1px solid $border-color;
  }
}

.payment-method {
  display: flex;
  justify-content: space-between;
  padding-top: 15px;
  font-size: 14px;
  color: $text-muted;
}

.info-section,
.payment-failed-section {
  .info-item {
    display: flex;
    align-items: flex-start;
    margin-bottom: 15px;
    
    &:last-child {
      margin-bottom: 0;
    }
    
    i {
      color: $secondary;
      font-size: 16px;
      margin-right: 15px;
      margin-top: 3px;
    }
    
    p {
      margin: 0;
      font-size: 14px;
      color: $text-muted;
    }
  }
}

.payment-failed-section {
  background-color: lighten($error-color, 42%);
  padding: 15px;
  border-radius: 6px;
  
  .info-item {
    i {
      color: $error-color;
    }
    
    p {
      color: darken($error-color, 5%);
    }
  }
}

.actions {
  display: flex;
  gap: 12px; // Reduced gap between buttons
  justify-content: center;
  margin-top: 30px;
  
  .btn {
    padding: 10px 18px; // Reduced padding for smaller button size
    border-radius: 4px;
    font-size: 14px; // Reduced font size
    font-weight: 500;
    text-decoration: none;
    cursor: pointer;
    transition: all 0.2s ease;
    display: inline-flex;
    align-items: center;
    justify-content: center; // Center content
    border: none;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1); // Subtle shadow for depth
    
    i {
      margin-right: 8px;
      font-size: 14px; // Match icon size to text
    }
    
    &.primary {
      background-color: $primary;
      color: $white;
      
      &:hover {
        background-color: darken($primary, 10%);
        transform: translateY(-1px); // Slight lift effect on hover
      }
      
      &:active {
        transform: translateY(0);
      }
    }
    
    &.secondary {
      background-color: $secondary;
      color: $white;
      
      &:hover {
        background-color: darken($secondary, 10%);
        transform: translateY(-1px); // Slight lift effect on hover
      }
      
      &:active {
        transform: translateY(0);
      }
    }
  }
}

.footer {
  background-color: $light-bg;
  border-top: 1px solid $border-color;
  padding: 20px;
  text-align: center;
  
  p {
    margin: 0 0 15px;
    font-size: 15px;
  }
  
  .contact {
    display: flex;
    justify-content: center;
    flex-wrap: wrap;
    gap: 20px;
    font-size: 14px;
    color: $text-muted;
    
    i {
      margin-right: 5px;
    }
  }
}

// Loading state styles
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
  
  .loading-spinner {
    width: 40px;
    height: 40px;
    border: 4px solid $light-bg;
    border-top: 4px solid $primary;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: 20px;
  }
  
  .loading-text {
    font-size: 16px;
    color: $text-muted;
  }
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

// Error state styles
.error-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
  text-align: center;
  
  .error-icon {
    font-size: 48px;
    color: $error-color;
    margin-bottom: 20px;
  }
  
  .error-title {
    font-size: 20px;
    font-weight: 600;
    color: $text-color;
    margin-bottom: 10px;
  }
  
  .error-message {
    font-size: 16px;
    color: $text-muted;
    margin-bottom: 30px;
    max-width: 400px;
  }
  
  .error-actions {
    display: flex;
    gap: 15px;
    justify-content: center;
    flex-wrap: wrap;
  }
}

// Animation for failed booking alert
.failed .error-message {
  animation: pulse 2s ease-in-out;
}

@keyframes pulse {
  0% {
    background-color: lighten($error-color, 38%);
  }
  50% {
    background-color: lighten($error-color, 30%);
  }
  100% {
    background-color: lighten($error-color, 38%);
  }
}

@media (max-width: 768px) {
  .booking-confirmation {
    margin: 25px 15px 15px 15px;
  }
  
  .detail-row {
    flex-direction: column;
  }
  
  .detail-col {
    padding: 0;
  }
  
  .actions {
    flex-direction: column;
    
    .btn {
      width: 100%;
    }
  }
  
  .error-actions {
    flex-direction: column;
    
    .btn {
      width: 100%;
    }
  }
}

@media print {
  body {
    background-color: $white;
  }
  
  .booking-confirmation {
    box-shadow: none;
    margin: 0;
  }
  
  .actions {
    display: none !important;
  }
  
  // Don't print failed booking page
  .booking-confirmation.failed {
    display: none !important;
  }
  
  .loading-container,
  .error-container {
    display: none !important;
  }
}

"use client";
import React, { useState, useEffect } from 'react';
import { useSearchParams } from 'next/navigation';
import ItineraryCard, { ItineraryData } from '../components/ItineraryCard/ItineraryCard';
import { getBookingDetails, transformToItineraryData } from '@/api/hotel/booking-details-service';
import { useTranslation } from '@/app/hooks/useTranslation';

function Page() {
  const { t } = useTranslation();
  const searchParams = useSearchParams();

  // State management
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [bookingData, setBookingData] = useState<ItineraryData | null>(null);

  useEffect(() => {
    const fetchBookingDetails = async () => {
      try {
        setIsLoading(true);
        setError(null);

        // Get booking reference from search params
        const bookingReference = searchParams?.get('bookingReference');

        if (!bookingReference) {
          throw new Error('No booking reference found. Please check your booking confirmation email or try again.');
        }

        console.log('🔍 Fetching booking details for reference:', bookingReference);

        // Fetch booking details from API
        const apiResponse = await getBookingDetails(bookingReference);
        console.log('API Response:', apiResponse);
        

        // Transform API response to ItineraryData format
        const itineraryData = transformToItineraryData(apiResponse);

        setBookingData(itineraryData);

        console.log('✅ Booking details loaded successfully for itinerary:', itineraryData);

      } catch (error) {
        console.error('❌ Failed to fetch booking details:', error);
        const errorMessage = error instanceof Error
          ? error.message
          : 'Failed to load booking details. Please try again.';
        setError(errorMessage);
      } finally {
        setIsLoading(false);
      }
    };

    fetchBookingDetails();
  }, [searchParams]);

  // Loading state
  if (isLoading) {
    return (
      <div className="py-8 px-4 bg-gray-50">
        <div className="max-w-7xl mx-auto">
          <div className="bg-white rounded-md shadow p-8 text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mx-auto mb-4"></div>
            <p className="text-gray-600">{t('common.loading')}</p>
          </div>
        </div>
      </div>
    );
  }

  // Error state
  if (error) {
    return (
      <div className="py-8 px-4 bg-gray-50">
        <div className="max-w-7xl mx-auto">
          <div className="bg-white rounded-md shadow p-8 text-center">
            <div className="text-red-500 mb-4">
              <i className="fas fa-exclamation-triangle text-4xl"></i>
            </div>
            <h2 className="text-xl font-bold text-gray-800 mb-2">
              {t('common.error')}
            </h2>
            <p className="text-gray-600 mb-4">{error}</p>
            <button
              onClick={() => window.location.reload()}
              className="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded transition-colors"
            >
              {t('common.retry')}
            </button>
          </div>
        </div>
      </div>
    );
  }

  // Success state with booking data
  if (!bookingData) {
    return (
      <div className="py-8 px-4 bg-gray-50">
        <div className="max-w-7xl mx-auto">
          <div className="bg-white rounded-md shadow p-8 text-center">
            <p className="text-gray-600">No booking data available.</p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="py-8 px-4 bg-gray-50">
      <div className="max-w-7xl mx-auto">
        <ItineraryCard data={bookingData} />
      </div>
    </div>
  );
}

export default Page
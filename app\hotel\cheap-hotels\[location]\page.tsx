"use client";

import './page.scss';
import HotelSearchBar from '@/app/components/hotel/HotelSearchBar/HotelSearchBar'
import { useCommonContext } from '@/app/contexts/commonContext';
import React, { useCallback, useEffect, useRef, useState } from 'react'
import { useRouter, useParams } from 'next/navigation'
import { Hotel } from '@/models/hotel/list-page.model';
import FilterMapShimmer from '../components/shimmers/FilterMapShimmer';
import FilterShimmer from '../components/shimmers/FilterShimmer/FilterShimmer';
import SortBarShimmer from '../components/shimmers/SortBarShimmer/SortBarShimmer';
import HotelSort from '@/app/components/hotel/HotelSort/HotelSort';
import HotelCardShimmer from '../components/shimmers/HotelCardShimmer/HotelCardShimmer';
import ShimmerCardList from '@/app/components/shimmer/ShimmerCardList';
import HotelCard from '../components/hotel-card/HotelCard';
import BottomToTopPopup from '@/components/popups/BottomToTopPopup';
import { searchApi, searchInitApi } from '@/api/hotel/list-page-service';
import { convertFormDataToSearchData, loadHotelSearchDataFromStorage, loadHotelSearchFormDataFromStorage } from '@/app/utils/hotelSearchUtils';
import { transformSearchInitApiResponse, updatePaginatedHotels } from '@/helpers/hotel/list-page-helper';
import {  applyFiltersHelper, populateHotelFilterData } from '@/helpers/hotel/filter-helper';
import { HotelFilterData } from '@/models/hotel/filter.model';
import { applySort } from '@/helpers/hotel/sort-helper';
import { HotelSearchData, HotelSearchFormData } from '@/models/hotel/search-page.model';
import MobileSortFilterButtonsShimmer from '../components/shimmers/mobile-sort-filter-section-shimmer/MobileSortFilterButtonShimmer';
import ToggleButton from '@/app/components/toggle-button/ToggleButton';
import FilterHotel from '../components/filter-hotel/FilterHotel';
import ListMap from '../components/map-modal/ListMap';
import { MapboxMap } from '@/app/components/map-box-map/MapBoxMap';
import { buildHotelDetailUrl } from '@/helpers/hotel/common-helper';
// import { SearchApiResponse } from '@/models/hotel/search-page.model';

function Page() {
    const router = useRouter();
    const params = useParams();
    const location = params && params.location as string;
    const [masterHotelList, setMasterHotelList] = useState<Hotel[]>([]);
    const [sorttedHotelList, setSorttedHotelList] = useState<Hotel[]>([]);
    const [paginatedList, setpaginationList] = useState<Hotel[][]>([]);
    const [displayList, setDisplayList] = useState<Hotel[]>([]);
    const [itemsPerPage, setItemsPerPage] = useState<number>(20);
    const [currentPage, setCurrentPage] = useState<number>(1);
    const [mapDisplayList, setMapDisplayList] = useState<Hotel[]>([]);
    const [priceDistribution, setPriceDistribution] = useState<number[]>([]);
    const [selectedHotels, setSelectedHotels] = useState<Hotel[]>([]);
    const [isMobile, setIsMobile] = useState<boolean>(false);
    const [showMap, setShowMap] = useState<boolean>(false);
    const [showCardShimmer , setShowCardShimmer] = useState<boolean>(true);
    const [showFilterShimmer, setShowFilterShimmer] = useState<boolean>(true);
    const [showSortShimmer,setshowSortShimmer] = useState<boolean>(true);
    const [isAllowClicks, setIsAllowClicks] = useState<boolean>(false);
    const [filterData, setFilterData] = useState<HotelFilterData>();
    const [viewMode, setViewMode] = useState<"list" | "grid">("list");
    const [selectedSort, setSelectedSort] = useState<string>("Top picks for long stays");
    const [isMobileSortFilterActive, setIsMobileSortFilterActive] = useState<boolean>(false);
    const [activeMobileFilterNav, setActiveMobileFilterNav] = useState<string>("Sort by");
    const {searchKey , setHotelSearchData, setHotelSearchFormData , hotelSearchData, setSearchKey , hotelSearchFormData } = useCommonContext();

    const paginatedListRef = useRef(paginatedList);
    const filterDataRef = useRef(filterData);
    const isInitRef = useRef(false);
    const masterHotelListRef = useRef(masterHotelList);
    const sorttedHotelListRef = useRef(sorttedHotelList);
    const currentPageRef = useRef(currentPage);
    const observer = useRef<IntersectionObserver | null>(null);
    const triggerRef = useRef<HTMLDivElement | null>(null);
    const displayListRef = useRef(displayList);

    // Decode the location parameter for display
    const decodedLocation = decodeURIComponent(location || '');


    // useEffect(() => {
    //     // Validate that the URL location matches the search data
    //     if (hotelSearchFormData?.searchQuery) {
    //         const searchLocation = hotelSearchFormData.searchQuery.toLowerCase().replace(/\s+/g, '-');
    //         const urlLocation = decodedLocation.toLowerCase();
            
    //         // If locations don't match, redirect to correct URL
    //         if (searchLocation !== urlLocation) {
    //             const correctUrl = `/hotel/cheap-hotels/${encodeURIComponent(searchLocation)}`;
    //             router.replace(correctUrl);
    //             return;
    //         }
    //     }
    // // }, [hotelSearchFormData, decodedLocation, router]);
    // }, []);


    const handleShowNewPage = useCallback((nextPage:number)=>{
        const totalPages = paginatedListRef.current.length;
        if(nextPage <= totalPages){
            setCurrentPage(nextPage);
            const newHotels = paginatedList[nextPage - 1];
            if (newHotels) {
                setDisplayList(prevDisplayList => [...prevDisplayList, ...newHotels]);
            }
        }
    },[paginatedList])

    const convertToPaginatedList = useCallback((hotel:Hotel[],isFilter:boolean = true)=>{
        let currentPaginationList : Hotel[][] = []
        if(isFilter){
            currentPaginationList = [];
        }else{
            currentPaginationList = paginatedListRef.current;
        }
        
        const newPaginatedList = updatePaginatedHotels(hotel,currentPaginationList,itemsPerPage);
        if(isFilter){
            setDisplayList(newPaginatedList[0]);
        }else{
            if(displayListRef.current.length === 0){
                setDisplayList(newPaginatedList[0]);
            }
        }
        setpaginationList(newPaginatedList);
        setCurrentPage(1);
        setShowCardShimmer(false);
        setshowSortShimmer(false);
        setShowFilterShimmer(false);
        setIsAllowClicks(true);
    // },[paginatedList]) 
    },[itemsPerPage])


    const applayFilters = useCallback((hotelList:Hotel[],newFilterData:HotelFilterData,isFilter:boolean = true)=>{
        const filteredHotels = applyFiltersHelper(hotelList,newFilterData);
        console.log("filteredHotels",filteredHotels);
        
        convertToPaginatedList(filteredHotels , isFilter);
    },[convertToPaginatedList])

    const handleFilterChange = useCallback((updatedFilterData: HotelFilterData | undefined , hotelList?:Hotel[]) => {
        setFilterData(updatedFilterData);
        console.log("updatedFilterData",updatedFilterData);
        
        if(updatedFilterData){
            alert("filter changed");
            if(hotelList){
                applayFilters(hotelList,updatedFilterData , false);
            }else{
                applayFilters(sorttedHotelListRef.current,updatedFilterData , true);
            }
        }
    // }, [applayFilters,masterHotelList]);
    }, [applayFilters]);


    const populateFilterData = useCallback((hotel:Hotel[],filterData:HotelFilterData | undefined)=>{
        if(hotel.length > 0){
            const newFilterData = populateHotelFilterData(hotel,filterData);
            setFilterData(newFilterData);
            handleFilterChange(newFilterData , hotel);
        }
    },[handleFilterChange])


    // Handle sort changes
    const handleSortChange = useCallback((sortOption: string) => {
        setSelectedSort(sortOption);
        const sortedHotels = applySort(masterHotelListRef.current, sortOption);
        setSorttedHotelList(sortedHotels);
        sorttedHotelListRef.current = sortedHotels;
        handleFilterChange(filterDataRef.current);
    // }, [convertToPaginatedList,masterHotelList]);
    }, [handleFilterChange]);


    const handleCardClick = useCallback((hotelId: number) => {
        
        if(hotelSearchFormData){
            // const url = buildHotelDetailUrl(hotelId, hotelSearchFormData);
            const url = `/HotelDetail?hotelId=${hotelId}`;
            localStorage.setItem('selectedHotel', JSON.stringify(hotelId));
            if (isMobile) {
                router.push(url);
            } else {
                window.open(url, "_blank");
            }
        }


    },[isMobile, router , hotelSearchFormData]);



    const isHotelSelected = (hotelId: number) => {
        return selectedHotels.some((hotel) => hotel.hotelId === hotelId);
    };

    const handleCheckboxChange = useCallback((hotel: Hotel, isChecked: boolean) => {
        if (isChecked) {
            setSelectedHotels((prev) => [...prev, hotel]);
        } else {
            setSelectedHotels((prev) =>
                prev.filter((h) => h.hotelId !== hotel.hotelId)
            );
        }       
    },[])

    const handleViewMode = (mode: "list" | "grid") => {
        if (typeof window !== "undefined" && window.innerWidth > 780) {
            setViewMode(mode);
            localStorage.setItem("viewMode", mode);
        }
    };

    const generatePriceDistribution = ( hotels: Hotel[],min: number,max: number,bins: number): number[] => {
        const range = max - min;
        const binSize = range / bins;
        const distribution = Array(bins).fill(0);
    
        hotels.forEach((hotel) => {
          const price = hotel.fareDetail?.totalPrice || 0;
          if (price >= min && price <= max) {
            const binIndex = Math.floor((price - min) / binSize);
            const adjustedBinIndex = Math.min(binIndex, bins - 1);
            distribution[adjustedBinIndex]++;
          }
        });
    
        return distribution;
    };

    const callSearchApi = useCallback(async (searchKey:string,skip:number = 0)=>{
        setShowFilterShimmer(true);
        setIsAllowClicks(false);
        
        try{
            const response = await searchApi({ search_key: searchKey }, skip);
            if(response && response.results && response.results.length > 0 && response.results[0].hotels.length > 0){
                    const hotels = transformSearchInitApiResponse(response);
                    setMasterHotelList((prevHotels) => [...prevHotels, ...hotels]);
                    populateFilterData(hotels,filterDataRef.current);
                    const lastBatchindex = response.results.length - 1;
                    if(!response.isCompleted && !response.results[lastBatchindex].batch_info.is_last_batch){
                        setTimeout(() => {
                            // callSearchApi(searchKey,skip + response.results.length);
                        },5000);
                    }
            }else {
                setTimeout(() => {
                    callSearchApi(searchKey,skip);
                }, 5000);
            }
        }catch (error){
            console.error("Error calling searchApi:", error);
        }
        
    },[populateFilterData])

    const callSearchInitApi = useCallback(async (skip:number = 0 , payload:HotelSearchData)=>{
        setshowSortShimmer(true);
        setShowFilterShimmer(true);
        setShowCardShimmer(true);
        setIsAllowClicks(false);

        if(!payload){
            return
        }

        if(searchKey && searchKey.trim().length > 0){
            callSearchApi(searchKey,skip);
            return
        }

        try{
            if(payload){
                const response = await searchInitApi(payload, skip, 1);
                if(response && response.searchKey){
                    setSearchKey(response.searchKey);
                    localStorage.setItem("searchKey", response.searchKey);
                    callSearchApi(response.searchKey);
                    if(response.results.length > 0){
                        if(response.results[0].hotels.length > 0){                          
                            const hotels = transformSearchInitApiResponse(response);
                            console.log("hotels",hotels);
                            if(hotels.length > 0){
                                console.log("entered the third if conditionn");
                                // convertToPaginatedList(hotels);
                                setDisplayList(hotels);
                                setShowCardShimmer(false);
                            }
                        }
                    }else{
                        // callSearchInitApi(0,payload);
                    }
                }
            }else{
                router.push('/hotel');
            }
        }catch (error){
            console.error("Error calling searchInitApi:", error);
        }
    // },[hotelSearchData,router,callSearchApi,convertToPaginatedList,setSearchKey])
    },[callSearchApi,setSearchKey,router,searchKey])

    const handleSearch = useCallback((searchFormData:HotelSearchFormData)=>{
        if(searchFormData){
            setSearchKey("");
            localStorage.removeItem("searchKey");
            setHotelSearchFormData(searchFormData);
            localStorage.setItem("hotelSearchFormData", JSON.stringify(searchFormData));
            const searchData = convertFormDataToSearchData(searchFormData);
            localStorage.setItem("hotelSearchData", JSON.stringify(searchData));
            setHotelSearchData(searchData);
        }
    },[setHotelSearchData,setHotelSearchFormData,setSearchKey])

    // useeffects
    useEffect(() => {
        const checkIsMobile = () => {
        setIsMobile(window.innerWidth < 950);
        };

        checkIsMobile(); // Initial check

        window.addEventListener("resize", checkIsMobile); // Optional: update on resize

        return () => {
        window.removeEventListener("resize", checkIsMobile); // Cleanup
        };
    }, []);

    useEffect(()=>{
         if (isInitRef.current) {
                return;
        }
            
        const getHotelSearchFormData = () => {
            if (typeof window !== "undefined") {

                let data: HotelSearchData | null = null;
                let formData: HotelSearchFormData | null = null;

                if (hotelSearchData) {
                    data = hotelSearchData
                } else {
                    data = loadHotelSearchDataFromStorage();
                    if(data){
                        setHotelSearchData(data);
                    }
                }
                if (hotelSearchFormData) {
                    formData = hotelSearchFormData
                } else {
                    formData = loadHotelSearchFormDataFromStorage();
                    if(formData){
                        setHotelSearchFormData(formData);
                    }
                }
                if (data) {
                    callSearchInitApi(0, data);
                }
            }
        };

        getHotelSearchFormData();
        isInitRef.current = true;
    // },[getHotelSearchFormData])
    },[callSearchInitApi,setHotelSearchData,hotelSearchData,hotelSearchFormData,setHotelSearchFormData])


    useEffect(() => {
        if (filterData && masterHotelList.length > 0) {
          const distribution = generatePriceDistribution(
            masterHotelList,
            filterData.priceRange.min,
            filterData.priceRange.max,
            40 // Number of histogram bins
          );
          setPriceDistribution(distribution);
        }
    // }, [masterHotelList, filterData]);
    }, [filterData,masterHotelList]);

    useEffect(()=>{
        if(masterHotelList.length > 0){
            masterHotelListRef.current = masterHotelList;
            setSorttedHotelList(masterHotelList);
        }
    },[masterHotelList])

    useEffect(()=>{
        if(filterData){
            filterDataRef.current = filterData;
        }
    },[filterData])

    useEffect(()=>{
        if(sorttedHotelList.length > 0){
            sorttedHotelListRef.current = sorttedHotelList;
        }
    },[sorttedHotelList])

    useEffect(()=>{
        if(currentPageRef.current !== currentPage && currentPage > 0){
            currentPageRef.current = currentPage;
        }
    },[currentPage])

    useEffect(()=>{
        if(paginatedList.length > 0){
            paginatedListRef.current = paginatedList;
        }
    },[paginatedList])

    useEffect(() => {
        if (displayList && displayList.length > 0) {
            displayListRef.current = displayList;
        }
    }, [displayList]);

    useEffect(() => {
        if (observer.current) observer.current.disconnect();

        observer.current = new IntersectionObserver((entries) => {
            if (entries[0].isIntersecting) {
                handleShowNewPage(currentPageRef.current + 1);
            }
        });

        // ✅ Capture the current value once
        const currentTarget = triggerRef.current;

        if (currentTarget) {
        observer.current.observe(currentTarget);
        }

        return () => {
        if (observer.current && currentTarget) {
            observer.current.unobserve(currentTarget);
        }
        };
    }, [handleShowNewPage]);


  return (
    <div className="hotel-search-result-container common-container">
        <div className="hotel-search-result">
            <div className="searchbar-container-list">
                <HotelSearchBar
                    onSearch={handleSearch}
                    isModify={true}
                    showShareButton={true}
                    selectedHotels={selectedHotels}
                />
            </div>
            <div className="route-path">
                <span>Home</span> <i className="fa-solid fa-greater-than"></i>
                <span>{decodedLocation || hotelSearchFormData?.searchQuery || "..."}</span>{" "}
                <i className="fa-solid fa-greater-than"></i> Search results
            </div>
            <div className="filter-card-container">
                {!isMobile && (
                    <div className="filter-map-container">
                       {!showFilterShimmer ? (
                            <>
                                <div className="hotel-list-map-container">
                                    {/* <MapboxMap
                                        center={
                                            hotelSearchFormData?.geoCode
                                            ? [parseFloat(hotelSearchFormData.geoCode.long), parseFloat(hotelSearchFormData.geoCode.lat)]
                                            : [76.2673, 9.9312]
                                        }
                                        zoom={12}
                                        buttonText="View on Map"
                                        onButtonClick={() => setShowMap(true)}
                                    /> */}
                                </div>
                                <div className={`filter`}>
                                    <FilterHotel
                                        handleChange={handleFilterChange}
                                        filterData={filterData}
                                        priceDistribution={priceDistribution}
                                    />
                                </div>
                            </>

                       ) : (
                            <>
                                <div className="hotel-list-map-container">
                                    <FilterMapShimmer />
                                </div>
                                <FilterShimmer />
                            </>
                       )}
                    </div>
                )}

                {/* card section */}
                <div className="card">
                    {!isMobile && (
                        <div className='card-head'>
                            <div className='sort-bar-container'>
                                {showSortShimmer ? (
                                    <SortBarShimmer />
                                ):(
                                    <HotelSort
                                        selectedSort={selectedSort}
                                        onSortChange={handleSortChange}
                                        isMobile={isMobile}
                                    />
                                )}
                            </div>

                            {/* Right side - Toggle buttons */}
                            <ToggleButton viewMode={viewMode} handleViewMode={handleViewMode} />
                        </div>
                    )}



                    <div className={`${viewMode == "list" ? "hotel-card-container" : "hotel-grid-container "}`}>
                        {showCardShimmer ? (
                            <ShimmerCardList
                                viewMode={viewMode}
                                ShimmerComponent={HotelCardShimmer}
                                listCount={3}
                                gridCount={6}
                            />
                         ) : (
                            displayList &&
                            displayList.length > 0 ? (
                                displayList.map((hotel, index) => {
                                    const isSecondLastItem = index === displayList.length - 2;
                                    return(
                                        <div key={index} ref={isSecondLastItem ? triggerRef : null} className= {`${viewMode === "list" ? "card-wrapper" : "grid-wrapper"}`} data-hotel-index={index}>
                                            <HotelCard
                                                hotel={hotel}
                                                type={viewMode}
                                                handleClick={()=>handleCardClick(hotel.id)}
                                                onCheckboxChange={handleCheckboxChange}
                                                isChecked={isHotelSelected(hotel?.hotelId)}
                                                isSearchCompleted={isAllowClicks}
                                                isPricingAvailable={isAllowClicks}
                                            />
                                        </div>
                                    )
                                })
                            ) : (
                                <div className="no-hotels-found">
                                    <div className="no-hotels-icon">
                                        <i className="fa-solid fa-bed"></i>
                                    </div>
                                    <h3 className="no-hotels-title">No Hotels Found</h3>
                                    <p className="no-hotels-description">
                                        We couldn&apos;t find any hotels matching your search criteria.
                                        Try adjusting your filters or search for a different location.
                                    </p>
                                    <div className="no-hotels-actions">
                                        <button
                                            className="btn-primary"
                                            onClick={() => window.location.reload()}
                                        >
                                            <i className="fa-solid fa-refresh"></i>
                                            Refresh Search
                                        </button>
                                        <button
                                            className="btn-secondary"
                                            onClick={() => window.history.back()}
                                        >
                                            <i className="fa-solid fa-arrow-left"></i>
                                            Go Back
                                        </button>
                                    </div>
                                </div>
                            )
                         )}
                    </div>
                </div>
            </div>
        </div>


        {/* Mobile Sort/Filter Buttons */}
        {isMobile && (
            showFilterShimmer ? (
                <MobileSortFilterButtonsShimmer />
            ) : (  
                <div className="mobile-sort-filter-buttons-container">
                    <div
                        className="mobile-sort-filter-buttons-container__button"
                        onClick={() => {
                            setIsMobileSortFilterActive(true);
                            setActiveMobileFilterNav("Sort by");
                        }}
                    >
                        <i className="fa-solid fa-sort"></i> Sort Results
                    </div>
                    <div
                        className="mobile-sort-filter-buttons-container__button"
                        onClick={() => {
                            setIsMobileSortFilterActive(true);
                            setActiveMobileFilterNav("Filters");
                        }}
                    >
                        <i className="fa-solid fa-filter"></i> Filter
                    </div>
                    <div
                        className="mobile-sort-filter-buttons-container__button"
                        onClick={() => {
                            setShowMap(true);
                        }}
                    >
                        <i className="fa-solid fa-map-location-dot"></i> Map View
                    </div>
                </div>
             )

        )}

        {/* Mobile Sort/Filter Modal using BottomToTopPopup */}
        <BottomToTopPopup
            isOpen={isMobileSortFilterActive}
            onClose={() => setIsMobileSortFilterActive(false)}
            heading="Sort & Filters"
            type={activeMobileFilterNav === "Sort by" ? "sort" : "filter"}
            enableDrag={true}
            snapPoints={[40, 70, 90]}
        >
            <div className="mobile-sort-filter-content">
                {/* Tab Navigation */}
                <div className="mobile-sort-filter-content__navbar">
                    <div
                        className={`nav-item ${
                            activeMobileFilterNav === "Sort by" ? "active" : ""
                        }`}
                        onClick={() => setActiveMobileFilterNav("Sort by")}
                    >
                        Sort by
                    </div>
                    <div
                        className={`nav-item ${
                            activeMobileFilterNav === "Filters" ? "active" : ""
                        }`}
                        onClick={() => setActiveMobileFilterNav("Filters")}
                    >
                        Filters
                    </div>
                </div>

                {/* Tab Content */}
                <div className="mobile-sort-filter-content__content">
                    {activeMobileFilterNav === "Sort by" && (
                        <HotelSort
                            selectedSort={selectedSort}
                            onSortChange={handleSortChange}
                            isMobile={isMobile}
                        />                      
                    )}

                    {activeMobileFilterNav === "Filters" && (
                        <div className="mobile-filter-wrapper">
                            <FilterHotel
                                handleChange={handleFilterChange}
                                filterData={filterData}
                                priceDistribution={priceDistribution}
                            />
                        </div>
                    )}
                </div>

                {/* Footer with Done Button */}
                <div className="mobile-sort-filter_footer">
                    <div
                        className="mobile-sort-filter_footer__done-button"
                        onClick={() => {
                            setIsMobileSortFilterActive(false);
                        }}
                    >
                        <span>Done</span>
                    </div>
                </div>
            </div>
        </BottomToTopPopup>

        {isMobile ? (
            <BottomToTopPopup
                isOpen={showMap}
                onClose={() => setShowMap(false)}
                heading="Map View"
                type="filter"
                enableDrag={true}
                snapPoints={[40, 70, 90]}
            >
                <ListMap hotels={displayList} filterData={filterData} handleFilterChange={handleFilterChange} />
            </BottomToTopPopup>
        ):(
            showMap && (
                <div className="hidden md:block w-full h-full fixed top-0 left-0 z-50 bg-white">
                    <ListMap hotels={displayList} filterData={filterData} handleFilterChange={handleFilterChange} />
                </div>
            )

        )}            
    </div>
  )
}

export default Page

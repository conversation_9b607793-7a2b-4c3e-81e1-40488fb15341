"use client";
import { useState } from "react";
import "./HotelOverviewComponent.scss";
import Image from "next/image";
import PhotoGalleryModal from "../PhotoGalleryModal/PhotoGalleryModal";
import useScrollLock from "@/app/components/utilities/ScrollLock/useScrollLock";
import { HotelDetailApiResponse, Images } from "../../hotel-detail-api-response.model";


interface HotelOverviewComponentProps {
  images: Images[];
  hotelData?: HotelDetailApiResponse;
}

function HotelOverviewComponent({ images, hotelData }: HotelOverviewComponentProps) {
  const [isPhotoGalleryModalActive, setIsPhotoGalleryModalAcive] = useState(false);
  useScrollLock(isPhotoGalleryModalActive);

  const heroImage = images.find(img => img.is_hero_image) || images[0];
  const secondaryImages = images.filter(img => !img.is_hero_image);
  const sideImages = secondaryImages.slice(0, 2);
  const bottomRowImages = secondaryImages.slice(2, 7);
  const remainingPhotos = Math.max(0, images.length - 8);

  // Create dynamic detail data from backend
  const detailData = hotelData ? {
    name: hotelData.name,
    rating: parseFloat(hotelData.userRating || "0"),
    stars: "★".repeat(hotelData.starRating || 0),
    position: [hotelData.geoLocationInfo?.lat || 0, hotelData.geoLocationInfo?.lon || 0 ] as [number, number],
    description: hotelData.userRatingCategoty || "No rating available",
    reviews: `${hotelData.reviews?.length || 0} reviews`,
    locationScore: `Great location - ${hotelData.userRating || "N/A"}`,
    address: hotelData.address,
    attractions: [
      // Generate attractions based on hotel location/city
      { name: `${hotelData.city} Center`, distance: "1.2 km" },
      { name: "Airport", distance: "5.0 km" },
      { name: "Beach", distance: "0.8 km" }
    ]
  } : {
    // Fallback data if no hotel data available
    name: "Hotel Example",
    rating: 8.5,
    stars: "★★★★",
    position: [9.9482882, 76.3194938] as [number, number],
    description: "Excellent",
    reviews: "1,234 reviews",
    locationScore: "Great location - 9.2",
    address: "123 Main St, City, Country",
    attractions: [
      { name: "Beach", distance: "0.5 km" },
      { name: "City Center", distance: "1.2 km" }
    ]
  };

  // Get top amenity from backend data
  const getTopAmenity = () => {
    if (hotelData?.amenities && hotelData.amenities.length > 0) {
      return {
        label: hotelData.amenities[0], // First amenity as top feature
        count: hotelData.userRating || "N/A"
      };
    }
    return {
      label: "Free Wifi", // Fallback
      count: "10"
    };
  };

  const topAmenity = getTopAmenity();

  return (
    <div className="hotel-overview-container">
      <div className="hotel-overview-container__left-section">
        <div className="left-section-row1">
          <div className="left-section-row1__flex-col col1">
            <Image
              src={
                heroImage?.image_path || "https://pix8.agoda.net/hotelImages/8348930/-1/83dd539294d01e36130154d723abc7e4.jpg?ca=10&ce=1&s=1024x768"
              }
              alt={heroImage?.alt_text || "hotel-image"}
              fill
              style={{ objectFit: "cover" }}
              sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
            />
          </div>
          <div className="left-section-row1__flex-col col2">
            <div className="sub-col">
              <Image
                src={
                  sideImages[0]?.image_path || "https://pix8.agoda.net/hotelImages/8348930/-1/3fdde253ef33cd5cd385828a571f0914.jpg?ca=10&ce=1&s=1024x768"
                }
                alt={sideImages[0]?.alt_text || "hotel-room"}
                fill
                style={{ objectFit: "cover" }}
                sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
              />
            </div>
            <div className="sub-col">
              <Image
                src={
                  sideImages[1]?.image_path || "https://pix8.agoda.net/hotelImages/8348930/-1/3fdde253ef33cd5cd385828a571f0914.jpg?ca=10&ce=1&s=1024x768"
                }
                alt={sideImages[1]?.alt_text || "hotel-room"}
                fill
                style={{ objectFit: "cover" }}
                sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
              />
            </div>
          </div>
        </div>
        <div className="left-section-row2">

          {bottomRowImages.map((image, index) => (
            <div className="left-section-row2__col" key={index}>
              <Image
                src={
                  image?.image_path || "https://pix8.agoda.net/hotelImages/8348930/-1/522586791962f1f04ba04b4dcac29a4b.jpg?ca=10&ce=1&s=1024x768"
                }
                alt={image?.alt_text || `hotel-room-${index}`}
                fill
                style={{ objectFit: "cover" }}
                sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
              />
              {index === bottomRowImages.length - 1 && remainingPhotos > 0 && (
                <div className="image-overlay" onClick={() => setIsPhotoGalleryModalAcive(true)}></div>
              )}
            </div>
          ))}

          {/* Fill remaining slots if we have fewer than 5 bottom images */}
          {Array.from({ length: Math.max(0, 5 - bottomRowImages.length) }).map((_, index) => (
            <div className="left-section-row2__col" key={`empty-${index}`}>
              <Image
                src="https://pix8.agoda.net/hotelImages/8348930/-1/522586791962f1f04ba04b4dcac29a4b.jpg?ca=10&ce=1&s=1024x768"
                alt={`placeholder-${index}`}
                fill
                style={{ objectFit: "cover" }}
                sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
              />
              {index === (5 - bottomRowImages.length - 1) && remainingPhotos > 0 && (
                <div className="image-overlay" onClick={() => setIsPhotoGalleryModalAcive(true)}></div>
              )}
            </div>
          ))}

          <div className="morePhotos">
            <i className="fa-solid fa-images"></i>
            <span className="label" onClick={() => setIsPhotoGalleryModalAcive(true)}>+{remainingPhotos} Photos</span>
          </div>
        </div>
      </div>
      <div className="hotel-overview-container__right-section">
        {/* review floater */}
        <div className="reviewFloater">
          {/* <div className="hp-gallery-score-card">
            <div className="review">
              <div className="rating-detail">
                <p className="detail detail1">{detailData.description}</p>
                <p className="detail detail2">{detailData.reviews}</p>
              </div>
              <div className="rating">{detailData.rating}</div>
            </div>
          </div> */}
          <div className="best-review-score-card">
            <div className="best-review-score-card__label">{topAmenity.label}</div>
            <div className="best-review-score-card__count">{topAmenity.count}</div>
          </div>
        </div>

        {/* map floater */}
        <div className="hotel-search-map-container" style={{ height: '86%' }}>
          {/* <HotelSearchMap 
            isDetail={true}
            detailData={{
              name: "Hotel Example",
              rating: 8.5,
              stars: "★★★★",
              position: [9.9482882, 76.3194938],
              description: "Excellent",
              reviews: "1,234 reviews",
              locationScore: "Great location - 9.2",
              address: "123 Main St, City, Country",
              attractions: [
                { name: "Beach", distance: "0.5 km" },
                { name: "City Center", distance: "1.2 km" }
              ]
            }}
            onDetailFavoriteClick={() => {}}
            onDetailCloseClick={() => {}}
            onDetailShowPrices={() => {}}
          /> */}

          {/* <HotelSearchMap
            isDetail={true}
            detailData={detailData} // Use dynamic data instead of hardcoded
            onDetailFavoriteClick={() => { }}
            onDetailCloseClick={() => { }}
            onDetailShowPrices={() => { }}
          /> */}
        </div>
      </div>

      <PhotoGalleryModal
        hotelName={hotelData?.name || "Hotel"}
        isOpen={isPhotoGalleryModalActive}
        onClose={() => setIsPhotoGalleryModalAcive(false)}
        images={images}
        hotelData={hotelData}
      />
    </div>
  );
}

export default HotelOverviewComponent;
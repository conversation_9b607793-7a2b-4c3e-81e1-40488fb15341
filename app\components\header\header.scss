@use "sass:color";
@use "/styles/variable" as *;
@use "/styles/zIndex" as *;

// $white: #ffffff;
// $dark: #333333;
// $nav-bg-start: #003366;
// $nav-bg-end: #004a9f;
// $transition-speed: 0.2s;
// $border-radius: 6px;

// .header {
//   width: 100%;
//   box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
//   background-color: $white;
//   font-family: Arial, sans-serif;
//   position: relative;

//   .top-bar {
//     display: flex;
//     align-items: center;
//     justify-content: space-between;
//     min-height: 110px;
//     padding: 0 6rem;
//     position: relative;

//     .phone-menu-btn {
//       display: none;

//       @media (max-width: $breakpoint-sm) {
//         display: flex;
//         position: absolute;
//         top: 25px;
//         right: 25px;
//         color: #333333;
//         cursor: pointer;
//       }
//     }

//     .logo {
//       cursor: pointer;
//       width: 162px;
//       height: 60px;
//       position: relative;
//       overflow: hidden;
//     }

//     .right-section {
//       display: flex;
//       align-items: center;
//       gap: 15px;

//       .dropdown {
//         position: relative;

//         &:hover .dropdown-content {
//           display: block;
//         }

//         .dropdown-button {
//           background: none;
//           border: none;
//           cursor: pointer;
//           font-size: 14px;
//           color: $dark;
//           padding: 8px 0;
//           display: flex;
//           align-items: center;
//           gap: 4px;
//           font-weight: 500;
//           transition: color $transition-speed ease;

//           i {
//             font-size: 12px;
//             margin-left: 4px;
//           }

//           &:hover {
//             color: $primary-color;
//           }
//         }

//         .dropdown-content {
//           display: none;
//           position: absolute;
//           top: 100%;
//           left: 0;
//           background-color: $white;
//           min-width: 160px;
//           box-shadow: 0 8px 20px rgba(0, 0, 0, 0.2);
//           border-radius: $border-radius;
//           overflow: hidden;
//           z-index: 1001;

//           &.last-item {
//             right: 0;
//             left: auto;
//           }

//           a,
//           .dropdown-item {
//             color: $dark;
//             padding: 12px 16px;
//             text-decoration: none;
//             display: block;
//             text-align: left;
//             font-size: 14px;
//             background: none;
//             border: none;
//             width: 100%;
//             transition: color 0.2s ease;

//             &:hover {
//               // background-color: rgba(0, 0, 0, 0.05);
//               color: $primary-color;
//             }
//           }

//           .currency-selector-container {
//             display: flex;
//             flex-wrap: wrap;
//             gap: 10px;
//             padding: 13px;
//             width: 376px;

//             @media (max-width: $breakpoint-sm) {
//               width: 200px;
//               max-height: 275px;
//               overflow-y: auto;
//             }

//             .currency-selector {
//               width: 80px;
//               display: flex;
//               justify-content: space-between;
//               border-radius: 9px;
//               padding: 5px;
//               border: 1px solid rgba(0, 0, 0, 0.1);
//               transition: border-color $transition-speed ease;
//               cursor: pointer;
//               background: none;

//               &:hover,
//               &.active {
//                 border-color: $primary-color;

//                 .currency-name {
//                   color: $primary-color;
//                 }
//               }

//               .currency-name,
//               .currency-symbol {
//                 font-size: 11px;
//               }

//               .currency-name {
//                 color: $dark;
//                 font-weight: 500;
//                 transition: color $transition-speed ease;
//               }

//               .currency-symbol {
//                 color: #ccc;
//               }
//             }
//           }
//         }
//       }

//       // Notification Bell Styles
//       .notification-bell {
//         position: relative;
//         margin-right: 5px;

//         .notification-button {
//           background: none;
//           border: none;
//           cursor: pointer;
//           color: $dark;
//           font-size: 16px;
//           padding: 8px;
//           transition: color $transition-speed ease;
//           position: relative;

//           &:hover {
//             color: $primary-color;
//           }
//         }

//         .notification-count {
//           position: absolute;
//           top: 0;
//           right: 0;
//           background-color: $primary-color;
//           color: $white;
//           font-size: 10px;
//           width: 16px;
//           height: 16px;
//           border-radius: 50%;
//           display: flex;
//           align-items: center;
//           justify-content: center;
//           font-weight: bold;
//         }

//         .notification-dropdown {
//           position: absolute;
//           top: 100%;
//           right: -70px;
//           width: 320px;
//           background-color: #fff;
//           box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
//           border-radius: 8px;
//           z-index: 1001;
//           overflow: hidden;

//           @media (max-width: $breakpoint-sm) {
//             width: 300px;
//             max-height: 275px;
//             overflow-y: auto;
//           }
//         }

//         .notification-header {
//           display: flex;
//           justify-content: space-between;
//           align-items: center;
//           padding: 12px 16px;
//           border-bottom: 1px solid #eaeaea;
//         }

//         .notification-header h3 {
//           font-size: 14px;
//           margin: 0;
//           font-weight: 600;
//           color: #333;
//         }

//         .mark-read-button {
//           background: none;
//           border: none;
//           color: $primary-color;
//           font-size: 12px;
//           cursor: pointer;
//           padding: 0;
//           font-weight: 500;
//         }

//         .mark-read-button:hover {
//           text-decoration: underline;
//         }

//         .notification-list {
//           max-height: 300px;
//           overflow-y: auto;
//         }

//         .notification-item {
//           padding: 12px 16px;
//           border-bottom: 1px solid #f0f0f0;
//           transition: background-color 0.15s ease;
//           cursor: pointer;
//         }

//         .notification-item:last-child {
//           border-bottom: none;
//         }

//         .notification-item:hover {
//           background-color: #f9f9f9;
//         }

//         .notification-item.unread {
//           background-color: rgba(177, 145, 118, 0.05);
//           position: relative;
//         }

//         .notification-item.unread:before {
//           content: "";
//           position: absolute;
//           left: 0;
//           top: 0;
//           bottom: 0;
//           width: 3px;
//           background-color: $primary-color;
//         }

//         .notification-item.unread:hover {
//           background-color: rgba(177, 145, 118, 0.08);
//         }

//         .notification-message {
//           margin: 0 0 5px;
//           font-size: 14px;
//           color: #333;
//           line-height: 1.4;
//         }

//         .notification-item.unread .notification-message {
//           font-weight: 500;
//         }

//         .notification-time {
//           font-size: 12px;
//           color: #999;
//           display: block;
//         }

//         .notification-empty {
//           padding: 30px 16px;
//           text-align: center;
//           color: #999;
//           font-size: 14px;
//         }

//         .notification-footer {
//           padding: 12px 16px;
//           text-align: center;
//           border-top: 1px solid #eaeaea;
//           background-color: #fafafa;
//         }

//         .notification-footer button {
//           background: none;
//           border: none;
//           color: $primary-color;
//           cursor: pointer;
//           font-size: 14px;
//           padding: 0;
//           font-weight: 600;
//         }

//         .notification-footer button:hover {
//           text-decoration: underline;
//         }

//         /* Scrollbar customization for webkit browsers */
//         .notification-list::-webkit-scrollbar {
//           width: 6px;
//         }

//         .notification-list::-webkit-scrollbar-track {
//           background: #f1f1f1;
//         }

//         .notification-list::-webkit-scrollbar-thumb {
//           background: #ddd;
//           border-radius: 3px;
//         }

//         .notification-list::-webkit-scrollbar-thumb:hover {
//           background: #ccc;
//         }
//       }

//       .login-button {
//         color: $dark;
//         text-decoration: none;
//         font-size: 14px;
//         padding: 6px 12px;
//         display: flex;
//         align-items: center;
//         gap: 6px;
//         font-weight: 500;
//         transition: color $transition-speed ease;
//         cursor: pointer;
//         position: relative;
//         padding-left: 27px;
//         border-left: 1px solid #ccc;

//         i {
//           font-size: 16px;
//         }

//         &:hover {
//           color: $primary-color;
//         }

//         .profile-drop-down {
//           padding: 0px;
//           width: 18.5em;
//           box-shadow: rgba(0, 0, 0, 0.2) 0px 1px 3px 1px;
//           border-radius: 0px;
//           background-color: rgb(255, 255, 255);
//           border: 1px solid rgb(221, 223, 226);

//           position: absolute;
//           top: 4px;
//           left: -140px;
//           background-color: #ffffff;
//           z-index: 1001;
//           opacity: 0;
//           visibility: hidden;
//           transform: translateY(-15px);
//           transition: all 0.4s cubic-bezier(0.165, 0.84, 0.44, 1);
//           transition-delay: 0.05s; // 👈 Add this line
//           will-change: transform, opacity; // 👈 Optional but helps
//           border: 1px solid rgba(230, 230, 230, 0.7);

//           &.active {
//             opacity: 1;
//             visibility: visible;
//             transform: translateY(0);
//           }

//           &::before {
//             content: "";
//             position: absolute;
//             top: -4px;
//             right: 15px;
//             width: 12px;
//             height: 12px;
//             background-color: #ffffff;
//             transform: rotate(45deg);
//             border-top: 1px solid rgba(230, 230, 230, 0.7);
//             border-left: 1px solid rgba(230, 230, 230, 0.7);
//           }

//           &__header {
//             display: block;
//             font-size: 12px;
//             background-color: rgb(248, 247, 249);
//             padding: 12px 24px;
//             text-transform: uppercase;
//             color: #000000;
//           }

//           &__content {
//             ul {
//               padding: 16px 12px 16px 12px;
//               li {
//                 display: flex;
//                 align-items: center;
//                 padding: 8px 12px;
//                 margin: 0;
//                 font-size: 14px;
//                 font-weight: 400;
//                 color: #2e2e2e;
//                 cursor: pointer;
//                 transition: all 0.2s ease;
//                 position: relative;

//                 &:hover {
//                   background-color: color.adjust(#ffffff, $lightness: -3%);
//                   color: $primary-color;
//                   text-decoration: underline;
//                   text-underline-offset: 1px;
//                 }

//                 &.active {
//                   background-color: color.adjust(#ffffff, $lightness: -3%);
//                   color: $primary-color;
//                   text-decoration: underline;
//                   text-underline-offset: 1px;
//                 }
//               }
//             }
//           }
//         }

//         .hotel-dropdown-overlay {
//           position: fixed;
//           inset: 0;
//           z-index: 999;
//           background-color: transparent;
//         }
//       }

//       .cart-button {
//         position: relative;
//         color: $dark;
//         display: flex;
//         align-items: center;
//         text-decoration: none;
//         transition: color $transition-speed ease;
//         padding-left: 27px;
//         border-left: 1px solid #ccc;

//         i {
//           font-size: 24px;
//         }

//         &:hover {
//           color: $primary-color;
//         }

//         .cart-count {
//           position: absolute;
//           top: -8px;
//           right: -8px;
//           background-color: $primary-color;
//           color: $white;
//           font-size: 12px;
//           width: 16px;
//           height: 16px;
//           border-radius: 50%;
//           display: flex;
//           align-items: center;
//           justify-content: center;
//           font-weight: bold;
//         }
//       }
//     }
//   }

//   .navigation {
//     .nav-container {
//       display: flex;
//       column-gap: 15px;
//       justify-content: center;
//       position: absolute;
//       bottom: -15px;
//       left: 50%;
//       transform: translateX(-50%);
//       z-index: 1000;

//       @media (max-width: $breakpoint-sm) {
//         width: 100%;
//       }

//       .nav-item {
//         color: $dark;
//         text-decoration: none;
//         min-width: 125px;
//         height: 40px;
//         display: flex;
//         align-items: center;
//         justify-content: center;
//         gap: 8px;
//         font-size: 14px;
//         font-weight: 500;
//         background-color: $light-primary;
//         transition: background-color $transition-speed, color $transition-speed;
//         border-radius: $border-radius;

//         i {
//           font-size: 16px;
//         }

//         &.active {
//           background-color: $primary-color;
//           color: $white;
//         }
//       }
//     }
//   }
// }

// // Responsive adjustments
// @media (max-width: $breakpoint-lg) {
//   .header .top-bar {
//     padding: 0 2rem;
//   }
// }

// @media (max-width: $breakpoint-md) {
//   .header {
//     .top-bar {
//       flex-direction: column;
//       padding: 1rem;
//       align-items: stretch;

//       .right-section {
//         width: 100%;
//         justify-content: center;
//         flex-wrap: wrap;
//         padding-top: 1rem;
//         margin-bottom: 15px;
//       }
//     }

//     .notification-bell .notification-dropdown {
//       width: 300px;
//       right: -20px;
//     }

//     .navigation {
//       .nav-container {
//   overflow-x: auto;
//   justify-content: flex-start;
//   padding: 0 1rem;
//   padding-bottom: 0.5rem; // Add some space above the scrollbar

//   &::-webkit-scrollbar {
//     height: 3px;
//   }

//   &::-webkit-scrollbar-thumb {
//     background-color: rgba(0, 0, 0, 0.3);
//     border-radius: 3px;
//   }
// }

//     }
//   }
// }

// @media (max-width: $breakpoint-sm) {
//   .header {
//     .top-bar {
//       .right-section {
//         display: none;
//         gap: 10px;
//         .dropdown .dropdown-button {
//           font-size: 12px;
//         }

//         .login-button {
//           font-size: 13px;

//           i {
//             font-size: 13px;
//           }
//         }

//         .notification-bell {
//           .notification-button {
//             font-size: 13px;
//           }

//           .notification-count {
//             font-size: 9px;
//             width: 11px;
//             height: 13px;
//             top: 4px;
//             right: 4px;
//           }
//         }
//       }
//     }
//   }
// }

// .rtl {
//   .dropdown-content.last-item {
//     // Reset to LTR defaults
//     left: 0;
//     right: auto;
//   }
// }

// .mobile-menu-group {
//   &__header {
//     display: block;
//     font-size: 12px;
//     background-color: rgb(248, 247, 249);
//     padding: 12px 24px;
//     text-transform: uppercase;
//     color: #000000;
//   }

//   &__content {
//     ul {
//       padding: 16px 12px 16px 12px;
//       li {
//         display: flex;
//         align-items: center;
//         padding: 8px 12px;
//         margin: 0;
//         font-size: 14px;
//         font-weight: 400;
//         color: #2e2e2e;
//         cursor: pointer;
//         transition: all 0.2s ease;
//         position: relative;

//         &:hover {
//           background-color: color.adjust(#ffffff, $lightness: -3%);
//           color: $primary-color;
//           // text-decoration: underline;
//           // text-underline-offset: 1px;
//         }

//         &.active {
//           background-color: color.adjust(#ffffff, $lightness: -3%);
//           color: $primary-color;
//           text-decoration: underline;
//           text-underline-offset: 1px;
//         }

//         .fa-solid {
//           margin-right: 10px;
//         }
//       }
//     }
//   }
// }

// .common-content-container {
//   &__header {
//     padding: 8px 12px;
//     background-color: rgb(237, 239, 243);
//     font-size: 14px;
//     font-weight: 400;
//   }

//   &__header-input {
//     position: relative;
//     width: 100%;
//     margin-top: 12px;

//     input {
//       width: 100%;
//       padding: 6px 12px 6px 36px;
//       box-sizing: border-box;
//       border: none;
//       border-radius: 4px;
//       outline: none;
//       box-shadow: none;


//       &:focus {
//         outline: none;
//         box-shadow: none;
//       }
//     }

//     .searchIcon {
//       position: absolute;
//       left: 10px;
//       top: 50%;
//       transform: translateY(-50%);
//       width: 20px;
//       height: 20px;
//       display: flex;
//       align-items: center;
//       justify-content: center;
//       font-size: 16px;
//       color: #888;
//       pointer-events: none;
//     }
//   }



//   &__content-container {
//     display: flex;
//     flex-direction: column;
//   }



//   &__content {
//     margin-top: 0px;
//     margin-bottom: 0px;
//     padding: 16px 12px;
//     -webkit-box-pack: justify;
//     justify-content: space-between;
//     -webkit-box-align: center;
//     align-items: center;
//     display: flex;
//     border-bottom: 1px solid rgb(213, 217, 226);
//     cursor: pointer;

//     .checkIcon{
//       display: none;

//       &.active{
//         display: inline-block;
//         color: rgb(62, 108, 234);
//       }




//     }

//   }

//   &__label-with-icon{
//     display: flex;
//     flex-direction: row;
//     align-items: center;

//   }

//   &__label-with-icon-description{
//     display: flex ;
//     flex-direction: column;
//   }

//   &__description{
//     font-size: 14px;
//     line-height: 17px;
//     font-weight: 400;
//     margin: 0px;

//   }

//   &__label{
//     font-size: 16px;
//     line-height: 19px;
//     font-weight: 400;
//     margin: 0px;
//   }

//   &__label-icon{
//     margin-right: 10px;
//   }

//   &__img{
//     background-repeat: no-repeat;
//     display: inline-block;
//     background-image: url(https://cdn6.agoda.net/images/desktop/bg-sprite-flags.png);
//     width: 24px;
//     height: 18px;
//     background-position: -216px -268px;
//     margin-right: 10px;
//   }


.currency-selector-container {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
  padding: 13px;
  width: 376px;

  @media (max-width: $breakpoint-sm) {
    width: 200px;
    max-height: 275px;
    overflow-y: auto;
  }

  .currency-selector {
    width: 80px;
    display: flex;
    justify-content: space-between;
    border-radius: 9px;
    padding: 5px;
    border: 1px solid rgba(0, 0, 0, 0.1);
    transition: border-color 0.2 ease;
    cursor: pointer;
    background: none;

    &:hover,
    &.active {
      border-color: $primary-color;

      .currency-name {
        color: $primary-color;
      }
    }

    .currency-name,
    .currency-symbol {
      font-size: 11px;
    }

    .currency-name {
      color: $black-color;
      font-weight: 500;
      transition: color 0.2 ease;
    }

    .currency-symbol {
      color: #ccc;
    }
  }
}

// RTL support for the header
.rtl {
  .hide-scrollbar {
    direction: rtl;
    scrollbar-width: none; /* Firefox */
  -ms-overflow-style: none; /* IE and Edge */
  }

  
.hide-scrollbar::-webkit-scrollbar {
  display: none; /* Chrome, Safari, and Opera */
}

  .currency-selector-container {
    text-align: right;
  }

  .mobile-menu-group {
    &__header {
      text-align: right;
    }

    &__content {
      ul li {
        text-align: right;

        .fa-solid {
          margin-right: 0;
          margin-left: 10px;
        }
      }
    }
  }

  .common-content-container {
    &__header {
      text-align: right;
    }

    &__content {
      text-align: right;
    }

    &__label-with-icon {
      flex-direction: row-reverse;

      .common-content-container__img {
        margin-right: 0;
        margin-left: 10px;
      }
    }

    &__label-icon {
      margin-right: 0;
      margin-left: 10px;
    }

    &__header-input {
      .searchIcon {
        left: auto;
        right: 10px;
      }

      input {
        padding: 6px 12px 6px 36px;
        text-align: right;
        padding-right: 36px;
        padding-left: 12px;
      }
    }
  }
}

@media (max-width: $isMobile) {
  .hide-at-950 {
    display: none;
  }
}

/* hide-scrollbar class */
.hide-scrollbar::-webkit-scrollbar {
  display: none;
}

.hide-scrollbar {
  -ms-overflow-style: none; /* IE & Edge */
  scrollbar-width: none;    /* Firefox */
}

// Header positioning and z-index classes
.header-main {
  position: fixed;
  top: 0;
  width: 100%;
  z-index: z-index(header);

  &.mobile-modal-open {
    z-index: 0;
  }

  @media (max-width: $breakpoint-md) {
    position: fixed;
  }
}

.header-spacer {
  height: 0;

  @media (min-width: $breakpoint-md) {
    height: 4rem; // 64px equivalent to h-16
  }
}

// Dropdown positioning classes
.dropdown-container {
  position: relative;
}

.dropdown-menu {
  position: fixed;
  z-index: z-index(dropdown);
  background: white;
  border-radius: 0.375rem; // 6px
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);

  &.language-dropdown {
    padding: 0.5rem 0;
    width: 14rem; // 224px
  }

  &.currency-dropdown {
    border-radius: 0.75rem; // 12px
    border: 1px solid #e5e7eb;
  }

  &.notification-dropdown {
    padding: 0.5rem 0;
    width: 16rem; // 256px
  }

  &.profile-dropdown {
    padding: 0.5rem 0;
    width: 12rem; // 192px
  }
}

// Overlay for dropdowns
.dropdown-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.2);
  z-index: z-index(dropdownOverlay);

  // Ensure dropdown menus can receive clicks
  .dropdown-menu {
    pointer-events: auto;
    z-index: z-index(dropdown);
  }
}

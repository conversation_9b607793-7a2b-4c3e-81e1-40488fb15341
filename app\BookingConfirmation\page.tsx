"use client";

import React, { useState, useEffect } from 'react';
import Link from 'next/link';
import { usePara<PERSON>, useRouter, useSearchParams } from 'next/navigation';
import './BookingConfirmation.scss';
import { getBookingDetails, extractBookingDisplayData } from '@/api/hotel/booking-details-service';
import { BookingDetailsApiResponse } from './booking-details-api.model';
import { HotelBookingResponse } from '../BookingPreviewPage/hotel-booking-api.model';

interface BookingDisplayData {
  confirmationNumber: string;
  bookingId?: string;
  status: string;
  paymentStatus: string;
  hotel: {
    name: string;
    address: string;
    city: string;
    country: string;
    phone: string;
    email: string;
    starRating: string;
    heroImage: string;
    checkInTime: string;
    checkOutTime: string;
  };
  booking: {
    checkIn: string;
    checkOut: string;
    nights: number;
    guests: number;
    roomType: string;
    bookingDate: string;
    bookingStatus: string;
  };
  pricing: {
    baseRate: number;
    taxes: number;
    totalAmount: number;
    currency: string;
  };
  guest: {
    name: string;
    email: string;
    phone: string;
  };
  rooms: Array<{
    id: string;
    name: string;
    description: string;
    facilities: string[];
  }>;
  cancellation: {
    refundable: boolean;
    cancellationPolicies: any[];
  };
}

export default function BookingConfirmation() {
  const params = useParams();
  const router = useRouter();
  const searchParams = useSearchParams();

  // State management
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [bookingDetails, setBookingDetails] = useState<BookingDisplayData | null>(null);
  const [bookingStatus, setBookingStatus] = useState<'success' | 'failed'>('success');
  const [currentBookingReference, setCurrentBookingReference] = useState<string | null>(null);

  useEffect(() => {
    const fetchBookingDetails = async () => {
      try {
        setIsLoading(true);
        setError(null);

        // Get booking reference from multiple sources (priority order)
        let bookingReference: string | null = null;

        // 1. Try URL search params: /BookingConfirmation?bookingReference=123
        bookingReference = searchParams && searchParams.get('bookingReference');

        // 2. Try URL path params (if using dynamic routes): /BookingConfirmation/123
        if (!bookingReference && params?.bookingReference) {
          bookingReference = params.bookingReference as string;
        }

        // 3. Fallback to localStorage (from booking creation response)
        if (!bookingReference) {
          const storedBookingResponse = localStorage.getItem('bookingResponse');
          if (storedBookingResponse) {
            try {
              const bookingResponse: HotelBookingResponse = JSON.parse(storedBookingResponse);
              bookingReference = bookingResponse.booking_reference;
            } catch (parseError) {
              console.error('Error parsing stored booking response:', parseError);
            }
          }
        }

        // if (!bookingReference) {
        //   throw new Error('No booking reference found. Please check your booking confirmation email or try again.');
        // }

        console.log('🔍 Fetching booking details for reference:', bookingReference);

        // Store the booking reference in state for use in navigation
        setCurrentBookingReference(bookingReference);

        // Fetch booking details from API
        const apiResponse = await getBookingDetails(bookingReference || "");

        // Extract and format data for display
        const displayData = extractBookingDisplayData(apiResponse);

        // Set booking status based on API response
        const isBookingSuccessful = apiResponse.status === 'CONFIRMED' || 
                                 apiResponse.status === 'COMPLETED' ||
                                 (apiResponse.status !== 'FAILED' && 
                                  apiResponse.payment_status !== 'FAILED');
        
        setBookingStatus(isBookingSuccessful ? 'success' : 'failed');
        
        // Log the booking status for debugging
        console.log('📊 Booking status:', {
          status: apiResponse.status,
          paymentStatus: apiResponse.payment_status,
          isSuccessful: isBookingSuccessful
        });

        setBookingDetails(displayData);

        console.log('✅ Booking details loaded successfully:', displayData);

      } catch (error) {
        console.error('❌ Failed to fetch booking details:', error);

        let errorMessage = 'Unable to load booking details. Please try again later.';

        if (error instanceof Error) {
          errorMessage = error.message;
        } else if (typeof error === 'object' && error !== null) {
          const apiError = error as any;
          if (apiError.response?.data?.message) {
            errorMessage = apiError.response.data.message;
          } else if (apiError.message) {
            errorMessage = apiError.message;
          }
        }

        setError(errorMessage);
        setBookingStatus('failed');
      } finally {
        setIsLoading(false);
      }
    };

    fetchBookingDetails();
  }, [params, router]);

  const handlePrint = () => {
    if (typeof window !== 'undefined') {
      window.print();
    }
  };

  const handleRetry = () => {
    // Redirect back to booking page
    router.push('/BookingPreviewPage');
  };

  const formatCurrency = (amount: number, currency: string = 'INR') => {
    if (currency === 'INR') {
      return `₹${amount.toLocaleString('en-IN', { minimumFractionDigits: 2, maximumFractionDigits: 2 })}`;
    } else if (currency === 'USD') {
      return `$${amount.toLocaleString('en-US', { minimumFractionDigits: 2, maximumFractionDigits: 2 })}`;
    }
    return `${currency} ${amount.toFixed(2)}`;
  };

  const formatDate = (dateString: string): string => {
    try {
      if (!dateString) return 'Date not available';
      
      const date = new Date(dateString);
      if (isNaN(date.getTime())) {
        console.warn('Invalid date string provided to formatDate:', dateString);
        return 'Invalid date';
      }
      
      return date.toLocaleDateString('en-US', {
        weekday: 'short',
        year: 'numeric',
        month: 'short',
        day: 'numeric'
      });
    } catch (error) {
      console.error('Error formatting date:', { dateString, error });
      return 'Date not available';
    }
  };

  // Render loading state
  const renderLoadingContent = () => (
    <div className="loading-container">
      <div className="loading-spinner"></div>
      <p className="loading-text">Loading your booking details...</p>
    </div>
  );

  // Render error state
  const renderErrorContent = () => (
    <div className="error-container">
      <i className="fa-solid fa-triangle-exclamation error-icon"></i>
      <h2 className="error-title">Unable to Load Booking Details</h2>
      <p className="error-message">{error}</p>
      <div className="error-actions">
        <button onClick={() => window.location.reload()} className="btn primary">
          <i className="fa-solid fa-rotate-right"></i> Try Again
        </button>
        <Link href="/profile" className="btn secondary">
          <i className="fa-solid fa-user"></i> My Bookings
        </Link>
        <Link href="/contact" className="btn secondary">
          <i className="fa-solid fa-headset"></i> Contact Support
        </Link>
      </div>
    </div>
  );

  // Render the success confirmation UI
  const renderSuccessContent = () => {
    if (!bookingDetails) return null;

    return (
      <>
        <div className="confirmation-header success">
          <i className="fa-solid fa-circle-check"></i>
          <h1>Booking Confirmed</h1>
          <p>Your reservation has been successfully confirmed.</p>
        </div>

        <div className="confirmation-code">
          <p>Confirmation Code: <strong>{bookingDetails.confirmationNumber}</strong></p>
        </div>

        <div className="confirmation-details">
          <div className="section">
            <h2>Reservation Details</h2>
            <div className="detail-row">
              <div className="detail-col">
                <div className="detail-item">
                  <i className="fa-solid fa-hotel"></i>
                  <div className="detail-text">
                    <label>Hotel</label>
                    <p>{bookingDetails.hotel.name}</p>
                  </div>
                </div>
              </div>
              <div className="detail-col">
                <div className="detail-item">
                  <i className="fa-solid fa-bed"></i>
                  <div className="detail-text">
                    <label>Room Type</label>
                    <p>{bookingDetails.booking.roomType}</p>
                  </div>
                </div>
              </div>
            </div>

            <div className="detail-row">
              <div className="detail-col">
                <div className="detail-item">
                  <i className="fa-solid fa-calendar-days"></i>
                  <div className="detail-text">
                    <label>Check-in</label>
                    <p>{formatDate(bookingDetails.booking.checkIn)}</p>
                  </div>
                </div>
              </div>
              <div className="detail-col">
                <div className="detail-item">
                  <i className="fa-solid fa-calendar-days"></i>
                  <div className="detail-text">
                    <label>Check-out</label>
                    <p>{formatDate(bookingDetails.booking.checkOut)}</p>
                  </div>
                </div>
              </div>
            </div>

            <div className="detail-row">
              <div className="detail-col">
                <div className="detail-item">
                  <i className="fa-solid fa-user"></i>
                  <div className="detail-text">
                    <label>Guests</label>
                    <p>{bookingDetails.booking.guests} Adults</p>
                  </div>
                </div>
              </div>
              <div className="detail-col">
                <div className="detail-item">
                  <i className="fa-solid fa-moon"></i>
                  <div className="detail-text">
                    <label>Length of Stay</label>
                    <p>{bookingDetails.booking.nights} Nights</p>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <div className="section">
            <h2>Payment Summary</h2>
            <div className="payment-summary">
              <div className="payment-row">
                <span>Room Rate ({bookingDetails.booking.nights} nights)</span>
                <span>{formatCurrency(bookingDetails.pricing.baseRate, bookingDetails.pricing.currency)}</span>
              </div>
              <div className="payment-row">
                <span>Taxes & Fees</span>
                <span>{formatCurrency(bookingDetails.pricing.taxes, bookingDetails.pricing.currency)}</span>
              </div>
              <div className="payment-row total">
                <span>Total Amount</span>
                <span>{formatCurrency(bookingDetails.pricing.totalAmount, bookingDetails.pricing.currency)}</span>
              </div>
              <div className="payment-method">
                <span>Payment Status:</span>
                <span>{bookingDetails.paymentStatus}</span>
              </div>
            </div>
          </div>

          <div className="section info-section">
            <div className="info-item">
              <i className="fa-solid fa-circle-info"></i>
              <p>Check-in time starts at {bookingDetails.hotel.checkInTime}, and check-out time is until {bookingDetails.hotel.checkOutTime}.</p>
            </div>
            <div className="info-item">
              <i className="fa-solid fa-envelope"></i>
              <p>A confirmation email has been sent to {bookingDetails.guest.email}.</p>
            </div>
            {bookingDetails.hotel.phone && (
              <div className="info-item">
                <i className="fa-solid fa-phone"></i>
                <p>Hotel Contact: {bookingDetails.hotel.phone}</p>
              </div>
            )}
          </div>

          <div className="actions">
            <Link
              href={currentBookingReference ? `/Itinerary?bookingReference=${currentBookingReference}` : "/Itinerary"}
              className="btn secondary"
            >
              View Itinerary
            </Link>
            <Link href="/profile" className="btn secondary">
              My Bookings
            </Link>
            <button onClick={handlePrint} className="btn primary">
              <i className="fa-solid fa-print"></i> Print
            </button>
          </div>
        </div>
      </>
    );
  };

  // Render the booking failed UI
  const renderFailedContent = () => {
    if (!bookingDetails) return null;

    return (
      <>
        <div className="confirmation-header failed">
          <i className="fa-solid fa-circle-xmark"></i>
          <h1>Booking Failed</h1>
          <p>We encountered a problem with your reservation.</p>
        </div>

        <div className="error-message">
          <p>{error || 'Your booking could not be completed at this time.'}</p>
        </div>

        <div className="confirmation-details">
          <div className="section">
            <h2>Booking Details</h2>
            <div className="detail-row">
              <div className="detail-col">
                <div className="detail-item">
                  <i className="fa-solid fa-hotel"></i>
                  <div className="detail-text">
                    <label>Hotel</label>
                    <p>{bookingDetails.hotel.name}</p>
                  </div>
                </div>
              </div>
              <div className="detail-col">
                <div className="detail-item">
                  <i className="fa-solid fa-bed"></i>
                  <div className="detail-text">
                    <label>Room Type</label>
                    <p>{bookingDetails.booking.roomType}</p>
                  </div>
                </div>
              </div>
            </div>

            <div className="detail-row">
              <div className="detail-col">
                <div className="detail-item">
                  <i className="fa-solid fa-calendar-days"></i>
                  <div className="detail-text">
                    <label>Check-in</label>
                    <p>{formatDate(bookingDetails.booking.checkIn)}</p>
                  </div>
                </div>
              </div>
              <div className="detail-col">
                <div className="detail-item">
                  <i className="fa-solid fa-calendar-days"></i>
                  <div className="detail-text">
                    <label>Check-out</label>
                    <p>{formatDate(bookingDetails.booking.checkOut)}</p>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <div className="section payment-failed-section">
            <div className="info-item">
              <i className="fa-solid fa-triangle-exclamation"></i>
              <p>Your booking was not completed. Please try again or use a different payment method.</p>
            </div>
            <div className="info-item">
              <i className="fa-solid fa-credit-card"></i>
              <p>No charges have been made to your payment method.</p>
            </div>
            {bookingDetails.paymentStatus && (
              <div className="info-item">
                <i className="fa-solid fa-info-circle"></i>
                <p>Payment Status: {bookingDetails.paymentStatus}</p>
              </div>
            )}
          </div>

          <div className="actions">
            <button onClick={handleRetry} className="btn primary">
              <i className="fa-solid fa-rotate-right"></i> Try Again
            </button>
            <Link href="/contact" className="btn secondary">
              <i className="fa-solid fa-headset"></i> Contact Support
            </Link>
          </div>
        </div>
      </>
    );
  };

  // Main render logic
  const renderContent = () => {
    if (isLoading) {
      return renderLoadingContent();
    }

    if (error && !bookingDetails) {
      return renderErrorContent();
    }

    if (bookingStatus === 'success') {
      return renderSuccessContent();
    } else {
      return renderFailedContent();
    }
  };

  return (
    <div className={`booking-confirmation ${bookingStatus}`}>
      {renderContent()}

      {/* Only show footer if not in loading or error state */}
      {!isLoading && (error ? bookingDetails : true) && (
        <div className="footer">
          <p>Thank you for choosing KindAli</p>
          <div className="contact">
            <span><i className="fa-solid fa-phone"></i> **************</span>
            <span><i className="fa-solid fa-envelope"></i> <EMAIL></span>
          </div>
        </div>
      )}
    </div>
  );
}


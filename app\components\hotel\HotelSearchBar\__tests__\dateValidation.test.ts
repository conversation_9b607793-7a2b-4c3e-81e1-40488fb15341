import { describe, it, expect, beforeEach, jest } from '@jest/globals';

// Mock the useTranslation hook
const mockT = jest.fn((key: string) => {
  const translations: Record<string, string> = {
    'search.validation.checkin_past': 'Check-in date cannot be in the past',
    'search.validation.checkout_before_checkin': 'Check-out date must be after check-in date'
  };
  return translations[key] || key;
});

jest.mock('@/app/hooks/useTranslation', () => ({
  useTranslation: () => ({ t: mockT })
}));

// Import the validation function - we'll need to extract it or test it through the component
// For now, let's create a standalone version for testing
const validateAndCorrectDates = (
  checkInDate: string | null, 
  checkOutDate: string | null,
  t: (key: string) => string
): { 
  checkInDate: string, 
  checkOutDate: string, 
  hasError: boolean, 
  errorMessage?: string 
} => {
  const today = new Date();
  today.setHours(0, 0, 0, 0);
  
  const tomorrow = new Date(today);
  tomorrow.setDate(today.getDate() + 1);

  // If no dates provided, set default dates
  if (!checkInDate || !checkOutDate) {
    return {
      checkInDate: today.toISOString(),
      checkOutDate: tomorrow.toISOString(),
      hasError: false
    };
  }

  const checkIn = new Date(checkInDate);
  const checkOut = new Date(checkOutDate);
  
  // Set time to midnight for accurate comparison
  checkIn.setHours(0, 0, 0, 0);
  checkOut.setHours(0, 0, 0, 0);

  // Check if check-in date is in the past
  if (checkIn < today) {
    return {
      checkInDate: today.toISOString(),
      checkOutDate: tomorrow.toISOString(),
      hasError: true,
      errorMessage: t("search.validation.checkin_past") || "Check-in date cannot be in the past"
    };
  }

  // Check if check-out date is before or same as check-in date
  if (checkOut <= checkIn) {
    const correctedCheckOut = new Date(checkIn);
    correctedCheckOut.setDate(checkIn.getDate() + 1);
    return {
      checkInDate: checkIn.toISOString(),
      checkOutDate: correctedCheckOut.toISOString(),
      hasError: true,
      errorMessage: t("search.validation.checkout_before_checkin") || "Check-out date must be after check-in date"
    };
  }

  // Dates are valid
  return {
    checkInDate: checkIn.toISOString(),
    checkOutDate: checkOut.toISOString(),
    hasError: false
  };
};

describe('Date Validation', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('validateAndCorrectDates', () => {
    it('should return default dates when no dates provided', () => {
      const result = validateAndCorrectDates(null, null, mockT);
      
      expect(result.hasError).toBe(false);
      expect(result.checkInDate).toBeDefined();
      expect(result.checkOutDate).toBeDefined();
      
      const checkIn = new Date(result.checkInDate);
      const checkOut = new Date(result.checkOutDate);
      expect(checkOut.getTime() - checkIn.getTime()).toBe(24 * 60 * 60 * 1000); // 1 day difference
    });

    it('should correct past check-in date', () => {
      const yesterday = new Date();
      yesterday.setDate(yesterday.getDate() - 1);
      const tomorrow = new Date();
      tomorrow.setDate(tomorrow.getDate() + 1);

      const result = validateAndCorrectDates(
        yesterday.toISOString(), 
        tomorrow.toISOString(), 
        mockT
      );
      
      expect(result.hasError).toBe(true);
      expect(result.errorMessage).toBe('Check-in date cannot be in the past');
      
      const correctedCheckIn = new Date(result.checkInDate);
      const today = new Date();
      today.setHours(0, 0, 0, 0);
      correctedCheckIn.setHours(0, 0, 0, 0);
      
      expect(correctedCheckIn.getTime()).toBeGreaterThanOrEqual(today.getTime());
    });

    it('should correct checkout date when it is before checkin date', () => {
      const today = new Date();
      const tomorrow = new Date();
      tomorrow.setDate(today.getDate() + 1);
      const dayAfterTomorrow = new Date();
      dayAfterTomorrow.setDate(today.getDate() + 2);

      // Set checkout before checkin (reversed dates)
      const result = validateAndCorrectDates(
        dayAfterTomorrow.toISOString(), 
        tomorrow.toISOString(), 
        mockT
      );
      
      expect(result.hasError).toBe(true);
      expect(result.errorMessage).toBe('Check-out date must be after check-in date');
      
      const correctedCheckIn = new Date(result.checkInDate);
      const correctedCheckOut = new Date(result.checkOutDate);
      
      expect(correctedCheckOut.getTime()).toBeGreaterThan(correctedCheckIn.getTime());
    });

    it('should correct checkout date when it equals checkin date', () => {
      const today = new Date();
      today.setDate(today.getDate() + 1); // Tomorrow to avoid past date issue

      const result = validateAndCorrectDates(
        today.toISOString(), 
        today.toISOString(), 
        mockT
      );
      
      expect(result.hasError).toBe(true);
      expect(result.errorMessage).toBe('Check-out date must be after check-in date');
      
      const correctedCheckIn = new Date(result.checkInDate);
      const correctedCheckOut = new Date(result.checkOutDate);
      
      expect(correctedCheckOut.getTime()).toBeGreaterThan(correctedCheckIn.getTime());
      expect(correctedCheckOut.getTime() - correctedCheckIn.getTime()).toBe(24 * 60 * 60 * 1000); // 1 day difference
    });

    it('should return valid dates unchanged', () => {
      const checkIn = new Date();
      checkIn.setDate(checkIn.getDate() + 1); // Tomorrow
      const checkOut = new Date();
      checkOut.setDate(checkOut.getDate() + 3); // Day after tomorrow

      const result = validateAndCorrectDates(
        checkIn.toISOString(), 
        checkOut.toISOString(), 
        mockT
      );
      
      expect(result.hasError).toBe(false);
      expect(result.errorMessage).toBeUndefined();
      
      const resultCheckIn = new Date(result.checkInDate);
      const resultCheckOut = new Date(result.checkOutDate);
      
      resultCheckIn.setHours(0, 0, 0, 0);
      resultCheckOut.setHours(0, 0, 0, 0);
      checkIn.setHours(0, 0, 0, 0);
      checkOut.setHours(0, 0, 0, 0);
      
      expect(resultCheckIn.getTime()).toBe(checkIn.getTime());
      expect(resultCheckOut.getTime()).toBe(checkOut.getTime());
    });
  });
});

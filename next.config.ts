import type { NextConfig } from "next";

const nextConfig: NextConfig = {
  i18n: {
    defaultLocale: 'en',
    locales: ['en', 'ar', 'es', 'fr', 'hi'],
    localeDetection: false,
  },
  // basePath: "/hotel",
  // trailingSlash: true,

  webpack(config) {
    config.module?.rules?.push({
      test: /\.svg$/,
      use: ["@svgr/webpack"],
    });
    return config;
  },
  images: {
    remotePatterns: [
      { protocol: "https", hostname: "pix8.agoda.net" },
      { protocol: "https", hostname: "q-xx.bstatic.com" },
      { protocol: "https", hostname: "media.istockphoto.com" },
      { protocol: "https", hostname: "images.ixigo.com" },
      { protocol: "https", hostname: "www.travelguru.com" },
      { protocol: "https", hostname: "images.via.com" },
      { protocol: "https", hostname: "static.vecteezy.com" },
      { protocol: "https", hostname: "www.traveltrendstoday.in" },
      { protocol: "https", hostname: "upload.wikimedia.org" },
      { protocol: "https", hostname: "www.indianatravelservices.com" },
      { protocol: "https", hostname: "tourismnotes.com" },
      { protocol: "https", hostname: "encrypted-tbn0.gstatic.com" },
      { protocol: "https", hostname: "cdn4.iconfinder.com" },
      { protocol: "https", hostname: "img.favpng.com" },
      { protocol: "https", hostname: "www.citypng.com" },
      { protocol: "https", hostname: "logowik.com" },
      { protocol: "https", hostname: "www.freepnglogos.com" },
      { protocol: "https", hostname: "cdn.example.com" },
      { protocol: "https", hostname: "i.travelapi.com" },
      // Common CDN patterns for hotel images
      { protocol: "https", hostname: "*.cloudfront.net" },
      { protocol: "https", hostname: "*.amazonaws.com" },
      { protocol: "https", hostname: "images.unsplash.com" },
      { protocol: "https", hostname: "plus.unsplash.com" },
      { protocol: "https", hostname: "i.travelapi.com" },
      { protocol: "https", hostname: "cdn.worldota.net" },
      { protocol: "https", hostname: "via.placeholder.com" },
      
    ],
  },
  eslint: {
    ignoreDuringBuilds: true,
  },
};

export default nextConfig;

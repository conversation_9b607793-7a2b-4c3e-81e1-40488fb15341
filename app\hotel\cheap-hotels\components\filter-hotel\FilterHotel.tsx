import { useCommonContext } from '@/app/contexts/commonContext';
import { useTranslation } from '@/app/hooks/useTranslation';
import BudgetSlider from '@/app/HotelSearchResult/components/HotelFilter/components/BudgetSlider/BudgetSlider';
import { resetActiveFilters } from '@/helpers/hotel/filter-helper';
import { AccommodationTypeItem, AmenityItem, HotelFilterData, StarRatingItem, UserRatingItem } from '@/models/hotel/filter.model';
import React, { useCallback, useEffect, useRef } from 'react'
import { FilterSection } from './components/FilterSection';
import './filter-hotel.scss'

interface FilterHotelProps {
    filterData: HotelFilterData | undefined;
    handleChange: (data: HotelFilterData | undefined) => void;
    priceDistribution?: number[];
    showCounts?: boolean;
}

function FilterHotel({filterData,handleChange,priceDistribution,showCounts = false,}:FilterHotelProps) {
    const { t } = useTranslation();
    const { selectedCurrency } = useCommonContext();
    const filterDataRef = useRef(filterData);

    const handleReset = useCallback(() => {
        const data = filterDataRef.current; 
        if(!data){
            return ; 
        }
        const resetData = resetActiveFilters(data);
        handleChange(resetData);
    },[handleChange])

    const isResetEnabled = (): boolean => {
        const data = filterDataRef.current;

        if (!data) return false;

        return (
            data.isPriceRangeChanged ||
            data.popularFiltersAnyActive ||
            data.userRatingAnyActive ||
            data.accommodationTypesAnyActive ||
            data.amenitiesAnyActive ||
            data.hotelStarRatingAnyActive
        );
    };

    const handlePriceRangeChange = useCallback((newMin: number, newMax: number) => {
      const data = filterDataRef.current;
      if (!data) return;

      const newFilterData = JSON.parse(JSON.stringify(data));
      newFilterData.priceRange.values.minimum = newMin;
      newFilterData.priceRange.values.maximum = newMax;
      
      // Check if the price range has been changed from its original state
      const isChanged = newMin !== data.priceRange.min || newMax !== data.priceRange.max;
      newFilterData.isPriceRangeChanged = isChanged;
      
      handleChange(newFilterData);
    }, [handleChange]);

    const handleCheckboxChange = useCallback((category: string, key: string | number, isChecked: boolean) => {
      const data = filterDataRef.current;
      if (!data) return;

      const newFilterData = JSON.parse(JSON.stringify(data));

      switch (category) {
        case 'userRating':
          newFilterData.userRating.forEach((item: UserRatingItem) => {
            if (item.key === key) {
              item.isSelected = isChecked;
            }
          });
          newFilterData.userRatingAnyActive = newFilterData.userRating.some((item: UserRatingItem) => item.isSelected);
          break;
        case 'accommodationTypes':
          newFilterData.accommodationTypes.forEach((item: AccommodationTypeItem) => {
            if (item.key === key) {
              item.isSelected = isChecked;
            }
          });
          newFilterData.accommodationTypesAnyActive = newFilterData.accommodationTypes.some((item: AccommodationTypeItem) => item.isSelected);
          break;
        case 'amenities':
          newFilterData.amenities.forEach((item: AmenityItem) => {
            if (item.key === key) {
              item.isSelected = isChecked;
            }
          });
          newFilterData.amenitiesAnyActive = newFilterData.amenities.some((item: AmenityItem) => item.isSelected);
          break;
        case 'hotelStarRating':
          newFilterData.hotelStarRating.forEach((item: StarRatingItem) => {
            if (item.key === key) {
              item.isSelected = isChecked;
            }
          });
          newFilterData.hotelStarRatingAnyActive = newFilterData.hotelStarRating.some((item: StarRatingItem) => item.isSelected);
          break;
        // You can add cases for other popular filters here as needed.
        case 'freeCancellation':
        case 'breakfastIncluded':
        case 'payAtHotel':
        case 'petsAllowed':
            newFilterData.popularFilters[category].isSelected = isChecked;
            const popularAnyActive = 
                newFilterData.popularFilters.freeCancellation.isSelected ||
                newFilterData.popularFilters.breakfastIncluded.isSelected ||
                newFilterData.popularFilters.payAtHotel.isSelected ||
                newFilterData.popularFilters.petsAllowed.isSelected;
            newFilterData.popularFiltersAnyActive = popularAnyActive;
            break;
        default:
          break;
      }
      
      handleChange(newFilterData);
    }, [handleChange]);

    useEffect(() => {
        filterDataRef.current = filterData;
    }, [filterData])

  return (
    <div className='hotel-filter-container'>
        <div className='filter-head'>
            <h3>{t("filters.filterBy")}</h3>
            <button onClick={handleReset} className={`resetBtn ${isResetEnabled() ? "enabled" : ""}`} disabled={!isResetEnabled()}>
                {t("filters.clear")}
            </button>
        </div>

        <div className="filter-body">
            <BudgetSlider
                priceRange={filterData?.priceRange || { min: 0, max: 100, values: { minimum: 0, maximum: 100 } }}
                onMinChange={(min) => handlePriceRangeChange(min, filterData?.priceRange.values.maximum || 100)}
                onMaxChange={(max) => handlePriceRangeChange(filterData?.priceRange.values.minimum || 0, max)}
                currency={selectedCurrency.symbol}
                histogramData={priceDistribution || []}
            />

            <FilterSection
                title={t("filters.popular")}
                items={Object.entries(filterData?.popularFilters || {}).map(([key, value]) => ({...value, key}))}
                category="popularFilters"
                onCheckboxChange={(cat, k, isChecked) => handleCheckboxChange(k as string, k as string, isChecked)}
                showCounts={showCounts}
                t={t}
            />
            
            <FilterSection
                title={t("filters.rating")}
                items={filterData?.hotelStarRating || []}
                category="hotelStarRating"
                onCheckboxChange={handleCheckboxChange}
                showCounts={showCounts}
                t={t}
            />

            {/* Reusable FilterSection for User Ratings */}
            <FilterSection
                title={t("filters.userRating")}
                items={filterData?.userRating || []}
                category="userRating"
                onCheckboxChange={handleCheckboxChange}
                showCounts={showCounts}
                t={t}
            />
            
            <FilterSection
                title={t("filters.accommodationTypes")}
                items={filterData?.accommodationTypes || []}
                category="accommodationTypes"
                onCheckboxChange={handleCheckboxChange}
                showCounts={showCounts}
                t={t}
            />


            {filterData?.amenities && filterData?.amenities.length > 0 && (
                <FilterSection
                    title={t("filters.amenities")}
                    items={filterData?.amenities || []}
                    category="amenities"
                    onCheckboxChange={handleCheckboxChange}
                    showCounts={showCounts}
                    t={t}
                />   
            )}
         
        </div>

    </div>
  )
}

export default FilterHotel
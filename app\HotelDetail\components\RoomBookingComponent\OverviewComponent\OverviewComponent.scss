@use "/styles/variable" as *;

.overview-container {
  display: flex;
  flex-direction: column;
  padding: 30px 0;

  .about-section,
  .facilities-section {
    .overview-header {
      margin: 0 0 15px 0;
      font-size: 22px;
      font-weight: 700;
      color: #17181c;
      margin-bottom: 25px;

      @media (max-width: $breakpoint-md) {
        font-size: 18px;
      }

      @media (max-width: $breakpoint-sm) {
        font-size: 16px;
      }

      @media (max-width: $breakpoint-xs) {
        font-size: 15px;
      }
    }

    .overviewBtn {
      font-size: 16px;
      font-weight: 600;
      display: flex;
      flex-direction: row;
      align-items: center;
      color: $primary_color;
      gap: 5px;
      cursor: pointer;

      @media (max-width: $breakpoint-sm) {
        font-size: 15px;
      }

      @media (max-width: $breakpoint-xs) {
        font-size: 14px;
      }
    }
  }

  .about-section {
    display: flex;
    flex-direction: column;
    margin: 0 0 30px 0;
    .overview-description {
      font-size: 16px;
      font-weight: 500;
      color: #17181c;
      margin-bottom: 15px;

      @media (max-width: $breakpoint-sm) {
        font-size: 14px;
      }
    }
  }

  // Mobile Rating and Location Section
  .mobile-rating-location-section {
    display: flex;
    flex-direction: column;
    gap: 20px;
    margin: 10px 0 20px 0;
    padding: 20px;
    background-color: #f8f9fa;
    border-radius: 12px;

    .mobile-review,
    .mobile-location {
      display: flex;
      flex-direction: row;
      align-items: center;
    }

    .mobile-review {
      gap: 12px;

      .rating {
        padding: 10px;
        background-color: #17181c;
        font-size: 16px;
        color: #fafafa;
        font-weight: 700;
        border-radius: 10px;
        display: flex;
        justify-content: center;
        align-items: center;
        min-width: 45px;
      }

      .rating-detail {
        flex: 1;

        .detail1 {
          font-size: 15px;
          font-weight: 600;
          color: #17181c;
          margin: 0 0 2px 0;
        }

        .detail2 {
          font-size: 13px;
          font-weight: 600;
          color: #5e616e;
          margin: 0;
        }
      }
    }

    .mobile-location {
      gap: 12px;

      .icon {
        background-color: rgba(0, 59, 149, 0.05);
        color: $primary-color;
        font-size: 20px;
        padding: 10px;
        border-radius: 8px;
        display: flex;
        align-items: center;
        justify-content: center;
        min-width: 40px;
        min-height: 40px;
      }

      .details {
        flex: 1;

        .detail1 {
          font-size: 14px;
          font-weight: 500;
          color: #17181c;
          margin: 0 0 2px 0;
          line-height: 1.3;
        }

        .detail2 {
          color: $primary-color;
          font-size: 13px;
          font-weight: 500;
          cursor: pointer;
          margin: 0;

          &:hover {
            text-decoration: underline;
          }
        }
      }
    }
  }

  .facilities-section {
    .popular-facilties {
      margin: 5px 0 10px 0;
      display: flex;
      flex-direction: row;
      gap: 30px;
    

      @media (max-width: $breakpoint-lg) {
        gap: 25px;
      }

      @media (max-width: $isMobile) {
        flex-wrap: wrap;
          justify-content: start;
      }

      .facility {
        display: flex;
        flex-direction: column;
        width: 110px;
        align-items: center;

        @media (max-width: $isMobile) {
          width: 95px;
        }

        .icon {
          width: 48px;
          height: 48px;
          border-radius: 50%;
          display: flex;
          align-items: center;
          justify-content: center;
          font-size: 22px;
          background-color: #f4f5f5;
          margin: 0 0 10px 0;

          .fa-solid {
            font-size: 16px;
          }
        }

        .label {
          text-align: center;
          font-size: 16px;
          font-weight: 500;
          line-height: normal;

          @media (max-width: $breakpoint-sm) {
            font-size: 14px;
          }

          @media (max-width: $breakpoint-xs) {
            font-size: 13px;
          }
        }
      }
    }
  }

  // Map Section
  .map-section {
    margin: 30px 0;

    .overview-header {
      margin: 0 0 15px 0;
      font-size: 22px;
      font-weight: 700;
      color: #17181c;
      margin-bottom: 25px;

      @media (max-width: $breakpoint-md) {
        font-size: 18px;
      }

      @media (max-width: $breakpoint-sm) {
        font-size: 16px;
      }

      @media (max-width: $breakpoint-xs) {
        font-size: 15px;
      }
    }

    .map-preview-container {
      .map-preview {
        position: relative;
        border-radius: 8px;
        overflow: hidden;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        margin-bottom: 15px;

        .map-overlay {
          position: absolute;
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;
          display: flex;
          align-items: center;
          justify-content: center;
          background: rgba(0, 0, 0, 0.1);
          transition: background 0.2s ease;

          &:hover {
            background: rgba(0, 0, 0, 0.2);
          }

          .show-map-button {
            display: flex;
            align-items: center;
            gap: 8px;
            padding: 12px 20px;
            background: $primary-color;
            color: white;
            border: none;
            border-radius: 25px;
            font-size: 14px;
            font-weight: 600;
            cursor: pointer;
            box-shadow: 0 4px 12px rgba(0, 59, 149, 0.3);
            transition: all 0.2s ease;

            &:hover {
              background: darken($primary-color, 10%);
              transform: translateY(-1px);
              box-shadow: 0 6px 16px rgba(0, 59, 149, 0.4);
            }

            .mr-2 {
              margin-right: 8px;
            }
          }
        }
      }

      .location-info {
        .location-address {
          display: flex;
          align-items: center;
          gap: 10px;
          font-size: 14px;
          color: #666;

          i {
            color: $primary-color;
            font-size: 16px;
          }

          span {
            line-height: 1.4;
          }
        }
      }
    }

    .map-expanded-container {
      .map-expanded-header {
        display: flex;
        justify-content: flex-end;
        margin-bottom: 15px;

        .collapse-map-button {
          display: flex;
          align-items: center;
          gap: 8px;
          padding: 8px 16px;
          background: #f8f9fa;
          color: #666;
          border: 1px solid #e0e0e0;
          border-radius: 20px;
          font-size: 14px;
          font-weight: 500;
          cursor: pointer;
          transition: all 0.2s ease;

          &:hover {
            background: #e9ecef;
            color: #333;
          }

          i {
            font-size: 12px;
          }
        }
      }
    }
  }
}

import { HotelBookingRequest, RoomAllocation, Guest, BillingContact, ContactInfo, Address, City, State, Country } from "../hotel-booking-api.model";
import { getStoredBookingInitData } from "./bookingInitUtils";

/**
 * Transform guest details form data and existing booking data into API payload
 * Primarily uses booking init API data for system identifiers
 */
export const transformToBookingRequest = (
  formData: any,
  searchKey?: string,
  hotelId?: string,
  userId?: number
): HotelBookingRequest => {
  // ✅ PRIMARY SOURCE: Get booking init API data (what we actually use)
  const bookingInitData = getStoredBookingInitData();

  // Use provided values or fallback to stored values
  const finalSearchKey = searchKey || "dubai-06-august-async-test-8"; // fallback for testing
  const finalHotelId = hotelId || "70480726"; // fallback for testing
  const finalUserId = userId || 123; // fallback for testing

  // Transform guest rooms data
  const roomsAllocations: RoomAllocation[] = formData.guestRooms.map((room: any, index: number) => {
    // ✅ PRIMARY SOURCE: Get room and rate IDs from booking init API data
    const roomId = bookingInitData?.data?.hotel?.rooms?.[index]?.id || 
                   `room-${index}-${Date.now()}`; // fallback
    const rateId = bookingInitData?.data?.hotel?.rates?.[index]?.id || 
                   "be385717-3047-42e1-891e-971f06310cc3"; // fallback

    // Transform guests for this room
    const guests: Guest[] = [{
      type: "Adult" as const,
      title: room.title.replace(".", "") as "Mr" | "Mrs" | "Miss" | "Dr" | "Prof",
      firstname: room.firstName,
      lastname: room.lastName,
      age: 25, // Default age - you might want to add age field to the form
      email: formData.email // Use contact email for all guests
    }];

    return {
      roomid: roomId,
      rateid: rateId,
      guests
    };
  });

  // Transform billing contact
  const billingContact: BillingContact = {
    title: formData.contactTitle.replace(".", ""),
    firstName: formData.firstName || formData.guestRooms[0]?.firstName || "Test",
    lastName: formData.lastName || formData.guestRooms[0]?.lastName || "User",
    age: 30, // Default age
    contact: {
      phone: formData.mobile,
      email: formData.email,
      address: {
        line1: "Default Address Line 1", // You might want to add address fields to the form
        line2: "Default Address Line 2",
        city: {
          name: "",
          code: ""
        },
        state: {
          name: "",
          code: ""
        },
        country: {
          name: formData.nationality || "India",
          code: "IN"
        },
        postalCode: ""
      }
    }
  };

  // ✅ PRIMARY SOURCE: Get rate IDs from booking init API data
  const rateIds = bookingInitData?.data?.hotel?.rates?.map(rate => rate.id) || 
                  ["be385717-3047-42e1-891e-971f06310cc3"]; // fallback

  return {
    search_key: finalSearchKey,
    hotel_id: finalHotelId,
    rateIds,
    user_id: finalUserId,
    roomsAllocations,
    billingContact
  };
};

/**
 * Validate that all required data is available for booking
 * Only checks for booking init API data since that's what's actually used
 */
export const validateBookingDataAvailability = (): {
  isValid: boolean;
  missingData: string[];
} => {
  const missingData: string[] = [];
  
  // ✅ ONLY check booking init API data (what we actually use for booking)
  const bookingInitData = getStoredBookingInitData();

  if (!bookingInitData) {
    missingData.push("Booking initialization data");
  }

  // Note: Form data validation is handled separately by the form component
  // localStorage data (selectedRoom, hotelDetails) is not used in final booking creation

  return {
    isValid: missingData.length === 0,
    missingData
  };
};

/**
 * Get default values for testing/development
 */
export const getDefaultBookingData = (): Partial<HotelBookingRequest> => {
  return {
    search_key: "dubai-06-august-async-test-8",
    hotel_id: "70480726",
    rateIds: ["be385717-3047-42e1-891e-971f06310cc3"],
    user_id: 123
  };
};

/**
 * Helper function to validate booking request payload
 */
export const validateBookingRequest = (payload: HotelBookingRequest): boolean => {
  // Basic validation checks
  if (!payload.search_key || !payload.hotel_id || !payload.rateIds || payload.rateIds.length === 0) {
    return false;
  }

  if (!payload.roomsAllocations || payload.roomsAllocations.length === 0) {
    return false;
  }

  if (!payload.billingContact || !payload.billingContact.contact) {
    return false;
  }

  // Validate each room allocation
  for (const room of payload.roomsAllocations) {
    if (!room.roomid || !room.rateid || !room.guests || room.guests.length === 0) {
      return false;
    }

    // Validate each guest
    for (const guest of room.guests) {
      if (!guest.firstname || !guest.lastname || !guest.email || !guest.title) {
        return false;
      }
    }
  }

  return true;
};

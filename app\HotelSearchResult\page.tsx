// "use client";
// import React, { useCallback, useEffect, useState } from "react";
// import "./HotelSearchResult.scss";
// import HotelFilter from "./components/HotelFilter/HotelFilter";
// import HotelCard from "./components/HotelCard/HotelCard";
// import FilterShimmer from "./ShimmerLoaders/FilterShimmer/FilterShimmer";
// import SortBarShimmer from "./ShimmerLoaders/SortBarShimmer/SortBarShimmer";
// import HotelCardShimmer from "./ShimmerLoaders/HotelCardShimmer/HotelCardShimmer";
// import HotelGridShimmer from "./ShimmerLoaders/HotelGridShimmer/HotelGridShimmer";
// import { Hotel, HotelListResponse } from "./hotel-search-result.model";
// import axios from "axios";
// import HotelSearchBar from "./components/HotelSearchBar/HotelSearchBar";
// import { addFilterInList } from "@/app/components/utilities/helpers/hotel/hotelFilterService";
// import {
//   createHotelFilters,
//   HotelFilterData,
// } from "@/app/components/utilities/helpers/hotel/filterHotels";
// import { useCommonContext } from "../contexts/commonContext";
// import useScrollLock from "../components/utilities/ScrollLock/useScrollLock";
// import { useTranslation } from "@/app/hooks/useTranslation";
// import HotelSearchMap from "./components/HotelSearchMap/HotelSearchMap";

// const sortOptions: string[] = [
//   "Top picks for long stays",
//   "Homes & Apartments first",
//   "Price (lowest first)",
//   "Price (highest first)",
//   "Best reviewed and lowest price",
//   "Property rating (high to low)",
//   "Property rating (low to high)",
//   "Property rating and price",
//   "Distance from city centre",
//   "Top reviewed",
// ];

// const defaultSort = "Top picks for long stays";

// const API_URL = "/data/hotels.json";

// function Page() {
//   const { t } = useTranslation();
//   const [hotels, setHotels] = useState<Hotel[]>([]);
//   const [isSortTopPopUpActive, setIsSortTopPopUpActive] =
//     useState<boolean>(false);
//   const [selectedSort, setSelectedSort] = useState<string>(sortOptions[0]);
//   const [viewMode, setViewMode] = useState<"list" | "grid">("list");
//   const [loading, setLoading] = useState<boolean>(true);
//   const [isViewModeLoaded, setIsViewModeLoaded] = useState<boolean>(false);
//   const [filterData, setFilterData] = useState<HotelFilterData | undefined>(
//     undefined
//   );
//   const { setIsLoading } = useCommonContext();
//   const [filteredHotels, setFilteredHotels] = useState<Hotel[]>([]);
//   const { setHotelSearchFormData, hotelSearchFormData } = useCommonContext();
//   const [isMobileFilterActive, setIsMobileFilterActive] =
//     useState<boolean>(false);
//   const [isMobileSortFilterActive, setIsMobileSortFilterActive] =
//     useState<boolean>(false);
//   const [isMobileSearchBarActive, setIsMobileSearchBarActive] =
//     useState<boolean>(false);
//   const [activeMobileFilterNav, setActiveMobileFilterNav] =
//     useState<string>("Sort by");
//   const [isNotMobile, setIsNotMobile] = useState<boolean>(false);
//   const [selectedHotels, setSelectedHotels] = useState<Hotel[]>([]);
//   const [priceDistribution, setPriceDistribution] = useState<number[]>([]);
//   const [isInitialLoad, setIsInitialLoad] = useState(true);

//   const getHotelSearchFormData = useCallback(() => {
//     const data = localStorage.getItem("hotelSearchFormData");
//     if (data !== null) {
//       setHotelSearchFormData(JSON.parse(data));
//     }
//   }, [setHotelSearchFormData]);

//   const generatePriceDistribution = (
//     hotels: Hotel[],
//     min: number,
//     max: number,
//     bins: number
//   ): number[] => {
//     const range = max - min;
//     const binSize = range / bins;
//     const distribution = Array(bins).fill(0);

//     hotels.forEach((hotel) => {
//       const price = hotel.fareDetail?.totalPrice || 0;
//       if (price >= min && price <= max) {
//         const binIndex = Math.floor((price - min) / binSize);
//         const adjustedBinIndex = Math.min(binIndex, bins - 1);
//         distribution[adjustedBinIndex]++;
//       }
//     });

//     return distribution;
//   };

//   // Sorting function that handles different sorting options
//   const sortHotels = (hotels: Hotel[], sortOption: string): Hotel[] => {
//     const sortedHotels = [...hotels];

//     switch (sortOption) {
//       case "Top picks for long stays":
//         return sortedHotels.sort((a, b) => {
//           const comfortDiff = (b.comfortRating || 0) - (a.comfortRating || 0);
//           if (comfortDiff !== 0) return comfortDiff;

//           const userRatingA = parseFloat(a.userRating || "0");
//           const userRatingB = parseFloat(b.userRating || "0");
//           return userRatingB - userRatingA;
//         });

//       case "Homes & Apartments first":
//         return sortedHotels.sort((a, b) => {
//           const aIsHomeOrApt =
//             a.accommodationType?.toLowerCase().includes("home") ||
//             a.accommodationType?.toLowerCase().includes("apartment") ||
//             false;
//           const bIsHomeOrApt =
//             b.accommodationType?.toLowerCase().includes("home") ||
//             b.accommodationType?.toLowerCase().includes("apartment") ||
//             false;

//           if (aIsHomeOrApt && !bIsHomeOrApt) return -1;
//           if (!aIsHomeOrApt && bIsHomeOrApt) return 1;
//           return 0;
//         });

//       case "Price (lowest first)":
//         return sortedHotels.sort(
//           (a, b) =>
//             (a.fareDetail?.totalPrice || 0) - (b.fareDetail?.totalPrice || 0)
//         );

//       case "Price (highest first)":
//         return sortedHotels.sort(
//           (a, b) =>
//             (b.fareDetail?.totalPrice || 0) - (a.fareDetail?.totalPrice || 0)
//         );

//       case "Best reviewed and lowest price":
//         return sortedHotels.sort((a, b) => {
//           const userRatingA = parseFloat(a.userRating || "0");
//           const userRatingB = parseFloat(b.userRating || "0");
//           const ratingDiff = userRatingB - userRatingA;

//           if (Math.abs(ratingDiff) > 0.1) return ratingDiff;

//           return (
//             (a.fareDetail?.totalPrice || 0) - (b.fareDetail?.totalPrice || 0)
//           );
//         });

//       case "Property rating (high to low)":
//         return sortedHotels.sort(
//           (a, b) => (b.starRating || 0) - (a.starRating || 0)
//         );

//       case "Property rating (low to high)":
//         return sortedHotels.sort(
//           (a, b) => (a.starRating || 0) - (b.starRating || 0)
//         );

//       case "Property rating and price":
//         return sortedHotels.sort((a, b) => {
//           const ratingDiff = (b.starRating || 0) - (a.starRating || 0);
//           if (ratingDiff !== 0) return ratingDiff;

//           return (
//             (a.fareDetail?.totalPrice || 0) - (b.fareDetail?.totalPrice || 0)
//           );
//         });

//       case "Distance from city centre":
//         return sortedHotels.sort((a, b) => {
//           const distanceA = parseFloat(
//             a.distanceFromSearchedEntity?.split(" ")[0] || "0"
//           );
//           const distanceB = parseFloat(
//             b.distanceFromSearchedEntity?.split(" ")[0] || "0"
//           );
//           return distanceA - distanceB;
//         });

//       case "Top reviewed":
//         return sortedHotels.sort((a, b) => {
//           const userRatingA = parseFloat(a.userRating || "0");
//           const userRatingB = parseFloat(b.userRating || "0");
//           const ratingDiff = userRatingB - userRatingA;

//           if (ratingDiff !== 0) return ratingDiff;

//           return (b.userRatingCount || 0) - (a.userRatingCount || 0);
//         });

//       default:
//         return sortedHotels;
//     }
//   };

//   const fetchHotelList = useCallback(async () => {
//     try {
//       setLoading(true);
//       const response = await axios.get<HotelListResponse>(API_URL);
//       const hotelsData = response.data.data.result.inventoryInfoList;
//       setHotels(hotelsData);

//       // Create filter data from the response
//       const filters = createHotelFilters(response.data);

//       // Apply initial filters to the hotel list
//       const initiallyFiltered = addFilterInList(filters, hotelsData);
//       setFilteredHotels(sortHotels(initiallyFiltered, selectedSort));

//       // Set initial filter data
//       if (isInitialLoad) {
//         setFilterData(filters);
//         setIsInitialLoad(false);
//       }
//     } catch (err) {
//       console.error("Error fetching hotel list:", err);
//     } finally {
//       setLoading(false);
//     }
//   }, [selectedSort, isInitialLoad]);

//   useEffect(() => {
//     fetchHotelList();
//     setIsLoading(false);
//     getHotelSearchFormData();

//     // Set initial view mode based on screen width
//     handleResponsiveViewMode();

//     // Add event listener to update view mode when window is resized
//     window.addEventListener("resize", handleResponsiveViewMode);

//     // Cleanup event listener on component unmount
//     return () => {
//       window.removeEventListener("resize", handleResponsiveViewMode);
//     };
//   }, [fetchHotelList, getHotelSearchFormData, setIsLoading]);

//   // Function to handle responsive view mode changes
//   const handleResponsiveViewMode = () => {
//     if (window.innerWidth <= 950) {
//       setViewMode("grid");
//       localStorage.setItem("viewMode", "grid");
//     } else {
//       // Load the saved view mode from localStorage if it exists
//       const savedViewMode = localStorage.getItem("viewMode") as "list" | "grid";
//       if (savedViewMode) {
//         setViewMode(savedViewMode);
//       }
//     }
//     setIsViewModeLoaded(true);
//   };

//   // Update price distribution when filtered hotels change
//   useEffect(() => {
//     if (filterData && filteredHotels.length > 0) {
//       const distribution = generatePriceDistribution(
//         filteredHotels,
//         filterData.priceRange.min,
//         filterData.priceRange.max,
//         40 // Number of histogram bins
//       );
//       setPriceDistribution(distribution);
//     }
//   }, [filteredHotels, filterData]);

//   // Re-sort hotels when sort option changes
//   useEffect(() => {
//     if (filteredHotels.length > 0) {
//       setFilteredHotels((prevHotels) =>
//         sortHotels([...prevHotels], selectedSort)
//       );
//     }
//   }, [selectedSort]);

//   const toggleSortTopPopup = () => {
//     setIsSortTopPopUpActive((prev) => !prev);
//   };

//   const handleSortSelection = (option: string): void => {
//     setSelectedSort(option);
//     setIsSortTopPopUpActive(false);
//     setIsMobileSortFilterActive(false);
//   };

//   const handleViewMode = (mode: "list" | "grid") => {
//     // Only allow manual view mode changes on larger screens
//     if (window.innerWidth > 780) {
//       setViewMode(mode);
//       localStorage.setItem("viewMode", mode);
//     }
//   };

//   const handleCardClick = () => {
//     window.open("/HotelDetail", "_blank");
//   };

//   // Fixed handleFilterChange - removed problematic comparison and dependencies
//   const handleFilterChange = useCallback((data: HotelFilterData | undefined) => {
//     // Use setTimeout to defer the state update to the next tick
//     setTimeout(() => {
//       setFilterData(data);
//       const updatedHotels = addFilterInList(data, hotels);
//       setFilteredHotels(sortHotels(updatedHotels, selectedSort));
//     }, 0);
//   }, [hotels, selectedSort]);

//   // Close sort popup when clicking outside
//   useEffect(() => {
//     const handleClickOutside = (event: MouseEvent) => {
//       const target = event.target as HTMLElement;
//       if (
//         isSortTopPopUpActive &&
//         !target.closest(".sort-button") &&
//         !target.closest(".sort-top-popup")
//       ) {
//         setIsSortTopPopUpActive(false);
//       }
//     };

//     document.addEventListener("mousedown", handleClickOutside);
//     return () => {
//       document.removeEventListener("mousedown", handleClickOutside);
//     };
//   }, [isSortTopPopUpActive]);

//   useEffect(() => {
//     const handleResize = () => {
//       setIsNotMobile(window.innerWidth >= 950);
//     };
//     handleResize();

//     window.addEventListener("resize", handleResize);
//     return () => window.removeEventListener("resize", handleResize);
//   }, []);

//   useScrollLock(isMobileSortFilterActive || isMobileFilterActive);

//   const handleCheckboxChange = (hotel: Hotel, isChecked: boolean) => {
//     if (isChecked) {
//       setSelectedHotels((prev) => [...prev, hotel]);
//     } else {
//       setSelectedHotels((prev) =>
//         prev.filter((h) => h.hotelId !== hotel.hotelId)
//       );
//     }
//   };

//   // Check if a hotel is currently selected
//   const isHotelSelected = (hotelId: number) => {
//     return selectedHotels.some((hotel) => hotel.hotelId === hotelId);
//   };

//   return (
//     <div className="hotel-search-result-container common-container">
//       <div className="hotel-search-result">
//         <div className="searchbar-container-list">
//           <HotelSearchBar
//             isModify={true}
//             shareButtonFlag={true}
//             selectedHotels={selectedHotels}
//           />
//         </div>

//         <div className="route-path">
//           <span>Home</span> <i className="fa-solid fa-greater-than"></i>
//           <span>India</span> <i className="fa-solid fa-greater-than"></i>
//           <span>{hotelSearchFormData?.searchQuery || "..."}</span>{" "}
//           <i className="fa-solid fa-greater-than"></i> Search results
//         </div>

//         <div className="filter-card-container">
//           {isNotMobile && (
//             <div className="filter-map-container">
//               <div className="hotel-list-map-container">
//                 <HotelSearchMap
//                   actualHotels={filteredHotels}
//                   filterData={filterData}
//                 />
//               </div>
//               <div className={`filter ${isMobileFilterActive ? "active" : ""}`}>
//                 <div className="mobilefilter_close-bttn-container">
//                   <div
//                     className="close-bttn"
//                     onClick={() => setIsMobileFilterActive(false)}
//                   >
//                     <i className="fa-solid fa-xmark"></i>
//                   </div>
//                 </div>
//                 {loading ? (
//                   <FilterShimmer />
//                 ) : (
//                   <HotelFilter
//                     isSearchList={true}
//                     handleChange={handleFilterChange}
//                     initialFilterData={filterData}
//                     priceDistribution={priceDistribution}
//                   />
//                 )}
//               </div>
//             </div>
//           )}

//           <div className="card">
//             {loading ? (
//               <SortBarShimmer />
//             ) : (
//               <>
//                 <div className="card-head">
//                   <h2>
//                     {" "}
//                     {hotelSearchFormData?.searchQuery + ":" || "..."}{" "}
//                     {filteredHotels.length} {t("search.properties_found")}
//                   </h2>

//                   <div className="list-grid-btn-container">
//                     <button
//                       className={`toggleBtn ${
//                         viewMode === "list" ? "active" : ""
//                       }`}
//                       onClick={() => handleViewMode("list")}
//                       disabled={window.innerWidth <= 780}
//                     >
//                       {t("search.view_mode.list")}
//                     </button>

//                     <button
//                       className={`toggleBtn ${
//                         viewMode === "grid" ? "active" : ""
//                       }`}
//                       onClick={() => handleViewMode("grid")}
//                       disabled={window.innerWidth <= 780}
//                     >
//                       {t("search.view_mode.grid")}
//                     </button>
//                   </div>
//                   <div
//                     onClick={() => setIsMobileSearchBarActive(true)}
//                     className="modify-search-bttn"
//                   >
//                     {t("search.modify")}
//                   </div>
//                   {isMobileSearchBarActive ? (
//                     <div className="mobile-search-bar show">
//                       <HotelSearchBar isModify={true} />
//                     </div>
//                   ) : (
//                     <></>
//                   )}

//                   <div
//                     className={`search-page-popup-close-btn ${
//                       isMobileSearchBarActive ? "show" : ""
//                     }`}
//                     onClick={() => setIsMobileSearchBarActive(false)}
//                   >
//                     <i className="fa-solid fa-xmark"></i>
//                   </div>

//                   <div
//                     className={`search-page-overlay ${
//                       isMobileSortFilterActive || isMobileSearchBarActive
//                         ? "show"
//                         : ""
//                     }`}
//                   ></div>
//                 </div>

//                 <div className="sort-button-container">
//                   <div
//                     className={`sort-button ${
//                       selectedSort !== defaultSort ? "active" : ""
//                     }`}
//                     onClick={toggleSortTopPopup}
//                   >
//                     <span className="material-icons swapIcon">swap_vert</span>{" "}
//                     Sort by: {selectedSort}
//                     <i className="fa-solid fa-sort"></i>
//                   </div>
//                   {isSortTopPopUpActive && (
//                     <>
//                       <div className="sort-top-popup">
//                         <div className="top-picks-filter-container">
//                           {sortOptions?.map((option, index) => (
//                             <div
//                               className={`sort-filter ${
//                                 selectedSort === option ? "active" : ""
//                               }`}
//                               key={index}
//                               onClick={() => handleSortSelection(option)}
//                             >
//                               {option}
//                             </div>
//                           ))}
//                         </div>
//                       </div>

//                       <div
//                         className="sort-top-popup-overlay"
//                         onClick={() => setIsSortTopPopUpActive(false)}
//                       ></div>
//                     </>
//                   )}
//                 </div>
//               </>
//             )}

//             <>
//               {isViewModeLoaded ? (
//                 <div
//                   className={` ${
//                     viewMode == "list"
//                       ? "hotel-card-container"
//                       : "hotel-grid-container "
//                   }`}
//                 >
//                   {viewMode == "list" ? (
//                     loading ? (
//                       <>
//                         <HotelCardShimmer />
//                         <HotelCardShimmer />
//                       </>
//                     ) : (
//                       <>
//                         {filteredHotels?.length > 0 ? (
//                           filteredHotels.map((hotel, index) => (
//                             <div key={index} className="card-wrapper">
//                               <HotelCard
//                                 handleClick={handleCardClick}
//                                 type="list"
//                                 hotel={hotel}
//                                 onCheckboxChange={handleCheckboxChange}
//                                 isChecked={isHotelSelected(hotel?.hotelId)}
//                               />
//                             </div>
//                           ))
//                         ) : (
//                           <div className="card-wrapper">
//                             <>
//                               <h2>No Data</h2>
//                             </>
//                           </div>
//                         )}
//                       </>
//                     )
//                   ) : loading ? (
//                     <>
//                       <HotelGridShimmer />
//                       <HotelGridShimmer />
//                     </>
//                   ) : (
//                     <>
//                       {filteredHotels?.length > 0 ? (
//                         filteredHotels.map((hotel, index) => (
//                           <div key={index} className="grid-wrapper">
//                             <HotelCard
//                               handleClick={handleCardClick}
//                               type="grid"
//                               hotel={hotel}
//                               onCheckboxChange={handleCheckboxChange}
//                               isChecked={isHotelSelected(hotel?.hotelId)}
//                             />
//                           </div>
//                         ))
//                       ) : (
//                         <div className="grid-wrapper">
//                           <>
//                             <h2>No Data</h2>
//                           </>
//                         </div>
//                       )}
//                     </>
//                   )}
//                 </div>
//               ) : (
//                 <>loading...</>
//               )}
//             </>
//           </div>
//         </div>
//       </div>

//       <div className="mobile-sort-filter-buttons-container">
//         <div
//           className="mobile-sort-filter-buttons-container__button"
//           onClick={() => {
//             setIsMobileSortFilterActive(true);
//             setActiveMobileFilterNav("Sort by");
//           }}
//         >
//           <i className="fa-solid fa-sort"></i> Sort Results
//         </div>
//         <div
//           className="mobile-sort-filter-buttons-container__button"
//           onClick={() => {
//             setIsMobileSortFilterActive(true);
//             setActiveMobileFilterNav("Filters");
//           }}
//         >
//           <i className="fa-solid fa-filter"></i> Filter
//         </div>
//         <div
//           className="mobile-sort-filter-buttons-container__button"
//           onClick={() => {
//             setIsMobileFilterActive(true);
//           }}
//         >
//           <i className="fa-solid fa-map-location-dot"></i> Map View
//         </div>
//       </div>

//       {/* Mobile Map View */}
//       <div
//         className={`mobile-map-view ${isMobileFilterActive ? "active" : ""}`}
//       >
//         <div className="mobile-map-view__header">
//           <div
//             className="mobile-map-view__header__close-bttn"
//             onClick={() => setIsMobileFilterActive(false)}
//           >
//             <i className="fa-solid fa-xmark"></i>
//           </div>
//           <h3>{t("map.mapView")}</h3>
//         </div>
//         <div className="mobile-map-view__content">
//           <HotelSearchMap
//             actualHotels={filteredHotels}
//             filterData={filterData}
//           />
//         </div>
//       </div>

//       <div
//         className={`mobile-sort-filter-container ${
//           isMobileSortFilterActive ? "active" : ""
//         }`}
//       >
//         <div className="mobile-sort-filter_header">
//           <div
//             className="mobile-sort-filter_header__close-bttn"
//             onClick={() => {
//               setIsMobileSortFilterActive(false);
//             }}
//           >
//             <i className="fa-solid fa-xmark"></i>
//           </div>
//           <h3 className="mobile-filter-head">Sort & Filters</h3>
//         </div>

//         <div className="mobile-sort-filter-content">
//           <div className="mobile-sort-filter-content__navbar">
//             <div
//               className={`nav-item ${
//                 activeMobileFilterNav == "Sort by" ? "active" : ""
//               }`}
//               onClick={() => setActiveMobileFilterNav("Sort by")}
//             >
//               Sort by
//             </div>
//             <div
//               className={`nav-item ${
//                 activeMobileFilterNav == "Filters" ? "active" : ""
//               }`}
//               onClick={() => setActiveMobileFilterNav("Filters")}
//             >
//               Filters
//             </div>
//           </div>

//           <div className="mobile-sort-filter-content__content">
//             {activeMobileFilterNav == "Sort by" && (
//               <div className="mobile-sort-wrapper">
//                 {sortOptions?.map((option, index) => (
//                   <div
//                     className={`sort-filter ${
//                       selectedSort === option ? "active" : ""
//                     }`}
//                     key={index}
//                     onClick={() => handleSortSelection(option)}
//                   >
//                     {option}
//                   </div>
//                 ))}
//               </div>
//             )}

//             {activeMobileFilterNav == "Filters" && (
//               <div className="mobile-filter-wrapper">
//                 <HotelFilter
//                   isSearchList={true}
//                   handleChange={handleFilterChange}
//                   initialFilterData={filterData}
//                   priceDistribution={priceDistribution}
//                 />
//               </div>
//             )}
//             <div className="mobile-sort-filter_footer">
//               <div
//                 className="mobile-sort-filter_footer__done-button"
//                 onClick={() => {
//                   setIsMobileSortFilterActive(false);
//                 }}
//               >
//                 <span>Done</span>
//               </div>
//             </div>
//           </div>
//         </div>
//       </div>
//     </div>
//   );
// }

// export default Page;

"use client";
import React, { useCallback, useEffect, useState, useRef } from "react";
import { flushSync } from "react-dom";
import "./HotelSearchResult.scss";
import HotelFilter from "./components/HotelFilter/HotelFilter";
import HotelCard from "./components/HotelCard/HotelCard";
import FilterShimmer from "./ShimmerLoaders/FilterShimmer/FilterShimmer";
import SortBarShimmer from "./ShimmerLoaders/SortBarShimmer/SortBarShimmer";
import HotelCardShimmer from "./ShimmerLoaders/HotelCardShimmer/HotelCardShimmer";
import { Hotel } from "./hotel-search-result.model";
import HotelSearchBar from "./components/HotelSearchBar/HotelSearchBar";
import { addFilterInList } from "@/app/components/utilities/helpers/hotel/hotelFilterService";
import {
  createHotelFilters,
  HotelFilterData,
} from "@/app/components/utilities/helpers/hotel/filterHotels";
import { useCommonContext } from "../contexts/commonContext";
import useScrollLock from "../components/utilities/ScrollLock/useScrollLock";
import { useTranslation } from "@/app/hooks/useTranslation";
import HotelSearchMap from "./components/HotelSearchMap/HotelSearchMap";
import { useRouter } from "next/navigation";
import { List, Grid3X3, ArrowLeft, Search } from "lucide-react";
import { searchInitApi, pollSearchApiWithBatchUpdates } from "@/api/hotel/list-page-service";
import { loadHotelSearchDataFromStorage } from "@/app/utils/hotelSearchUtils";
import { isSearchCompleted } from "@/app/utils/hotelDataConverter";
import {
  extractAndConvertHotels,
  convertSearchInitResponseToHotelListResponse as convertHotelListData
} from "@/app/HotelSearchResult/helpers/hotelDataConverter";

const sortOptions: string[] = [
  "Top picks for long stays",
  "Homes & Apartments first",
  "Price (lowest first)",
  "Price (highest first)",
  "Best reviewed and lowest price",
  "Property rating (high to low)",
  "Property rating (low to high)",
  "Property rating and price",
  "Distance from city centre",
  "Top reviewed",
];

const defaultSort = "Top picks for long stays";



function Page() {
  const { t } = useTranslation();
  const router = useRouter();

  // New state management for infinite scroll
  const [fullHotels, setFullHotels] = useState<Hotel[]>([]); // Store all hotels from API
  const [displayList, setDisplayList] = useState<Hotel[]>([]); // Store hotels to display (20 at a time)
  const [currentDisplayIndex, setCurrentDisplayIndex] = useState<number>(0); // Track how many hotels are displayed

  // Legacy states (keeping for compatibility)
  const [hotels, setHotels] = useState<Hotel[]>([]);
  const [isSortTopPopUpActive, setIsSortTopPopUpActive] =
    useState<boolean>(false);
  const [selectedSort, setSelectedSort] = useState<string>(sortOptions[0]);
  const [viewMode, setViewMode] = useState<"list" | "grid">("list");
  const [isShimmerLoading, setIsShimmerLoading] = useState<boolean>(true); // Main card shimmer
  const [isFilterShimmerLoading, setIsFilterShimmerLoading] = useState<boolean>(false); // Filter shimmer (starts false)
  const [isSearchApiCompleted, setIsSearchApiCompleted] = useState<boolean>(false);
  const [isSearchResultsReceived, setIsSearchResultsReceived] = useState<boolean>(false);
  const [areClicksRestricted, setAreClicksRestricted] = useState<boolean>(true);
  const [isPricingAvailable, setIsPricingAvailable] = useState<boolean>(false);
  const [receivedHotelList, setReceivedHotelList] = useState<Hotel[]>([]); // Store all received hotels
  const [isViewModeLoaded, setIsViewModeLoaded] = useState<boolean>(false);
  const [filterData, setFilterData] = useState<HotelFilterData | undefined>(
    undefined
  );
  const { setIsLoading, isMobileModalOpen } = useCommonContext();
  const [filteredHotels, setFilteredHotels] = useState<Hotel[]>([]);
  const { setHotelSearchFormData, hotelSearchFormData, hotelSearchData, setSearchKey } = useCommonContext();
  const [isMobileFilterActive, setIsMobileFilterActive] =  useState<boolean>(false);
  const [isMobileSortFilterActive, setIsMobileSortFilterActive] =  useState<boolean>(false);
  const [isMobileSearchBarActive, setIsMobileSearchBarActive] =  useState<boolean>(false);
  const [activeMobileFilterNav, setActiveMobileFilterNav] =  useState<string>("Sort by");
  const [isNotMobile, setIsNotMobile] = useState<boolean>(false);
  const [selectedHotels, setSelectedHotels] = useState<Hotel[]>([]);
  const [priceDistribution, setPriceDistribution] = useState<number[]>([]);
  const [isInitialLoad, setIsInitialLoad] = useState(true);

  // Add hydration-safe state
  const [isHydrated, setIsHydrated] = useState(false);

  // Add ref to track if initial API call has been made
  const hasInitialApiCallBeenMade = useRef(false);
  const isApiCallInProgress = useRef(false);
  const isSortingInProgress = useRef(false);
  const hasSearchResultsStarted = useRef(false);

  // Fix: Move localStorage access to useEffect to avoid hydration mismatch
  const getHotelSearchFormData = useCallback(() => {
    if (typeof window !== "undefined") {
      const data = localStorage.getItem("hotelSearchFormData");
      if (data !== null) {
        setHotelSearchFormData(JSON.parse(data));
      }
    }
  }, []); // Remove dependency to prevent re-renders

  // Function to load more hotels into display list (20 at a time)
  const loadMoreHotelsToDisplay = useCallback(() => {
    const nextIndex = currentDisplayIndex + 20;
    const newHotelsToAdd = fullHotels.slice(currentDisplayIndex, nextIndex);

    if (newHotelsToAdd.length > 0) {
      setDisplayList(prev => [...prev, ...newHotelsToAdd]);
      setCurrentDisplayIndex(nextIndex);
      console.log(`📱 Added ${newHotelsToAdd.length} hotels to display. Total displayed: ${nextIndex}`);
    }
  }, [fullHotels, currentDisplayIndex]);

  // Function to update full hotels and manage display list
  const updateFullHotels = useCallback((newHotels: Hotel[], isAppend: boolean = false) => {
    if (isAppend) {
      console.log(`📝 APPEND MODE: Adding ${newHotels.length} hotels to existing ${fullHotels.length}`);
      setFullHotels(prev => {
        const updatedHotels = [...prev, ...newHotels];
        console.log(`📊 Total hotels after append: ${updatedHotels.length}`);
        return updatedHotels;
      });
      // Note: Display list will be updated by infinite scroll logic when needed
    } else {
      console.log(`🔄 REPLACE MODE: Replacing ${fullHotels.length} hotels with ${newHotels.length} new hotels`);
      setFullHotels(newHotels);
      setCurrentDisplayIndex(0);
      setDisplayList([]);

      // Load initial 20 hotels
      const initialHotels = newHotels.slice(0, 20);
      setDisplayList(initialHotels);
      setCurrentDisplayIndex(20);
      console.log(`📱 Display list updated: ${initialHotels.length} hotels displayed`);
    }
  }, [fullHotels.length]);

  const generatePriceDistribution = (
    hotels: Hotel[],
    min: number,
    max: number,
    bins: number
  ): number[] => {
    const range = max - min;
    const binSize = range / bins;
    const distribution = Array(bins).fill(0);

    hotels.forEach((hotel) => {
      const price = hotel.fareDetail?.totalPrice || 0;
      if (price >= min && price <= max) {
        const binIndex = Math.floor((price - min) / binSize);
        const adjustedBinIndex = Math.min(binIndex, bins - 1);
        distribution[adjustedBinIndex]++;
      }
    });

    return distribution;
  };

  // Sorting function that handles different sorting options
  const sortHotels = (hotels: Hotel[], sortOption: string): Hotel[] => {
    const sortedHotels = [...hotels];

    switch (sortOption) {
      case "Top picks for long stays":
        return sortedHotels.sort((a, b) => {
          const comfortDiff = (b.comfortRating || 0) - (a.comfortRating || 0);
          if (comfortDiff !== 0) return comfortDiff;

          const userRatingA = parseFloat(a.userRating || "0");
          const userRatingB = parseFloat(b.userRating || "0");
          return userRatingB - userRatingA;
        });

      case "Homes & Apartments first":
        return sortedHotels.sort((a, b) => {
          const aIsHomeOrApt =
            a.hotelType?.toLowerCase().includes("home") ||
            a.hotelType?.toLowerCase().includes("apartment") ||
            false;
          const bIsHomeOrApt =
            b.hotelType?.toLowerCase().includes("home") ||
            b.hotelType?.toLowerCase().includes("apartment") ||
            false;

          if (aIsHomeOrApt && !bIsHomeOrApt) return -1;
          if (!aIsHomeOrApt && bIsHomeOrApt) return 1;
          return 0;
        });

      case "Price (lowest first)":
        return sortedHotels.sort(
          (a, b) =>
            (a.fareDetail?.totalPrice || 0) - (b.fareDetail?.totalPrice || 0)
        );

      case "Price (highest first)":
        return sortedHotels.sort(
          (a, b) =>
            (b.fareDetail?.totalPrice || 0) - (a.fareDetail?.totalPrice || 0)
        );

      case "Best reviewed and lowest price":
        return sortedHotels.sort((a, b) => {
          const userRatingA = parseFloat(a.userRating || "0");
          const userRatingB = parseFloat(b.userRating || "0");
          const ratingDiff = userRatingB - userRatingA;

          if (Math.abs(ratingDiff) > 0.1) return ratingDiff;

          return (
            (a.fareDetail?.totalPrice || 0) - (b.fareDetail?.totalPrice || 0)
          );
        });

      case "Property rating (high to low)":
        return sortedHotels.sort(
          (a, b) => (b.starRating || 0) - (a.starRating || 0)
        );

      case "Property rating (low to high)":
        return sortedHotels.sort(
          (a, b) => (a.starRating || 0) - (b.starRating || 0)
        );

      case "Property rating and price":
        return sortedHotels.sort((a, b) => {
          const ratingDiff = (b.starRating || 0) - (a.starRating || 0);
          if (ratingDiff !== 0) return ratingDiff;

          return (
            (a.fareDetail?.totalPrice || 0) - (b.fareDetail?.totalPrice || 0)
          );
        });

      case "Distance from city centre":
        return sortedHotels.sort((a, b) => {
          const distanceA = parseFloat(
            a.distanceFromSearchedEntity?.split(" ")[0] || "0"
          );
          const distanceB = parseFloat(
            b.distanceFromSearchedEntity?.split(" ")[0] || "0"
          );
          return distanceA - distanceB;
        });

      case "Top reviewed":
        return sortedHotels.sort((a, b) => {
          const userRatingA = parseFloat(a.userRating || "0");
          const userRatingB = parseFloat(b.userRating || "0");
          const ratingDiff = userRatingB - userRatingA;

          if (ratingDiff !== 0) return ratingDiff;

          return (b.userRatingCount || 0) - (a.userRatingCount || 0);
        });

      default:
        return sortedHotels;
    }
  };

  // Helper function to merge filter data from new hotels with existing filter data
  const mergeFilterData = useCallback((existingFilterData: HotelFilterData, newHotels: Hotel[]): HotelFilterData => {
    console.log(`🔄 Merging filter data for ${newHotels.length} new hotels`);

    // Create filter data from new hotels
    const mockResponse = {
      data: {
        result: {
          inventoryInfoList: newHotels
        }
      }
    };
    const newFilterData = createHotelFilters(mockResponse as any);

    // Merge the filter data
    const mergedFilterData: HotelFilterData = {
      // Price range - expand to include new range
      priceRange: {
        min: Math.min(existingFilterData.priceRange.min, newFilterData.priceRange.min),
        max: Math.max(existingFilterData.priceRange.max, newFilterData.priceRange.max),
        values: existingFilterData.priceRange.values // Keep user's selected values
      },

      // Star ratings - merge and update counts
      starRatings: [...existingFilterData.starRatings],

      // User rating categories - merge
      userRatingCategories: [...existingFilterData.userRatingCategories],

      // Accommodation types - merge and update counts
      accommodationTypes: [...existingFilterData.accommodationTypes],

      // Amenities - merge and update counts
      amenities: [...existingFilterData.amenities],

      // Room types - merge and update counts
      roomTypes: [...existingFilterData.roomTypes],

      // Bedroom/bathroom - update max values
      bedroom: {
        selected: existingFilterData.bedroom.selected,
        max: Math.max(existingFilterData.bedroom.max, newFilterData.bedroom.max)
      },
      bathroom: {
        selected: existingFilterData.bathroom.selected,
        max: Math.max(existingFilterData.bathroom.max, newFilterData.bathroom.max)
      },

      // Distance - expand range
      distanceFromLandmark: {
        min: Math.min(existingFilterData.distanceFromLandmark.min, newFilterData.distanceFromLandmark.min),
        max: Math.max(existingFilterData.distanceFromLandmark.max, newFilterData.distanceFromLandmark.max)
      },

      // Special offers - merge
      specialOffers: [...existingFilterData.specialOffers],

      // Availability status - keep existing
      availabilityStatus: existingFilterData.availabilityStatus,

      // FOMO filters - merge
      fomoFilters: [...existingFilterData.fomoFilters]
    };

    // Merge star ratings
    newFilterData.starRatings.forEach(newRating => {
      const existing = mergedFilterData.starRatings.find(r => r.key === newRating.key);
      if (existing) {
        existing.count += newRating.count;
      } else {
        mergedFilterData.starRatings.push({ ...newRating, isSelected: false });
      }
    });

    // Merge accommodation types
    newFilterData.accommodationTypes.forEach(newType => {
      const existing = mergedFilterData.accommodationTypes.find(t => t.key === newType.key);
      if (existing) {
        existing.count += newType.count;
      } else {
        mergedFilterData.accommodationTypes.push({ ...newType, isSelected: false });
      }
    });

    // Merge amenities
    newFilterData.amenities.forEach(newAmenity => {
      const existing = mergedFilterData.amenities.find(a => a.key === newAmenity.key);
      if (existing) {
        existing.count += newAmenity.count;
      } else {
        mergedFilterData.amenities.push({ ...newAmenity, isSelected: false });
      }
    });

    // Merge room types
    newFilterData.roomTypes.forEach(newRoom => {
      const existing = mergedFilterData.roomTypes.find(r => r.key === newRoom.key);
      if (existing) {
        existing.count += newRoom.count;
      } else {
        mergedFilterData.roomTypes.push({ ...newRoom, isSelected: false });
      }
    });

    // Merge special offers
    newFilterData.specialOffers.forEach(newOffer => {
      const existing = mergedFilterData.specialOffers.find(o => o.key === newOffer.key);
      if (existing) {
        existing.count += newOffer.count;
      } else {
        mergedFilterData.specialOffers.push({ ...newOffer, isSelected: false });
      }
    });

    // Merge user rating categories (avoid duplicates)
    newFilterData.userRatingCategories.forEach(newCategory => {
      const existing = mergedFilterData.userRatingCategories.find(c => c.key === newCategory.key);
      if (!existing) {
        mergedFilterData.userRatingCategories.push({ ...newCategory, isSelected: false });
      }
    });

    // Merge FOMO filters
    newFilterData.fomoFilters.forEach(newFomo => {
      const existing = mergedFilterData.fomoFilters.find(f => f.key === newFomo.key);
      if (existing) {
        existing.tags.push(...newFomo.tags);
      } else {
        mergedFilterData.fomoFilters.push({ ...newFomo });
      }
    });

    console.log(`✅ Filter data merged successfully`);
    return mergedFilterData;
  }, [createHotelFilters]);

  // Dedicated batch processing function
  const processBatch = useCallback((batchData: any[], isFirstBatch: boolean, isLastBatch: boolean) => {
    console.log(`🚨 CRITICAL: processBatch called - isFirstBatch: ${isFirstBatch}, isLastBatch: ${isLastBatch}, batches: ${batchData.length}`);
    console.log(`🚨 CRITICAL: Current shimmer states at batch start:`, {
      isShimmerLoading,
      isFilterShimmerLoading,
      areClicksRestricted,
      isPricingAvailable
    });
    console.log(`🚨 CRITICAL: This should ONLY be called from search polling, NOT from init API!`);

    try {
      // Extract hotels from the batch data
      const batchHotels: any[] = [];
      batchData.forEach(batch => {
        if (batch.hotels && batch.hotels.length > 0) {
          batchHotels.push(...batch.hotels);
        }
      });

      if (batchHotels.length === 0) {
        console.log("⚠️ No hotels found in batch data");
        return;
      }

      // Convert the batch hotels to our Hotel format
      const mockResponse = {
        batches: batchData,
        isCompleted: isLastBatch,
        results: [],
        searchKey: '',
        total_batches: 1
      };
      const convertedHotels = extractAndConvertHotels(mockResponse);
      console.log(`🏨 Converted ${convertedHotels.length} hotels from batch`);

      if (isFirstBatch) {
        console.log("🎯 FIRST BATCH - Replace init data with search data");

        // Update received hotel list (replace for first batch)
        setReceivedHotelList(convertedHotels);
        console.log(`📝 Received hotel list updated: ${convertedHotels.length} hotels`);

        // Update display list for pagination (first 20 hotels)
        updateFullHotels(convertedHotels, false);
        setHotels(convertedHotels);

        // Create and update filter data
        const hotelListResponse = convertHotelListData(mockResponse);
        const filters = createHotelFilters(hotelListResponse);
        setFilterData(filters);

        // Apply filters and sorting
        const initiallyFiltered = addFilterInList(filters, convertedHotels);
        setFilteredHotels(() => {
          const sorted = sortHotels(initiallyFiltered, selectedSort);
          console.log(`🔍 Applied filters and sorting to ${sorted.length} hotels from first batch`);
          return sorted;
        });

        // Turn off shimmer and enable functionality
        console.log("🔧 ABOUT TO TURN OFF SHIMMER - First batch processing");
        console.log("🔧 Current states before turning off:", {
          isFilterShimmerLoading,
          areClicksRestricted,
          isPricingAvailable
        });

        flushSync(() => {
          setIsFilterShimmerLoading(false); // Turn off filter shimmer
          setAreClicksRestricted(false); // Enable clicks
          setIsPricingAvailable(true); // Enable pricing
        });

        console.log("✅ FIRST BATCH PROCESSED - All shimmer OFF, functionality ENABLED");

      } else {
        console.log("➕ SUBSEQUENT BATCH - Append to existing data");

        // Update received hotel list (append for subsequent batches)
        setReceivedHotelList(prev => {
          const updated = [...prev, ...convertedHotels];
          console.log(`📝 Received hotel list updated: ${updated.length} total hotels`);
          return updated;
        });

        // Append to display list
        updateFullHotels(convertedHotels, true);
        setHotels(prev => [...prev, ...convertedHotels]);

        // TODO: Merge filter data with new hotels (temporarily disabled for debugging)
        // setFilterData(currentFilterData => {
        //   if (currentFilterData) {
        //     const mergedFilters = mergeFilterData(currentFilterData, convertedHotels);
        //     console.log(`🔍 Filter data merged for ${convertedHotels.length} new hotels`);
        //     return mergedFilters;
        //   }
        //   return currentFilterData;
        // });
        console.log(`⚠️ Filter data merge temporarily disabled for debugging`);

        console.log(`✅ SUBSEQUENT BATCH PROCESSED - ${convertedHotels.length} hotels appended with updated filters`);
      }

      if (isLastBatch) {
        console.log("🏁 LAST BATCH - Search completed");
        setIsSearchApiCompleted(true);
      }

    } catch (error) {
      console.error("❌ Error in batch processing:", error);
    }
  }, [updateFullHotels, extractAndConvertHotels, convertHotelListData, createHotelFilters, addFilterInList, sortHotels]);

  // Callback to handle batch updates as they arrive
  const handleBatchUpdate = useCallback((batchData: any[], isFirstBatch: boolean, isLastBatch: boolean) => {
    console.log(`📦 BATCH UPDATE CALLED - Delegating to processBatch`);

    // Mark that search results have been received for first batch
    if (isFirstBatch) {
      setIsSearchResultsReceived(true);
      hasSearchResultsStarted.current = true;
    }

    // Delegate to the dedicated batch processing function
    processBatch(batchData, isFirstBatch, isLastBatch);
  }, [processBatch]);

  // Use the new polling function from the service
  const handleSearchPolling = useCallback(async (searchKey: string) => {
    try {
      console.log("🚀 SEARCH POLLING STARTED with searchKey:", searchKey);
      console.log("🔍 Current state before polling:", {
        isFilterShimmerLoading,
        areClicksRestricted,
        isPricingAvailable,
        fullHotelsCount: fullHotels.length
      });

      console.log("📞 Calling pollSearchApiWithBatchUpdates...");
      const searchResponse = await pollSearchApiWithBatchUpdates(searchKey, handleBatchUpdate);
      console.log("📥 pollSearchApiWithBatchUpdates completed, response:", searchResponse);

      if (searchResponse && searchResponse.isCompleted === true) {
        console.log("✅ Search polling completed successfully!");

        // If no batches were processed progressively (fallback case)
        if (!hasSearchResultsStarted.current) {
          console.log("⚠️ No progressive updates occurred, processing final response as fallback");

          // Convert and update hotel data if available
          if (searchResponse.batches?.length > 0) {
            // Extract all hotels from all batches
            const allHotels: any[] = [];
            searchResponse.batches.forEach((batch: any) => {
              if (batch.hotels && batch.hotels.length > 0) {
                allHotels.push(...batch.hotels);
              }
            });

            if (allHotels.length > 0) {
              // Convert hotels using the existing converter
              const convertedHotels = extractAndConvertHotels(searchResponse);
              console.log("Final converted hotels:", convertedHotels);

              // Convert to HotelListResponse format for filter creation
              const hotelListResponse = convertHotelListData(searchResponse);

              // Create filter data from the response
              const filters = createHotelFilters(hotelListResponse);

              // Update full hotels and display list using new state management
              updateFullHotels(convertedHotels, false);

              // Set legacy hotels state for compatibility
              setHotels(convertedHotels);

              // Apply initial filters to the hotel list
              const initiallyFiltered = addFilterInList(filters, convertedHotels);
              setFilteredHotels(sortHotels(initiallyFiltered, selectedSort));

              // Update filter data
              setFilterData(filters);
              setIsSearchApiCompleted(true);

              // Turn off shimmer loading
              setIsShimmerLoading(false);
            }
          }
        } else {
          console.log("✅ Progressive updates completed successfully - search is done");
          setIsSearchApiCompleted(true);
        }
      } else {
        console.log("⏰ Search polling completed but no final results received");

        // Turn off shimmer loading even if no results (only if no progressive updates occurred)
        if (!hasSearchResultsStarted.current) {
          setIsShimmerLoading(false);
        }
      }

    } catch (error) {
      console.error("❌ Error during search polling:", error);
      // Always turn off shimmer loading even if there's an error
      setIsShimmerLoading(false);
    }
  }, [handleBatchUpdate]);

  // Call search init API with hotel search data - Updated for new infinite scroll logic
  const callSearchInitApi = useCallback(async (skip: number = 0, isLoadMore: boolean = false) => {
    console.log(`📞 callSearchInitApi called with skip: ${skip}, isLoadMore: }`);

    // Prevent multiple simultaneous API calls
    if (isApiCallInProgress.current) {
      console.log('🚫 API call already in progress, skipping...');
      return;
    }

    isApiCallInProgress.current = true;

    try {
      // Get search data from context or localStorage
      let searchData = hotelSearchData;

      // If not in context, try to load from localStorage
      if (!searchData) {
        const loadedData = loadHotelSearchDataFromStorage();
        if (loadedData) {
          searchData = loadedData;
        }
      }

      // If we have search data, call the API
      if (searchData) {
        console.log("Calling searchInitApi with data:", searchData, "skip:", skip, "limit: 1");

        // Note: isLoadMore parameter is kept for future use but not currently used
        // since we're using the new polling logic

        // Always use limit=1 as per requirements
        const initResponse = await searchInitApi(searchData, skip, 1);
        console.log("searchInitApi response:", initResponse);

        // If searchInitApi is successful, process the response
        if (initResponse) {
          // Convert and set initial hotel data if available
          if (initResponse.batches?.length > 0 && initResponse.batches[0]?.hotels?.length > 0) {
            const convertedHotels = extractAndConvertHotels(initResponse);

            // Convert to HotelListResponse format for filter creation
            const hotelListResponse = convertHotelListData(initResponse);

            // Create filter data from the response
            const filters = createHotelFilters(hotelListResponse);

            if (isLoadMore) {
              // For load more, we don't append here since we're using the polling logic
              // The polling will handle collecting all batches
              console.log("Load more request processed");
            } else {
              // Initial load - use new state management
              updateFullHotels(convertedHotels, false);

              // Set legacy hotels state for compatibility
              setHotels(convertedHotels);

              // Apply initial filters to the hotel list
              const initiallyFiltered = addFilterInList(filters, convertedHotels);
              setFilteredHotels(sortHotels(initiallyFiltered, selectedSort));

              // Set initial filter data
              if (isInitialLoad) {
                setFilterData(filters);
                setIsInitialLoad(false);
              }

              // Turn off card shimmer, turn on filter shimmer, keep pricing shimmer and clicks disabled
              console.log("🔧 INIT API - About to update shimmer states");
              console.log("🔧 Current states before init update:", {
                isShimmerLoading,
                isFilterShimmerLoading,
                areClicksRestricted,
                isPricingAvailable
              });

              flushSync(() => {
                setIsShimmerLoading(false); // Turn off main card shimmer
                setIsFilterShimmerLoading(true); // Turn ON filter shimmer (waiting for search results)
                // Keep areClicksRestricted = true (until search results)
                // Keep isPricingAvailable = false (until search results)
              });

              console.log("🔧 INIT API - Shimmer states updated");
              console.log("🏨 INIT DATA LOADED - Showing hotels from init API");
              console.log(`📊 Loaded ${convertedHotels.length} hotels from init API`);
              console.log("✅ Card shimmer turned OFF - showing init hotels");
              console.log("⏳ Pricing shimmer REMAINS ON until search results");
              console.log("🚫 Clicks REMAIN DISABLED until search results");
              console.log("🔄 Filter shimmer turned ON - waiting for search results");
            }

            // Check if there are more batches to load (for logging purposes)
            const latestBatch = initResponse.batches[initResponse.batches.length - 1];
            console.log("Latest batch is_last_batch:", latestBatch?.batch_info.is_last_batch);

            // Only set search completed if the response is actually completed
            if (initResponse.isCompleted) {
              setIsSearchApiCompleted(true);
            }
          } else {
            // No initial hotel data available, keep shimmer on for search polling
            console.log("⚠️ No initial hotel data in init response - keeping shimmer on for search polling");
          }

          // Extract searchKey from the response
          console.log("Full initResponse structure:", JSON.stringify(initResponse, null, 2));
          const searchKey = initResponse.searchKey;
          console.log("Extracted searchKey:", searchKey);
          console.log("SearchKey type:", typeof searchKey);

          if (searchKey) {
            console.log("✅ Valid searchKey found:", searchKey);

            // Save searchKey to context and localStorage
            setSearchKey(searchKey);
            if (typeof window !== "undefined") {
              localStorage.setItem("searchKey", searchKey);
              console.log("💾 SearchKey saved to localStorage:", searchKey);
            }

            // Check if search is already completed
            const searchCompleted = isSearchCompleted(initResponse);
            console.log("🔍 Checking if search is completed:", searchCompleted);
            if (searchCompleted) {
              console.log("✅ Search already completed in init response - skipping polling!");
              return;
            }

            console.log("⏳ Search not completed - starting polling...");

            // Call searchApi up to 10 times until completed: true
            await handleSearchPolling(searchKey);

          } else {
            console.error("❌ No searchKey found in searchInitApi response!");
            console.log("Response structure:", JSON.stringify(initResponse, null, 2));
            console.log("Available properties:", Object.keys(initResponse));
            console.log("Please check the API response to identify the correct searchKey property name.");
          }
        }

      } else {
        console.log("No hotel search data available for API call");
      }
    } catch (error) {
      console.error("❌ Error calling searchInitApi:", error);
      // Turn off all shimmer and enable everything if there's an error
      setIsShimmerLoading(false);
      setIsFilterShimmerLoading(false);
      setAreClicksRestricted(false);
      setIsPricingAvailable(true);
    } finally {
      // Reset the API call progress flag
      isApiCallInProgress.current = false;
    }
  }, [hotelSearchData, handleSearchPolling]); // Remove unnecessary dependencies

  // Fix: Separate hydration effect
  useEffect(() => {
    setIsHydrated(true);
  }, []);

  // Fix: Only run client-side code after hydration
  useEffect(() => {
    if (!isHydrated || hasInitialApiCallBeenMade.current) {
      console.log('🚫 Skipping initial API call - already made or not hydrated');
      return;
    }

    console.log('🚀 Making initial API call');
    setIsLoading(false);
    getHotelSearchFormData();

    // Call search init API when page loads with initial skip value
    callSearchInitApi(0, false);
    hasInitialApiCallBeenMade.current = true; // Mark that initial API call has been made

    // Set initial view mode based on screen width
    handleResponsiveViewMode();

    // Add event listener to update view mode when window is resized
    window.addEventListener("resize", handleResponsiveViewMode);

    // Cleanup event listener on component unmount
    return () => {
      window.removeEventListener("resize", handleResponsiveViewMode);
    };
  }, [isHydrated]); // Remove dependencies that cause re-renders

  // Safety timeout to ensure shimmer doesn't show indefinitely
  useEffect(() => {
    if (isShimmerLoading || isFilterShimmerLoading) {
      const timeout = setTimeout(() => {
        console.log("🚨 SHIMMER TIMEOUT REACHED (60s) - forcing all shimmer off");
        console.log("🚨 This suggests the search API is not returning batches properly");
        setIsShimmerLoading(false);
        setIsFilterShimmerLoading(false);
        setIsPricingAvailable(true);
        setAreClicksRestricted(false);
      }, 60000); // 60 seconds timeout (increased from 30)

      return () => clearTimeout(timeout);
    }
  }, [isShimmerLoading, isFilterShimmerLoading]);

  // New infinite scroll functionality - Load more when 16th item comes into viewport
  useEffect(() => {
    const observerCallback = (entries: IntersectionObserverEntry[]) => {
      entries.forEach((entry) => {
        if (entry.isIntersecting) {
          const target = entry.target as HTMLElement;
          const index = parseInt(target.getAttribute('data-hotel-index') || '0');

          // Check if this is the 16th item (index 15) and we have more hotels to show
          if (index === 15 && currentDisplayIndex < fullHotels.length) {
            console.log('📱 16th item in viewport, loading more hotels...');
            loadMoreHotelsToDisplay();
          }
        }
      });
    };

    const observer = new IntersectionObserver(observerCallback, {
      root: null,
      rootMargin: '100px',
      threshold: 0.1
    });

    // Observe every 16th item (15, 35, 55, etc.)
    const hotelCards = document.querySelectorAll('[data-hotel-index]');
    hotelCards.forEach((card) => {
      const cardIndex = parseInt(card.getAttribute('data-hotel-index') || '0');
      if ((cardIndex + 1) % 16 === 0) { // 16th, 32nd, 48th items, etc.
        observer.observe(card);
      }
    });

    return () => {
      observer.disconnect();
    };
  }, [displayList, currentDisplayIndex, fullHotels.length, loadMoreHotelsToDisplay]);

  // Fix: Function to handle responsive view mode changes
  const handleResponsiveViewMode = () => {
    if (typeof window === "undefined") return;

    if (window.innerWidth <= 950) {
      setViewMode("grid");
      localStorage.setItem("viewMode", "grid");
    } else {
      // Load the saved view mode from localStorage if it exists
      const savedViewMode = localStorage.getItem("viewMode") as "list" | "grid";
      if (savedViewMode) {
        setViewMode(savedViewMode);
      }
    }
    setIsViewModeLoaded(true);
  };

  // Update price distribution when filtered hotels change
  useEffect(() => {
    if (filterData && filteredHotels.length > 0) {
      const distribution = generatePriceDistribution(
        filteredHotels,
        filterData.priceRange.min,
        filterData.priceRange.max,
        40 // Number of histogram bins
      );
      setPriceDistribution(distribution);
    }
  }, [filteredHotels, filterData]);

  // Re-sort hotels when sort option changes
  useEffect(() => {
    if (fullHotels.length > 0 && !isSortingInProgress.current) {
      isSortingInProgress.current = true;

      const sortedHotels = sortHotels([...fullHotels], selectedSort);
      setFilteredHotels(sortedHotels);

      // Reset display list with sorted hotels (first 20)
      const initialDisplay = sortedHotels.slice(0, 20);
      setDisplayList(initialDisplay);
      setCurrentDisplayIndex(20);

      // Update fullHotels with sorted list
      setFullHotels(sortedHotels);

      // Reset sorting flag after a brief delay
      setTimeout(() => {
        isSortingInProgress.current = false;
      }, 100);
    }
  }, [selectedSort]); // Remove fullHotels.length dependency to prevent loops

  const toggleSortTopPopup = () => {
    setIsSortTopPopUpActive((prev) => !prev);
  };

  const handleSortSelection = (option: string): void => {
    setSelectedSort(option);
    setIsSortTopPopUpActive(false);
    setIsMobileSortFilterActive(false);
  };

  // Fix: Add window check
  const handleViewMode = (mode: "list" | "grid") => {
    // Only allow manual view mode changes on larger screens
    if (typeof window !== "undefined" && window.innerWidth > 780) {
      setViewMode(mode);
      localStorage.setItem("viewMode", mode);
    }
  };

  const handleCardClick = () => {
    if (typeof window !== "undefined") {
      // Mobile (≤768px): Same tab navigation
      // Desktop (>768px): New tab navigation
      if (window.innerWidth <= 768) {
        router.push("/HotelDetail");
      } else {
        window.open("/HotelDetail", "_blank");
      }
    }
  };

  // Fixed handleFilterChange - updated for new state management
  const handleFilterChange = useCallback(
    (data: HotelFilterData | undefined) => {
      console.log("🔍 FILTER CHANGE - Applying new filters");
      console.log("🔍 Filter data:", data);
      console.log("🔍 Total hotels to filter from:", fullHotels.length);

      // Use setTimeout to defer the state update to the next tick
      setTimeout(() => {
        setFilterData(data);

        // Apply filters to full hotels list (NOT to already filtered hotels)
        const updatedHotels = addFilterInList(data, fullHotels);
        const sortedHotels = sortHotels(updatedHotels, selectedSort);
        setFilteredHotels(sortedHotels);

        console.log(`🔍 Filtered hotels: ${updatedHotels.length} out of ${fullHotels.length}`);

        // Reset display list with filtered hotels (first 20)
        const initialDisplay = sortedHotels.slice(0, 20);
        setDisplayList(initialDisplay);
        setCurrentDisplayIndex(20);

        // DO NOT update fullHotels - it should always contain ALL hotels
        // updateFullHotels(sortedHotels, false); // REMOVED - This was the bug!

        console.log(`🔍 Display list updated with ${initialDisplay.length} hotels`);
      }, 0);
    },
    [fullHotels, selectedSort] // Removed updateFullHotels dependency
  );

  // Close sort popup when clicking outside
  useEffect(() => {
    if (!isHydrated) return;

    const handleClickOutside = (event: MouseEvent) => {
      const target = event.target as HTMLElement;
      if (
        isSortTopPopUpActive &&
        !target.closest(".sort-button") &&
        !target.closest(".sort-top-popup")
      ) {
        setIsSortTopPopUpActive(false);
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, [isSortTopPopUpActive, isHydrated]);

  useEffect(() => {
    if (!isHydrated) return;

    const handleResize = () => {
      setIsNotMobile(window.innerWidth >= 950);
    };
    handleResize();

    window.addEventListener("resize", handleResize);
    return () => window.removeEventListener("resize", handleResize);
  }, [isHydrated]);

  useScrollLock(isMobileSortFilterActive || isMobileFilterActive);

  // Computed variable to control filter visibility
  const shouldShowFilterShimmer = isShimmerLoading || isFilterShimmerLoading;

  // Apply filters when fullHotels changes (for batch updates)
  useEffect(() => {
    if (fullHotels.length > 0 && filterData && !isSortingInProgress.current) {
      console.log(`🔍 Applying filters to ${fullHotels.length} hotels after batch update`);
      const filteredHotels = addFilterInList(filterData, fullHotels);
      const sortedFiltered = sortHotels(filteredHotels, selectedSort);
      setFilteredHotels(sortedFiltered);
    }
  }, [fullHotels.length, filterData, selectedSort]);

  const handleCheckboxChange = (hotel: Hotel, isChecked: boolean) => {
    if (isChecked) {
      setSelectedHotels((prev) => [...prev, hotel]);
    } else {
      setSelectedHotels((prev) =>
        prev.filter((h) => h.hotelId !== hotel.hotelId)
      );
    }
  };

  // Check if a hotel is currently selected
  const isHotelSelected = (hotelId: number) => {
    return selectedHotels.some((hotel) => hotel.hotelId === hotelId);
  };

  // Fix: Show loading state during hydration
  // if (!isHydrated) {
  //   return (
  //     <div className="hotel-search-result-container common-container">
  //       <div className="hotel-search-result">
  //         <div className="searchbar-container-list">
  //           <div>Loading...</div>
  //         </div>
  //       </div>
  //     </div>
  //   );
  // }

  return (
    <div className="hotel-search-result-container common-container">
      <div className="hotel-search-result">
        <div className="searchbar-container-list">
          <HotelSearchBar
            isModify={true}
            shareButtonFlag={true}
            selectedHotels={selectedHotels}
          />
        </div>

        <div className="route-path">
          <span>Home</span> <i className="fa-solid fa-greater-than"></i>
          <span>India</span> <i className="fa-solid fa-greater-than"></i>
          <span>{hotelSearchFormData?.searchQuery || "..."}</span>{" "}
          <i className="fa-solid fa-greater-than"></i> Search results
        </div>

        <div className="filter-card-container">
          {isNotMobile && (
            <div className="filter-map-container">
              <div className="hotel-list-map-container">
                <HotelSearchMap
                  actualHotels={displayList}
                  filterData={filterData}
                />
              </div>

              {/* Show FilterShimmer OR HotelFilter, never both */}
              {shouldShowFilterShimmer ? (
                <FilterShimmer />
              ) : (
                <div className={`filter ${isMobileFilterActive ? "active" : ""}`}>
                  <div className="mobilefilter_close-bttn-container">
                    <div
                      className="close-bttn"
                      onClick={() => setIsMobileFilterActive(false)}
                    >
                      <i className="fa-solid fa-xmark"></i>
                    </div>
                  </div>
                  <HotelFilter
                    isSearchList={true}
                    handleChange={handleFilterChange}
                    initialFilterData={filterData}
                    priceDistribution={priceDistribution}
                  />
                </div>
              )}
            </div>
          )}

      

          <div className="card">
            {isShimmerLoading ? (
              <SortBarShimmer />
            ) : (
              <>
                <div className="card-head md:relative md:top-auto sticky top-0 bg-white z-10 md:z-auto">
                  {/* Desktop Layout with Toggle Buttons */}
                  <div className="hidden w-full md:flex md:items-center md:justify-between md:py-4">
                    {/* Left side - Heading */}
                    <h2 className="text-xl font-semibold text-gray-800">
                      {hotelSearchFormData?.searchQuery + ":" || "..."}{" "}
                      {fullHotels.length} {t("search.properties_found")}
                      {displayList.length < fullHotels.length && (
                        <span className="text-sm text-gray-600 ml-2">
                          (Showing {displayList.length})
                        </span>
                      )}
                    </h2>

                    {/* Right side - Toggle buttons */}
                    <div className="list-grid-btn-container">
                      <button
                        className={`toggleBtn ${
                          viewMode === "list" ? "active" : ""
                        }`}
                        onClick={() => handleViewMode("list")}
                        style={{ display: 'flex', alignItems: 'center', gap: '4px' }}
                      >
                        <List size={16} />
                        {t("search.view_mode.list")}
                      </button>

                      <button
                        className={`toggleBtn ${
                          viewMode === "grid" ? "active" : ""
                        }`}
                        onClick={() => handleViewMode("grid")}
                        style={{ display: 'flex', alignItems: 'center', gap: '4px' }}
                      >
                        <Grid3X3 size={16} />
                        {t("search.view_mode.grid")}
                      </button>
                    </div>
                  </div>

                  {/* Mobile Layout */}
                  <div className="block md:hidden px-2 py-2 w-screen relative left-1/2 transform -translate-x-1/2 bg-white">
                    <div className="flex items-center justify-between mb-1 w-full">
                      {/* Left side - Back arrow + destination details */}
                      <div className="flex items-center flex-1 mr-2">
                        <ArrowLeft
                          size={20}
                          className="mr-2 text-gray-700 mt-0.5 flex-shrink-0 cursor-pointer hover:text-gray-900 transition-colors"
                          onClick={() => router.push('/')}
                        />
                        <div className="flex-1 min-w-0">
                          <div className="text-md font-bold text-gray-900 mb-0.5 truncate">
                            {hotelSearchFormData?.searchQuery || "Destination"}
                          </div>
                          <div className="text-xs text-gray-600 leading-tight">
                            {hotelSearchFormData?.checkInDate && hotelSearchFormData?.checkOutDate
                              ? `${new Date(hotelSearchFormData.checkInDate).toLocaleDateString('en-GB', { day: '2-digit', month: 'short' })} - ${new Date(hotelSearchFormData.checkOutDate).toLocaleDateString('en-GB', { day: '2-digit', month: 'short' })} • ${(hotelSearchFormData.travelers?.adults || 0) + (hotelSearchFormData.travelers?.children || 0)} Guests • ${fullHotels.length} Hotels`
                              : `2 Guests • ${fullHotels.length} Hotels`
                            }
                          </div>
                        </div>
                      </div>

                      {/* Right side - Modify button */}
                      <div
                        onClick={() => setIsMobileSearchBarActive(true)}
                        className="modify-search-bttn bg-blue-600 text-white p-2 rounded text-xs font-medium hover:bg-blue-700 transition-colors flex-shrink-0 flex items-center justify-center"
                        title={t("search.modify")}
                      >
                        <Search size={16} />
                      </div>
                    </div>

                    {/* Mobile Toggle Buttons */}
                    <div className="list-grid-btn-container">
                      <button
                        className={`toggleBtn ${
                          viewMode === "list" ? "active" : ""
                        }`}
                        onClick={() => handleViewMode("list")}
                        disabled={
                          typeof window !== "undefined" &&
                          window.innerWidth <= 780
                        }
                        style={{ display: 'flex', alignItems: 'center', gap: '4px' }}
                      >
                        <List size={16} />
                        {t("search.view_mode.list")}
                      </button>

                      <button
                        className={`toggleBtn ${
                          viewMode === "grid" ? "active" : ""
                        }`}
                        onClick={() => handleViewMode("grid")}
                        disabled={
                          typeof window !== "undefined" &&
                          window.innerWidth <= 780
                        }
                        style={{ display: 'flex', alignItems: 'center', gap: '4px' }}
                      >
                        <Grid3X3 size={16} />
                        {t("search.view_mode.grid")}
                      </button>
                    </div>
                  </div>

                  {/* Desktop modify button */}
                  <div className="hidden md:block">
                    <div
                      onClick={() => setIsMobileSearchBarActive(true)}
                      className="modify-search-bttn"
                    >
                      {t("search.modify")}
                    </div>
                  </div>
                  {isMobileSearchBarActive ? (
                    <div className="mobile-search-bar show">
                      <div
                        style={{
                          borderRadius: "12px", // adjust as needed
                          overflow: "hidden",
                          marginTop:'90px',
                          transition: "all 0.3s ease", // optional for smooth visual transitions
                        }}
                      >
                        <HotelSearchBar isModify={true} />
                      </div>
                    </div>
                  ) : (
                    <></>
                  )}

                  <div
                    className={`search-page-popup-close-btn ${
                      isMobileSearchBarActive && !isMobileModalOpen ? "show" : ""
                    }`}
                    onClick={() => setIsMobileSearchBarActive(false)}
                  >
                    <i className="fa-solid fa-xmark"></i>
                  </div>

                  <div
                    className={`search-page-overlay ${
                      isMobileSortFilterActive || isMobileSearchBarActive
                        ? "show"
                        : ""
                    }`}
                  ></div>
                </div>

                {/* Mobile Properties Found - After card-head but outside it */}
                <div className="block md:hidden">
                  <div className="text-sm font-medium text-gray-700 bg-gray-50 pr-2 py-1.5 rounded mx-3 my-2">
                    {fullHotels.length} {t("search.properties_found")}
                    {displayList.length < fullHotels.length && (
                      <span className="text-xs text-gray-600 ml-2">
                        (Showing {displayList.length})
                      </span>
                    )}
                  </div>
                </div>

                <div className="sort-button-container">
                  <div
                    className={`sort-button ${
                      selectedSort !== defaultSort ? "active" : ""
                    }`}
                    onClick={toggleSortTopPopup}
                  >
                    <span className="material-icons swapIcon">swap_vert</span>{" "}
                    Sort by: {selectedSort}
                    <i className="fa-solid fa-sort"></i>
                  </div>
                  {isSortTopPopUpActive && (
                    <>
                      <div className="sort-top-popup">
                        <div className="top-picks-filter-container">
                          {sortOptions?.map((option, index) => (
                            <div
                              className={`sort-filter ${
                                selectedSort === option ? "active" : ""
                              }`}
                              key={index}
                              onClick={() => handleSortSelection(option)}
                            >
                              {option}
                            </div>
                          ))}
                        </div>
                      </div>

                      <div
                        className="sort-top-popup-overlay"
                        onClick={() => setIsSortTopPopUpActive(false)}
                      ></div>
                    </>
                  )}
                </div>
              </>
            )}

            <>
              {/* Search Status Indicator */}
              {!isSearchApiCompleted && !isShimmerLoading && (
                <div className="bg-blue-50 border-l-4 border-blue-400 p-3 mb-4">
                  <div className="flex items-center">
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600 mr-3"></div>
                    <div className="text-sm text-blue-700">
                      <p className="font-medium">Searching for the best prices...</p>
                      <p className="text-xs">Prices and booking options will be available shortly</p>
                    </div>
                  </div>
                </div>
              )}

              {isViewModeLoaded ? (
                <div
                  className={` ${
                    viewMode == "list"
                      ? "hotel-card-container"
                      : "hotel-grid-container "
                  }`}
                >
                  {viewMode == "list" ? (
                    isShimmerLoading ? (
                      <ShimmerCardList
                        viewMode="list"
                        ShimmerComponent={HotelCardShimmer}
                        listCount={3}
                        gridCount={6}
                      />
                    ) : (
                      <>
                        {displayList?.length > 0 ? (
                          displayList.map((hotel, index) => (
                            <div
                              key={`${hotel.hotelId}-${index}`}
                              className="card-wrapper"
                              data-hotel-index={index}
                            >
                              <HotelCard
                                handleClick={handleCardClick}
                                type="list"
                                hotel={hotel}
                                onCheckboxChange={handleCheckboxChange}
                                isChecked={isHotelSelected(hotel?.hotelId)}
                                isSearchCompleted={!areClicksRestricted}
                                isPricingAvailable={isPricingAvailable}
                              />
                            </div>
                          ))
                        ) : (
                          <div className="card-wrapper">
                            <>
                              <h2>No Data</h2>
                            </>
                          </div>
                        )}
                      </>
                    )
                  ) : isShimmerLoading ? (
                    <ShimmerCardList
                      viewMode="grid"
                      ShimmerComponent={HotelCardShimmer}
                      listCount={3}
                      gridCount={6}
                    />
                  ) : (
                    <>
                      {displayList?.length > 0 ? (
                        displayList.map((hotel, index) => (
                          <div
                            key={`${hotel.hotelId}-${index}`}
                            className="grid-wrapper"
                            data-hotel-index={index}
                          >
                            <HotelCard
                              handleClick={handleCardClick}
                              type="grid"
                              hotel={hotel}
                              onCheckboxChange={handleCheckboxChange}
                              isChecked={isHotelSelected(hotel?.hotelId)}
                              isSearchCompleted={!areClicksRestricted}
                              isPricingAvailable={isPricingAvailable}
                            />
                          </div>
                        ))
                      ) : (
                        <div className="grid-wrapper">
                          <>
                            <h2>No Data</h2>
                          </>
                        </div>
                      )}
                    </>
                  )}
                </div>
              ) : (
                <div className={`${viewMode === "list" ? "hotel-card-container" : "hotel-grid-container"}`}>
                  {typeof window !== "undefined" && window.innerWidth <= 950 ? (
                    <ShimmerCardList
                      viewMode="grid"
                      ShimmerComponent={HotelCardShimmer}
                      listCount={3}
                      gridCount={6}
                    />
                  ) : viewMode === "list" ? (
                    <ShimmerCardList
                      viewMode="list"
                      ShimmerComponent={HotelCardShimmer}
                      listCount={3}
                      gridCount={6}
                    />
                  ) : (
                    <ShimmerCardList
                      viewMode="grid"
                      ShimmerComponent={HotelCardShimmer}
                      listCount={3}
                      gridCount={6}
                    />
                  )}
                </div>
              )}
            </>
          </div>



          {/* End of Results Indicator */}
          {currentDisplayIndex >= fullHotels.length && !isShimmerLoading && fullHotels.length > 0 && (
            <div className="flex justify-center items-center py-8">
              <span className="text-gray-500 text-sm">You've reached the end of the results</span>
            </div>
          )}
        </div>
      </div>

      <div className="mobile-sort-filter-buttons-container">
        <div
          className="mobile-sort-filter-buttons-container__button"
          onClick={() => {
            setIsMobileSortFilterActive(true);
            setActiveMobileFilterNav("Sort by");
          }}
        >
          <i className="fa-solid fa-sort"></i> Sort Results
        </div>
        <div
          className="mobile-sort-filter-buttons-container__button"
          onClick={() => {
            setIsMobileSortFilterActive(true);
            setActiveMobileFilterNav("Filters");
          }}
        >
          <i className="fa-solid fa-filter"></i> Filter
        </div>
        <div
          className="mobile-sort-filter-buttons-container__button"
          onClick={() => {
            setIsMobileFilterActive(true);
          }}
        >
          <i className="fa-solid fa-map-location-dot"></i> Map View
        </div>
      </div>

      {/* Mobile Map View */}
      <div
        className={`mobile-map-view ${isMobileFilterActive ? "active" : ""}`}
      >
        <div className="mobile-map-view__header">
          <div
            className="mobile-map-view__header__close-bttn"
            onClick={() => setIsMobileFilterActive(false)}
          >
            <i className="fa-solid fa-xmark"></i>
          </div>
          <h3>{t("map.mapView")}</h3>
        </div>
        <div className="mobile-map-view__content">
          <HotelSearchMap
            actualHotels={displayList}
            filterData={filterData}
            onFilterChange={handleFilterChange}
          />
        </div>
      </div>

      <div
        className={`mobile-sort-filter-container ${
          isMobileSortFilterActive ? "active" : ""
        }`}
      >
        <div className="mobile-sort-filter_header">
          <div
            className="mobile-sort-filter_header__close-bttn"
            onClick={() => {
              setIsMobileSortFilterActive(false);
            }}
          >
            <i className="fa-solid fa-xmark"></i>
          </div>
          <h3 className="mobile-filter-head">Sort & Filters</h3>
        </div>

        <div className="mobile-sort-filter-content">
          <div className="mobile-sort-filter-content__navbar">
            <div
              className={`nav-item ${
                activeMobileFilterNav == "Sort by" ? "active" : ""
              }`}
              onClick={() => setActiveMobileFilterNav("Sort by")}
            >
              Sort by
            </div>
            <div
              className={`nav-item ${
                activeMobileFilterNav == "Filters" ? "active" : ""
              }`}
              onClick={() => setActiveMobileFilterNav("Filters")}
            >
              Filters
            </div>
          </div>

          <div className="mobile-sort-filter-content__content">
            {activeMobileFilterNav == "Sort by" && (
              <div className="mobile-sort-wrapper">
                {sortOptions?.map((option, index) => (
                  <div
                    className={`sort-filter ${
                      selectedSort === option ? "active" : ""
                    }`}
                    key={index}
                    onClick={() => handleSortSelection(option)}
                  >
                    {option}
                  </div>
                ))}
              </div>
            )}

            {activeMobileFilterNav == "Filters" && (
              <div className="mobile-filter-wrapper">
                <HotelFilter
                  isSearchList={true}
                  handleChange={handleFilterChange}
                  initialFilterData={filterData}
                  priceDistribution={priceDistribution}
                />
              </div>
            )}
            <div className="mobile-sort-filter_footer">
              <div
                className="mobile-sort-filter_footer__done-button"
                onClick={() => {
                  setIsMobileSortFilterActive(false);
                }}
              >
                <span>Done</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

export default Page;

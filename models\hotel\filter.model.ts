export type AmenityItem = {
  key: string;
  count: number;
  isSelected: boolean;
  icon?: string;
};

export type AccommodationTypeItem = {
  key: string;
  count: number;
  isSelected: boolean;
};

export type StarRatingItem = {
  key: number;
  isSelected: boolean;
  count: number;
};

export type UserRatingItem = {
  key: string;
  isSelected: boolean;
  count: number;
};

export interface HotelFilterData {
  priceRange: {
    min: number;
    max: number;
    values: {
      minimum: number;
      maximum: number;
    };
  };
  popularFilters: {
    freeCancellation: { key: string; isSelected: boolean; count: number };
    breakfastIncluded: { key: string; isSelected: boolean; count: number };
    payAtHotel: { key: string; isSelected: boolean; count: number };
    petsAllowed: { key: string; isSelected: boolean; count: number };
  };
  userRating: UserRatingItem[];
  accommodationTypes: AccommodationTypeItem[];
  amenities: AmenityItem[];
  hotelStarRating: StarRatingItem[];
  priceDistribution: number[];
  isPriceRangeChanged: boolean;
  popularFiltersAnyActive: boolean;
  userRatingAnyActive: boolean;
  accommodationTypesAnyActive: boolean;
  amenitiesAnyActive: boolean;
  hotelStarRatingAnyActive: boolean;
}

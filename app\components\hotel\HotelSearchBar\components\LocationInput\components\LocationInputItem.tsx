import React, { useState } from "react";
import Image from "next/image";
import { Plane, Building2, Hotel, Globe2, MapPin } from "lucide-react";
import { autoSuggestion } from "@/models/hotel/search-page.model";



interface AutoSuggestionDropdownProps {
  header?: string;
  items: autoSuggestion[];
  onSelect: (item: autoSuggestion) => void;
  selectedIndex?: number;
  startIndex?: number; // Starting index for this group in the overall list
}

const getIconForType = (type: autoSuggestion["type"]) => {
  switch (type) {
    case "Airport":
      return <Plane size={16} className="text-primary" />;
    case "City":
      return <Building2 size={16} className="text-primary" />;
    case "Hotel":
      return <Hotel size={16} className="text-primary" />;
    case "Country":
      return <Globe2 size={16} className="text-primary" />;
    default:
      return <MapPin size={16} className="text-primary" />;
  }
};

// Function to extract country code from location data
const getCountryCode = (item: autoSuggestion): string | null => {
  // First try the direct country property
  if (item.country && item.country.length === 2) {
    return item.country.toUpperCase();
  }

  // Try to extract from fullName (e.g., "Dubai, United Arab Emirates" -> "AE")
  if (item.fullName) {
    const fullNameLower = item.fullName.toLowerCase();

    // Common country mappings
    const countryMappings: { [key: string]: string } = {
      'united arab emirates': 'AE',
      'saudi arabia': 'SA',
      'united states': 'US',
      'united kingdom': 'GB',
      'india': 'IN',
      'france': 'FR',
      'germany': 'DE',
      'italy': 'IT',
      'spain': 'ES',
      'japan': 'JP',
      'china': 'CN',
      'australia': 'AU',
      'canada': 'CA',
      'brazil': 'BR',
      'russia': 'RU',
      'south africa': 'ZA',
      'egypt': 'EG',
      'turkey': 'TR',
      'thailand': 'TH',
      'singapore': 'SG',
      'malaysia': 'MY',
      'indonesia': 'ID',
      'philippines': 'PH',
      'vietnam': 'VN',
      'south korea': 'KR',
      'mexico': 'MX',
      'argentina': 'AR',
      'chile': 'CL',
      'peru': 'PE',
      'colombia': 'CO',
      'venezuela': 'VE',
      'ecuador': 'EC',
      'bolivia': 'BO',
      'uruguay': 'UY',
      'paraguay': 'PY',
      'netherlands': 'NL',
      'belgium': 'BE',
      'switzerland': 'CH',
      'austria': 'AT',
      'portugal': 'PT',
      'greece': 'GR',
      'poland': 'PL',
      'czech republic': 'CZ',
      'hungary': 'HU',
      'romania': 'RO',
      'bulgaria': 'BG',
      'croatia': 'HR',
      'serbia': 'RS',
      'slovenia': 'SI',
      'slovakia': 'SK',
      'lithuania': 'LT',
      'latvia': 'LV',
      'estonia': 'EE',
      'finland': 'FI',
      'sweden': 'SE',
      'norway': 'NO',
      'denmark': 'DK',
      'iceland': 'IS',
      'ireland': 'IE',
      'new zealand': 'NZ',
      'qatar': 'QA',
      'kuwait': 'KW',
      'bahrain': 'BH',
      'oman': 'OM',
      'jordan': 'JO',
      'lebanon': 'LB',
      'israel': 'IL',
      'palestine': 'PS',
      'syria': 'SY',
      'iraq': 'IQ',
      'iran': 'IR',
      'afghanistan': 'AF',
      'pakistan': 'PK',
      'bangladesh': 'BD',
      'sri lanka': 'LK',
      'nepal': 'NP',
      'bhutan': 'BT',
      'maldives': 'MV',
      'myanmar': 'MM',
      'cambodia': 'KH',
      'laos': 'LA',
      'mongolia': 'MN',
      'north korea': 'KP',
      'taiwan': 'TW',
      'hong kong': 'HK',
      'macau': 'MO',
      'morocco': 'MA',
      'algeria': 'DZ',
      'tunisia': 'TN',
      'libya': 'LY',
      'sudan': 'SD',
      'ethiopia': 'ET',
      'kenya': 'KE',
      'tanzania': 'TZ',
      'uganda': 'UG',
      'rwanda': 'RW',
      'burundi': 'BI',
      'madagascar': 'MG',
      'mauritius': 'MU',
      'seychelles': 'SC',
      'comoros': 'KM',
      'djibouti': 'DJ',
      'eritrea': 'ER',
      'somalia': 'SO',
      'ghana': 'GH',
      'nigeria': 'NG',
      'senegal': 'SN',
      'mali': 'ML',
      'burkina faso': 'BF',
      'niger': 'NE',
      'chad': 'TD',
      'cameroon': 'CM',
      'central african republic': 'CF',
      'democratic republic of the congo': 'CD',
      'republic of the congo': 'CG',
      'gabon': 'GA',
      'equatorial guinea': 'GQ',
      'sao tome and principe': 'ST',
      'cape verde': 'CV',
      'guinea': 'GN',
      'guinea-bissau': 'GW',
      'sierra leone': 'SL',
      'liberia': 'LR',
      'ivory coast': 'CI',
      'togo': 'TG',
      'benin': 'BJ',
      'zambia': 'ZM',
      'zimbabwe': 'ZW',
      'botswana': 'BW',
      'namibia': 'NA',
      'lesotho': 'LS',
      'swaziland': 'SZ',
      'malawi': 'MW',
      'mozambique': 'MZ',
      'angola': 'AO'
    };

    // Check if any country name is found in the fullName
    for (const [countryName, countryCode] of Object.entries(countryMappings)) {
      if (fullNameLower.includes(countryName)) {
        return countryCode;
      }
    }
  }

  return null;
};

// Function to get country name from country code
const getCountryName = (countryCode: string): string => {
  const countryNames: { [key: string]: string } = {
    'AE': 'UAE',
    'SA': 'Saudi Arabia',
    'US': 'United States',
    'GB': 'United Kingdom',
    'IN': 'India',
    'FR': 'France',
    'DE': 'Germany',
    'IT': 'Italy',
    'ES': 'Spain',
    'JP': 'Japan',
    'CN': 'China',
    'AU': 'Australia',
    'CA': 'Canada',
    'BR': 'Brazil',
    'RU': 'Russia',
    'ZA': 'South Africa',
    'EG': 'Egypt',
    'TR': 'Turkey',
    'TH': 'Thailand',
    'SG': 'Singapore',
    'MY': 'Malaysia',
    'ID': 'Indonesia',
    'PH': 'Philippines',
    'VN': 'Vietnam',
    'KR': 'South Korea',
    'MX': 'Mexico',
    'AR': 'Argentina',
    'CL': 'Chile',
    'PE': 'Peru',
    'CO': 'Colombia',
    'VE': 'Venezuela',
    'EC': 'Ecuador',
    'BO': 'Bolivia',
    'UY': 'Uruguay',
    'PY': 'Paraguay',
    'NL': 'Netherlands',
    'BE': 'Belgium',
    'CH': 'Switzerland',
    'AT': 'Austria',
    'PT': 'Portugal',
    'GR': 'Greece',
    'PL': 'Poland',
    'CZ': 'Czech Republic',
    'HU': 'Hungary',
    'RO': 'Romania',
    'BG': 'Bulgaria',
    'HR': 'Croatia',
    'RS': 'Serbia',
    'SI': 'Slovenia',
    'SK': 'Slovakia',
    'LT': 'Lithuania',
    'LV': 'Latvia',
    'EE': 'Estonia',
    'FI': 'Finland',
    'SE': 'Sweden',
    'NO': 'Norway',
    'DK': 'Denmark',
    'IS': 'Iceland',
    'IE': 'Ireland',
    'NZ': 'New Zealand',
    'QA': 'Qatar',
    'KW': 'Kuwait',
    'BH': 'Bahrain',
    'OM': 'Oman',
    'JO': 'Jordan',
    'LB': 'Lebanon',
    'IL': 'Israel',
    'PS': 'Palestine',
    'SY': 'Syria',
    'IQ': 'Iraq',
    'IR': 'Iran',
    'AF': 'Afghanistan',
    'PK': 'Pakistan',
    'BD': 'Bangladesh',
    'LK': 'Sri Lanka',
    'NP': 'Nepal',
    'BT': 'Bhutan',
    'MV': 'Maldives',
    'MM': 'Myanmar',
    'KH': 'Cambodia',
    'LA': 'Laos',
    'MN': 'Mongolia',
    'KP': 'North Korea',
    'TW': 'Taiwan',
    'HK': 'Hong Kong',
    'MO': 'Macau',
    'MA': 'Morocco',
    'DZ': 'Algeria',
    'TN': 'Tunisia',
    'LY': 'Libya',
    'SD': 'Sudan',
    'ET': 'Ethiopia',
    'KE': 'Kenya',
    'TZ': 'Tanzania',
    'UG': 'Uganda',
    'RW': 'Rwanda',
    'BI': 'Burundi',
    'MG': 'Madagascar',
    'MU': 'Mauritius',
    'SC': 'Seychelles',
    'KM': 'Comoros',
    'DJ': 'Djibouti',
    'ER': 'Eritrea',
    'SO': 'Somalia',
    'GH': 'Ghana',
    'NG': 'Nigeria',
    'SN': 'Senegal',
    'ML': 'Mali',
    'BF': 'Burkina Faso',
    'NE': 'Niger',
    'TD': 'Chad',
    'CM': 'Cameroon',
    'CF': 'Central African Republic',
    'CD': 'DR Congo',
    'CG': 'Congo',
    'GA': 'Gabon',
    'GQ': 'Equatorial Guinea',
    'ST': 'São Tomé',
    'CV': 'Cape Verde',
    'GN': 'Guinea',
    'GW': 'Guinea-Bissau',
    'SL': 'Sierra Leone',
    'LR': 'Liberia',
    'CI': 'Ivory Coast',
    'TG': 'Togo',
    'BJ': 'Benin',
    'ZM': 'Zambia',
    'ZW': 'Zimbabwe',
    'BW': 'Botswana',
    'NA': 'Namibia',
    'LS': 'Lesotho',
    'SZ': 'Eswatini',
    'MW': 'Malawi',
    'MZ': 'Mozambique',
    'AO': 'Angola'
  };

  return countryNames[countryCode] || countryCode;
};

// Component to handle flag image with country name
const FlagWithCountry: React.FC<{ countryCode: string; countryName?: string }> = ({ countryCode, countryName }) => {
  const [imageError, setImageError] = useState(false);

  if (!countryCode) {
    return null;
  }

  const displayCountryName = countryName || getCountryName(countryCode);

  return (
    <div className="flex items-center gap-1.5">
      {!imageError && (
        <Image
          src={`/assets/img/country-logo/${countryCode}.webp`}
          alt={displayCountryName}
          width={20}
          height={14}
          className="rounded-sm border border-gray-200 object-cover flex-shrink-0"
          onError={() => {
            setImageError(true);
          }}
        />
      )}
      <span className="text-xs text-gray-600 font-medium whitespace-nowrap">
        {displayCountryName}
      </span>
    </div>
  );
};

const LocationInputItem: React.FC<AutoSuggestionDropdownProps> = ({
  header = "Suggestions",
  items,
  onSelect,
  selectedIndex = -1,
  startIndex = 0,
}) => {
  return (
    <div className="w-full bg-white shadow-lg border border-gray-200">
      {/* Header */}
      <div className="px-4 py-2 bg-gray-50 border-b border-gray-200">
        <p className="text-sm font-semibold text-gray-700">{header}</p>
      </div>

      {/* List */}
      <div className="divide-y divide-gray-100">
        {items.map((item, index) => {
          const countryCode = getCountryCode(item);
          const itemIndex = startIndex + index;
          const isSelected = selectedIndex === itemIndex;

          return (
            <div
              key={item.id}
              className={`flex items-center px-3 py-2 cursor-pointer min-h-[60px] transition-colors ${
                isSelected
                  ? 'bg-blue-50 border-l-2 border-blue-500'
                  : 'hover:bg-gray-50'
              }`}
              onClick={() => onSelect(item)}
              id={`suggestion-${itemIndex}`}
            >
              {/* Left Section - Allow to grow but reserve more space for right section */}
              <div className="flex items-start gap-2 flex-1 min-w-0 pr-4">
                <div className="flex-shrink-0">
                  {getIconForType(item.type)}
                </div>

                <div className="min-w-0 flex-1">
                  <p className="text-sm font-medium text-gray-900 truncate">{item.name}</p>
                  <div className="flex items-center gap-1 mt-0.5">
                      <span className="px-1.5 py-0.5 text-[10px] font-medium rounded-sm bg-gray-100 text-gray-600 border border-gray-200 flex-shrink-0">
                          {item.type}
                      </span>
                      <p className="text-xs text-gray-500 truncate" title={item.fullName} style={{ maxWidth: "200px" }}>
                          {item.fullName}
                      </p>
                  </div>
                </div>
              </div>

              {/* Right Section — Code + Flag + Country - Fixed width to prevent overflow */}
              <div className="flex items-center gap-2 flex-shrink-0 ml-auto">
                {item.code && (
                  <span className="text-sm font-medium text-gray-700 whitespace-nowrap">
                    {item.code}
                  </span>
                )}
                {countryCode && (
                  <FlagWithCountry
                    countryCode={countryCode}
                    countryName={item.country || getCountryName(countryCode)}
                  />
                )}
              </div>
            </div>
          );
        })}
      </div>
    </div>
  );
};

export default LocationInputItem;


import { HotelBookingRequest, HotelBookingResponse } from "@/app/BookingPreviewPage/hotel-booking-api.model";
import apiService from "../api-service";

/**
 * Service for creating hotel bookings
 * This API call is made when user clicks Pay Now button after filling guest details
 */
export const createHotelBooking = async (
  payload: HotelBookingRequest
): Promise<HotelBookingResponse> => {
  const response = await apiService.post2<HotelBookingResponse>(
    "api/v1/booking/zentrumhub/hotel/book",
    payload
  );

  return response;
};

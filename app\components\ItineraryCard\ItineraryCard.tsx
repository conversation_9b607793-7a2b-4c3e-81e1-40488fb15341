"use client";
import React from 'react';
import Image from 'next/image';
import { useTranslation } from '@/app/hooks/useTranslation';
import { useLanguage } from '@/app/contexts/languageContext';
import { formatCurrency } from '@/app/utils/currencyFormatter';

export interface ItineraryCardProps {
  data: ItineraryData;
}

export interface ItineraryData {
  bookingId: string;
  hotelName: string;
  hotelAddress: string;
  hotelRating: number;
  checkInDate: string;
  checkOutDate: string;
  checkInTime: string;
  checkOutTime: string;
  roomType: string;
  status: 'CONFIRMED' | 'PENDING' | 'CANCELLED' | 'FAILED';
  guestCount: {
    rooms: number;
    adults: number;
  };
  primaryGuest: {
    title: string;
    name: string;
    email: string;
    phone: string;
  };
  allGuests: {
    title: string;
    name: string;
    type: string;
    roomNumber?: number;
  }[];
  priceDetails: {
    roomRate: number;
    nights: number;
    taxesAndFees: number;
    resortFee: number;
    currency: string;
  };
  paymentInfo: {
    isPaid: boolean;
    cardType: string;
    cardLastDigits: string;
  };
  cancellationDeadline: string;
  hotelImage?: string;
  amenities?: string[];
}

const ItineraryCard: React.FC<ItineraryCardProps> = ({ data }) => {
  const { t } = useTranslation();
  const { isRTL } = useLanguage();

  // Get status display properties
  const getStatusDisplay = (status: string) => {
    switch (status) {
      case 'CONFIRMED':
        return {
          text: t('itinerary.confirmed'),
          bgColor: 'bg-gradient-to-r from-green-50 to-green-100',
          textColor: 'text-green-700',
          icon: 'fas fa-check-circle'
        };
      case 'PENDING':
        return {
          text: t('itinerary.pending'),
          bgColor: 'bg-gradient-to-r from-yellow-50 to-yellow-100',
          textColor: 'text-yellow-700',
          icon: 'fas fa-clock'
        };
      case 'CANCELLED':
        return {
          text: t('itinerary.cancelled'),
          bgColor: 'bg-gradient-to-r from-red-50 to-red-100',
          textColor: 'text-red-700',
          icon: 'fas fa-ban'
        };
      case 'FAILED':
        return {
          text: t('itinerary.failed'),
          bgColor: 'bg-gradient-to-r from-red-50 to-red-100',
          textColor: 'text-red-700',
          icon: 'fas fa-exclamation-triangle'
        };
      default:
        return {
          text: t('itinerary.confirmed'),
          bgColor: 'bg-gradient-to-r from-green-50 to-green-100',
          textColor: 'text-green-700',
          icon: 'fas fa-check-circle'
        };
    }
  };

  const statusDisplay = getStatusDisplay(data.status);

  // Get payment status display properties
  const getPaymentStatusDisplay = (isPaid: boolean) => {
    if (isPaid) {
      return {
        textColor: 'text-green-600',
        icon: 'fas fa-check-circle'
      };
    } else {
      return {
        textColor: 'text-yellow-600',
        icon: 'fas fa-clock'
      };
    }
  };

  const paymentStatusDisplay = getPaymentStatusDisplay(data.paymentInfo.isPaid);

  // Helper function to check if a value indicates missing data
  const isDataNotAvailable = (value: string) => {
    return value.includes('not available') || value.includes('information not available');
  };



  // Get amenity icon based on amenity name
  const getAmenityIcon = (amenity: string) => {
    const amenityLower = amenity.toLowerCase();
    if (amenityLower.includes('wifi') || amenityLower.includes('internet')) return 'fas fa-wifi';
    if (amenityLower.includes('breakfast') || amenityLower.includes('meal')) return 'fas fa-utensils';
    if (amenityLower.includes('pool') || amenityLower.includes('swimming')) return 'fas fa-swimming-pool';
    if (amenityLower.includes('parking') || amenityLower.includes('garage')) return 'fas fa-parking';
    if (amenityLower.includes('spa') || amenityLower.includes('wellness')) return 'fas fa-spa';
    if (amenityLower.includes('gym') || amenityLower.includes('fitness')) return 'fas fa-dumbbell';
    if (amenityLower.includes('air') || amenityLower.includes('conditioning')) return 'fas fa-snowflake';
    if (amenityLower.includes('tv') || amenityLower.includes('television')) return 'fas fa-tv';
    if (amenityLower.includes('bar') || amenityLower.includes('lounge')) return 'fas fa-cocktail';
    if (amenityLower.includes('room service')) return 'fas fa-concierge-bell';
    return 'fas fa-check'; // Default icon
  };

  // Calculate total amount
  const totalAmount =
    data.priceDetails.roomRate +
    data.priceDetails.taxesAndFees +
    data.priceDetails.resortFee;

  // Default hotel image if not provided
  const hotelImageSrc = data.hotelImage || "https://via.placeholder.com/100";

  return (
    <div className="bg-white rounded-md shadow p-5 mb-6 hover:shadow-md transition-all duration-300">
      {/* Header with Booking Info and Action Buttons */}
      <div className="flex justify-between items-start mb-5">
        <div>
          <h2 className="text-2xl font-bold text-gray-800 mb-2">
            {t('itinerary.bookingConfirmation')}
          </h2>
          <p className="text-base text-gray-600">
            {t('itinerary.bookingId')}: <span className="font-medium">{data.bookingId}</span>
          </p>
          <div className={`mt-2 inline-flex items-center px-3 py-1 rounded-full ${statusDisplay.bgColor} ${statusDisplay.textColor} text-sm`}>
            <i className={`${statusDisplay.icon} ${isRTL ? 'ml-1.5' : 'mr-1.5'}`}></i>
            {statusDisplay.text}
          </div>
        </div>
        <div className="text-right space-y-2">
  <button className="w-full md:w-auto px-3 py-1.5 text-base text-gray-600 hover:text-gray-800 flex items-center justify-end hover:bg-gray-50 rounded transition-all duration-300">
    <i className={`fas fa-print ${isRTL ? 'ml-0 md:ml-2' : 'mr-0 md:mr-2'}`}></i>
    <span className="hidden sm:inline">{t('itinerary.print')}</span>
  </button>
  <button className="w-full md:w-auto px-3 py-1.5 text-base text-gray-600 hover:text-gray-800 flex items-center justify-end hover:bg-gray-50 rounded transition-all duration-300">
    <i className={`fas fa-download ${isRTL ? 'ml-0 md:ml-2' : 'mr-0 md:mr-2'}`}></i>
    <span className="hidden sm:inline">{t('itinerary.downloadPdf')}</span>
  </button>
</div>

      </div>

      {/* Main Content Grid */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-5">
        {/* Left Column (2/3 width on medium screens and up) */}
        <div className="md:col-span-2">
          {/* Hotel Information */}
          <div className="bg-gray-50 rounded-md p-4 mb-4">
            <div className="flex items-start">
              <Image
                width={80}
                height={80}
                src={hotelImageSrc}
                alt={data.hotelName}
                className="w-20 h-20 rounded object-cover mr-4"
              />
              <div>
                <h3 className={`text-lg font-bold mb-1 ${isDataNotAvailable(data.hotelName) ? 'text-gray-400 italic' : 'text-gray-800'}`}>
                  {data.hotelName}
                </h3>
                <p className="text-sm text-gray-600 mb-1.5 flex items-center">
                  <i style={{ color: "var(--primary-color)" }} className="fas fa-map-marker-alt mr-1.5"></i>
                  {data.hotelAddress}
                </p>
                <div className="flex items-center text-sm">
                  <div className="flex items-center text-yellow-400 mr-1.5">
                    {Array.from({ length: Math.max(0, data.hotelRating || 0) }).map((_, i) => (
                      <i key={i} className="fas fa-star"></i>
                    ))}
                  </div>
                  <span className="text-gray-600">{data.hotelRating}{t('itinerary.starHotel')}</span>
                </div>
              </div>
            </div>
          </div>

          {/* Stay Details */}
          <div className="bg-gray-50 rounded-md p-4 mb-4">
            <h4 className="text-base font-bold text-gray-800 mb-3">
              {t('itinerary.stayDetails')}
            </h4>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
              <div>
                <p className="text-sm text-gray-600 mb-1">{t('itinerary.checkIn')}</p>
                <div className="flex items-center">
                  <i style={{ color: "var(--primary-color)" }} className={`far fa-calendar-alt ${isRTL ? 'ml-1.5' : 'mr-1.5'}`}></i>
                  <span className="text-sm font-medium">{data.checkInDate}</span>
                  <span className={`text-sm text-gray-500 ${isRTL ? 'mr-1.5' : 'ml-1.5'}`}>
                    ({data.checkInTime})
                  </span>
                </div>
              </div>
              <div>
                <p className="text-sm text-gray-600 mb-1">{t('itinerary.checkOut')}</p>
                <div className="flex items-center">
                  <i style={{ color: "var(--primary-color)" }} className={`far fa-calendar-alt ${isRTL ? 'ml-1.5' : 'mr-1.5'}`}></i>
                  <span className="text-sm font-medium">{data.checkOutDate}</span>
                  <span className={`text-sm text-gray-500 ${isRTL ? 'mr-1.5' : 'ml-1.5'}`}>
                    ({data.checkOutTime})
                  </span>
                </div>
              </div>
            </div>
            <div className="mt-3">
              <p className="text-sm text-gray-600 mb-1">{t('itinerary.roomType')}</p>
              <div className="flex items-center">
                <i style={{ color: "var(--primary-color)" }} className={`fas fa-bed ${isRTL ? 'ml-1.5' : 'mr-1.5'}`}></i>
                <span className={`text-sm font-medium ${isDataNotAvailable(data.roomType) ? 'text-gray-400 italic' : ''}`}>
                  {data.roomType}
                </span>
                <span className={`text-sm text-gray-500 ${isRTL ? 'mr-1.5' : 'ml-1.5'}`}>
                  ({data.guestCount.rooms} {t(data.guestCount.rooms === 1 ? 'itinerary.room' : 'itinerary.rooms')},
                  {data.guestCount.adults} {t(data.guestCount.adults === 1 ? 'itinerary.adult' : 'itinerary.adults')})
                </span>
              </div>
            </div>
          </div>

          {/* Guest Details */}
          <div className="bg-gray-50 rounded-md p-4 mb-4">
            <h4 className="text-base font-bold text-gray-800 mb-3">
              {t('itinerary.guestDetails')}
            </h4>
            <div className="space-y-3">
              {/* Primary Guest Contact Info */}
              <div className="border-b border-gray-200 pb-3">
                <div className="flex items-center mb-2">
                  <i style={{ color: "var(--primary-color)" }} className={`fas fa-user ${isRTL ? 'ml-2.5' : 'mr-2.5'}`}></i>
                  <div>
                    <p className="text-sm font-medium">{t('itinerary.primaryGuest')}</p>
                    <p className={`text-sm ${isDataNotAvailable(data.primaryGuest.name) ? 'text-gray-400 italic' : 'text-gray-600'}`}>
                      {data.primaryGuest.title} {data.primaryGuest.name}
                    </p>
                  </div>
                </div>
                <div className="flex items-center mb-1">
                  <i style={{ color: "var(--primary-color)" }} className={`fas fa-envelope ${isRTL ? 'ml-2.5' : 'mr-2.5'}`}></i>
                  <p className="text-sm text-gray-600">{data.primaryGuest.email}</p>
                </div>
                <div className="flex items-center">
                  <i style={{ color: "var(--primary-color)" }} className={`fas fa-phone ${isRTL ? 'ml-2.5' : 'mr-2.5'}`}></i>
                  <p className="text-sm text-gray-600">{data.primaryGuest.phone}</p>
                </div>
              </div>

              {/* All Guests List */}
              {data.allGuests && data.allGuests.length > 0 && (
                <div>
                  <p className="text-sm font-medium text-gray-800 mb-2">
                    {t('itinerary.allGuests')} ({data.allGuests.length})
                  </p>
                  <div className="space-y-2">
                    {data.allGuests.map((guest, index) => (
                      <div key={index} className="flex items-center justify-between bg-white rounded p-2">
                        <div className="flex items-center">
                          <i style={{ color: "var(--primary-color)" }} className={`fas fa-user-circle ${isRTL ? 'ml-2' : 'mr-2'} text-sm`}></i>
                          <div>
                            <p className="text-sm font-medium text-gray-700">
                              {guest.title} {guest.name}
                            </p>
                            {guest.roomNumber && (
                              <p className="text-xs text-gray-500">
                                {t('itinerary.room')} {guest.roomNumber}
                              </p>
                            )}
                          </div>
                        </div>
                        <span className="text-xs bg-blue-100 text-blue-700 px-2 py-1 rounded-full">
                          {guest.type === 'adult' ? t('itinerary.adult') : t('itinerary.child')}
                        </span>
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </div>
          </div>

          {/* Included Amenities */}
          {data.amenities && data.amenities.length > 0 && (
            <div className="bg-gray-50 rounded-md p-4">
              <h4 className="text-base font-bold text-gray-800 mb-3">
                {t('itinerary.includedAmenities')}
              </h4>
              <div className="grid grid-cols-2 gap-3">
                {data.amenities.map((amenity, index) => (
                  <div key={index} className="flex items-center">
                    <i style={{ color: "var(--primary-color)" }} className={`${getAmenityIcon(amenity)} ${isRTL ? 'ml-2.5' : 'mr-2.5'}`}></i>
                    <span className="text-sm text-gray-600">{amenity}</span>
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>

        {/* Right Column (1/3 width on medium screens and up) */}
        <div className="md:col-span-1">
          {/* Price Details */}
          <div className="bg-gray-50 rounded-md p-4 mb-4">
            <h4 className="text-base font-bold text-gray-800 mb-3">
              {t('itinerary.priceDetails')}
            </h4>
            <div className="space-y-2">
              <div className="flex justify-between">
                <span className="text-sm text-gray-600">
                  {t('itinerary.roomRate')} ({data.priceDetails.nights} {t('itinerary.nights')})
                </span>
                <span className="text-sm font-medium">{formatCurrency(data.priceDetails.roomRate, data.priceDetails.currency)}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-sm text-gray-600">{t('itinerary.taxesAndFees')}</span>
                <span className="text-sm font-medium">{formatCurrency(data.priceDetails.taxesAndFees, data.priceDetails.currency)}</span>
              </div>
              {/* <div className="flex justify-between">
                <span className="text-sm text-gray-600">{t('itinerary.resortFee')}</span>
                <span className="text-sm font-medium">{formatCurrency(data.priceDetails.resortFee, data.priceDetails.currency)}</span>
              </div> */}
              <div className="pt-2 border-t border-gray-200 flex justify-between">
                <span className="text-sm font-bold">{t('itinerary.totalAmount')}</span>
                <span style={{ color: "var(--primary-color)" }} className="text-sm font-bold">{formatCurrency(totalAmount, data.priceDetails.currency)}</span>
              </div>
            </div>
          </div>

          {/* Payment Status */}
          <div className="bg-gray-50 rounded-md p-4 mb-4">
            <h4 className="text-base font-bold text-gray-800 mb-3">
              {t('itinerary.paymentStatus')}
            </h4>
            <div className="space-y-2">
              <div className={`flex items-center ${paymentStatusDisplay.textColor}`}>
                <i className={`${paymentStatusDisplay.icon} ${isRTL ? 'ml-1.5' : 'mr-1.5'}`}></i>
                <span className="text-sm font-medium">
                  {data.paymentInfo.isPaid ? t('itinerary.paidInFull') : t('common.paymentPending')}
                </span>
              </div>
              {data.paymentInfo.isPaid && (
                <div className="text-gray-600">
                  <p className="text-sm mb-1 font-medium">{t('itinerary.paymentMethod')}</p>
                  <div className="flex items-center">
                    <i className={`fab fa-cc-${data.paymentInfo.cardType.toLowerCase()} ${isRTL ? 'ml-1.5' : 'mr-1.5'}`}></i>
                    <span className="text-sm">{data.paymentInfo.cardType} {t('itinerary.cardEnding')} {data.paymentInfo.cardLastDigits}</span>
                  </div>
                </div>
              )}
            </div>
          </div>

          {/* Cancellation Policy */}
          <div className="bg-gray-50 rounded-md p-4">
            <h4 className="text-base font-bold text-gray-800 mb-3">
              {t('itinerary.cancellationPolicy')}
            </h4>
            <p className="text-sm text-gray-600 mb-3">
              {t('itinerary.freeCancellation')} {data.cancellationDeadline} (48 hours before
              check-in). {t('itinerary.cancellationFee')}
            </p>
            {(data.status === 'CONFIRMED' || data.status === 'PENDING') && (
              <button className="mt-2 w-full bg-white border border-red-500 text-red-500 hover:bg-red-50 px-4 py-1.5 rounded text-sm transition-colors">
                {t('itinerary.cancelBooking')}
              </button>
            )}
          </div>
        </div>
      </div>

      {/* Need Assistance Section */}
      <div className="mt-5 bg-blue-50 rounded-md p-4">
        <div className="flex items-start">
          <i className={`fas fa-info-circle text-blue-500 mt-0.5 ${isRTL ? 'ml-2.5' : 'mr-2.5'}`}></i>
          <div>
            <h4 className="text-base font-bold text-gray-800 mb-2">
              {t('itinerary.needAssistance')}
            </h4>
            <p className="text-sm text-gray-600 mb-3">
              {t('itinerary.supportMessage')}
            </p>
            <button className="bg-white text-blue-500 hover:bg-blue-500 hover:text-white px-4 py-1.5 rounded text-sm transition-colors">
              {t('itinerary.contactSupport')}
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ItineraryCard;

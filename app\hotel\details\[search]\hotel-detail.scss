@use "/styles/variable" as *;
@use "/styles/zIndex" as *;
@use "sass:color";

.hotel-detail-container{
    width: 100%;
    height: auto;
    padding: 10px 0;
    position: relative;

    .mobile-header-info {
        flex: 1;
        display: flex;
        flex-direction: column;
        gap: 4px;
        min-width: 0; // Allow flex item to shrink

        .hotel-name-with-rating {
          display: flex;
          align-items: center;
          gap: 8px;
          min-width: 0; // Allow flex item to shrink

          .hotel-name {
            font-size: 16px;
            font-weight: 600;
            color: #17181c;
            margin: 0;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            line-height: 1.2;
            //flex: 1;
            min-width: 0; // Allow flex item to shrink
          }

          .rating-stars {
            display: flex;
            align-items: center;
            gap: 1px;
            flex-shrink: 0; // Prevent stars from shrinking

            i.fa-star {
              font-size: 10px;
              color: $rating_color;
            }
          }
        }

        .mobile-date-range {
          // Compact date range styling is handled in the DateRangeDisplay component
          margin: 0;
        }
    }

    .navigation-tabs-container {
      &.sticky {
        position: sticky;
        width: 100%;
        top: 60px;
        background-color: #f2f2f2;
        left: 0;
        z-index: z-index(sticky);
        //border-top: 1px solid rgba(0, 0, 0, 0.2);

        // @media(max-width: $isMobile){
        //   top: 60px;
        // }
      }

      .navigation-tabs {
        display: flex;
        flex-direction: row;
        width: 100%;
        overflow-y: auto;
        //border-bottom: 1px solid rgba(0, 0, 0, 0.2);

        scrollbar-width: none; // Firefox
        -ms-overflow-style: none; // Internet Explorer and Edge

        &::-webkit-scrollbar {
          display: none; // Chrome, Safari, and Opera
        }

        .navigation-tab {
          padding: 10px 15px;
          font-size: 16px;
          font-weight: 500;
          color: #17181c;
          position: relative;
          cursor: pointer;
          user-select: none;

          @media (max-width: $breakpoint-sm) {
            font-size: 15px;
          }

          &:hover {
            background: rgba(0, 0, 0, 0.05);
          }

          &.active {
            //color: #0770e4;
            color: $secondary-color;
          }

          &.active::after {
            content: "";
            position: absolute;
            left: 0;
            bottom: 0;
            width: 100%;
            height: 3px;
            //background-color: #0770e4;
            background-color: $secondary-color;
            border-radius: 5px;
          }
        }
      }
    }

    .overview-section{
        display: flex;
        flex-direction: column;

        .route-path {
          display: flex;
          align-items: center;
          gap: 5px;
          flex-wrap: nowrap;
          margin-bottom: 16px;
          padding: 16px 8px 0 8px;
          font-size: 12px;
          span {
            color: $primary_color;
          }

          .fa-greater-than {
            font-size: 9px;
            margin-bottom: -2px;
          }

          @media (max-width: $isMobile) {
            display: none;
          }
        }
    }
}
"use client";
import React, {  useState } from "react";
import "./CancellationPolicy.scss";
import SlideFromRightModal from "../../../HotelDetail/components/SlideFromRightModal/SlideFromRightModal";
import useScrollLock from "@/app/components/utilities/ScrollLock/useScrollLock";
import { CancellationPolicy as CancellationPolicyType, CancellationRule } from "../../booking-init.model";

interface cancellationPolicyProps {
  cancellationPolicies: CancellationPolicyType[];
  refundable: boolean;
  totalRate: number;
  currency?: string;
}

function CancellationPolicy({ cancellationPolicies, refundable, totalRate, currency = "INR" }: cancellationPolicyProps) {
  const [isModalOpen, setIsModalOpen] = useState<boolean>(false);

  const handleCloseModal = () => {
    setIsModalOpen(false);
  };

  // Format currency symbol
  const currencySymbol = currency === "INR" ? "₹" : currency === "USD" ? "$" : currency;

  const formatPolicyCode = (code: string) => {
    return code
      .toLowerCase() // Convert to lowercase
      .replace(/_/g, " ") // Replace underscores with spaces
      .replace(/\b\w/g, (char) => char.toUpperCase()); // Capitalize first letter of each word
  };

  const formatDateTime = (dateTimeString: string | undefined) => {
    if (!dateTimeString) {
      return { date: "", time: "" };
    }

    const dateObj = new Date(dateTimeString);

    // Format date as "01 Apr"
    const optionsDate: Intl.DateTimeFormatOptions = {
      day: "2-digit",
      month: "short",
    };
    const date = dateObj.toLocaleDateString("en-GB", optionsDate);

    // Format time as "11:59 PM"
    const optionsTime: Intl.DateTimeFormatOptions = {
      hour: "2-digit",
      minute: "2-digit",
      hour12: true,
    };
    const time = dateObj.toLocaleTimeString("en-US", optionsTime);

    return { date, time };
  };

  useScrollLock(isModalOpen);

  // Process cancellation rules from booking init API
  const processedRules = cancellationPolicies?.[0]?.rules || [];

  // Generate policy description based on refundability
  const policyDescription = refundable
    ? "This booking has flexible cancellation terms. See details below."
    : "This booking is non-refundable. 100% amount will be deducted on cancellation.";

  return (
    <div className="cancellation-policy">
      <div className="cancellation-policy__header">Cancellation Policy</div>

      <p className="cancellation-policy__description">
        {policyDescription}
      </p>

      <div className="cancellation-policy__timeline">
        {processedRules.length > 0 ? (
          processedRules.map((rule: CancellationRule, index: number) => {
            // Use end date for cancellation deadlines
            const { date: endDate, time: endTime } = formatDateTime(rule.end);

            // Determine policy type based on refundability and value
            const policyCode = refundable ?
              (rule.value === 0 ? "FULLY_REFUNDABLE" : "PARTIALLY_REFUNDABLE") :
              "NON_REFUNDABLE";

            // Generate title based on policy
            const policyTitle = refundable ?
              (rule.value === 0 ? "Fully Refundable" : `${rule.value}% Cancellation Charge`) :
              "Non-refundable";

            return (
              <div
                key={index}
                className={`timeline-content ${!refundable ? "non-refundable" : ""}`}
              >
                <p className="timeline-title">
                  {policyTitle}
                </p>

                <div className="timeline-divider">
                  <div className={`line ${policyCode}`}></div>
                  <div
                    className={`timeline-label-dot ${
                      index !== 0 && "second-style-dot"
                    }`}
                  >
                    {index === 0 ? (
                      <>
                        <div className="dot left">
                          <span className="icon">
                            <i className="fa-solid fa-check"></i>
                          </span>
                        </div>
                      </>
                    ) : (
                      <div className="dot2 left">
                        <span className="icon"></span>
                      </div>
                    )}
                    {processedRules.length > 1 && index === 0 ? (
                      <div className="label">Now</div>
                    ) : (
                      <div className="label-time">
                        <span className="label">{endDate || "Check-in"}</span>
                        <span className="time">{endTime || "11:59 PM"}</span>
                      </div>
                    )}
                  </div>
                  {index === processedRules.length - 1 && (
                    <div className="timeline-label-dot right">
                      <div className="dot">
                        <span className="icon">
                          <i className="fa-solid fa-key"></i>
                        </span>
                      </div>
                      <div className="label-time">
                        <span className="label">Check-In</span>
                      </div>
                    </div>
                  )}
                </div>
              </div>
            );
          })
        ) : (
          <></>
        )}
      </div>

      <div className="cancellation-policy__link">
        <div
          className="view-cancellation-link"
          onClick={() => setIsModalOpen(true)}
        >
          View Cancellation Policy
        </div>
      </div>

      <SlideFromRightModal
        isOpen={isModalOpen}
        handleClose={handleCloseModal}
        title="Cancellation Policy"
        component_name="CancellationPolicy"
      >
        <div className="cancellation-policy-modal-content">
          <div className="policy-summary">
            <h4>Booking Summary</h4>
            <p><strong>Refundable:</strong> {refundable ? "Yes" : "No"}</p>
            <p><strong>Total Amount:</strong> {currencySymbol}{totalRate.toLocaleString()}</p>
          </div>

          <div className="cancellation-details-timeline-refundable">
            {processedRules.length > 0 ? (
              processedRules.map((rule: CancellationRule, index: number) => {
                const { date: startDate, time: startTime } = formatDateTime(rule.start);
                const { date: endDate, time: endTime } = formatDateTime(rule.end);

                const cancellationAmount = rule.valueType === "Amount" ?
                  rule.value :
                  (totalRate * rule.value / 100);

                return (
                  <div
                    className="timeline-label-cancellation-details"
                    key={index}
                  >
                    <div className="timeline-container">
                      <div className="timeline-divider">
                        <div className="dot left">
                          <span className="icon"></span>
                        </div>
                        <div className={`line ${refundable ? "REFUNDABLE" : "NON_REFUNDABLE"}`}></div>
                        {index === processedRules.length - 1 && (
                          <div className="dot right">
                            <span className="icon"></span>
                          </div>
                        )}
                      </div>
                    </div>
                    <div className="timeline-label-cancellation-details">
                      {processedRules.length > 1 && index === 0 ? (
                        <span className="timline-label">Now</span>
                      ) : (
                        <p className="timline-label">
                          {startDate || "Booking"} • {startTime || ""}
                        </p>
                      )}

                      <div className={`cancellation-details ${refundable ? "REFUNDABLE" : "NON_REFUNDABLE"}`}>
                        <p className="title">
                          {refundable
                            ? `${rule.value}% cancellation charge from ${startDate} to ${endDate}`
                            : "Non-refundable booking"
                          }
                        </p>
                        <p className="description">
                          {refundable
                            ? `Cancellation charge: ${currencySymbol}${cancellationAmount.toLocaleString()}`
                            : "100% amount will be deducted on cancellation"
                          }
                        </p>
                      </div>
                      {index === processedRules.length - 1 && (
                        <p className="timline-label">Check-In</p>
                      )}
                    </div>
                  </div>
                );
              })
            ) : (
              <></>
            )}
          </div>

          <div className="note-container">
            <h6 className="note-container__title">Note:</h6>

            <p className="note-container__description">
              Date and time is displayed as per the local time zone of your
              hotel’s location.
            </p>
          </div>
        </div>
      </SlideFromRightModal>
    </div>
  );
}

export default CancellationPolicy;

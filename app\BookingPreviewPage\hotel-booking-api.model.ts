// Hotel Booking API Models

export interface HotelBookingRequest {
  search_key: string;
  hotel_id: string;
  rateIds: string[];
  user_id: number;
  roomsAllocations: RoomAllocation[];
  billingContact: BillingContact;
}

export interface RoomAllocation {
  roomid: string;
  rateid: string;
  guests: Guest[];
}

export interface Guest {
  type: "Adult" | "Child" | "Infant";
  title: "Mr" | "Mrs" | "Miss" | "Dr" | "Prof";
  firstname: string;
  lastname: string;
  age: number;
  email: string;
}

export interface BillingContact {
  title: string;
  firstName: string;
  lastName: string;
  age: number;
  contact: ContactInfo;
}

export interface ContactInfo {
  phone: string;
  address: Address;
  email: string;
}

export interface Address {
  line1: string;
  line2: string;
  city: City;
  state: State;
  country: Country;
  postalCode: string;
}

export interface City {
  name: string;
  code: string;
}

export interface State {
  name: string;
  code: string;
}

export interface Country {
  name: string;
  code: string;
}

// Booking API Response Models
// export interface HotelBookingResponse {
//   success: boolean;
//   message: string;
//   data: {
//     bookingId: string;
//     bookingReference: string;
//     status: string;
//     totalAmount: number;
//     currency: string;
//     paymentUrl?: string;
//     confirmationDetails?: any;
//   };
//   errors?: string[];
// }


export interface HotelBookingResponse {
  booking_reference : string;
  service_type: string;
  status: string;
  payment_status: string;
}

// Error Response Model
export interface BookingErrorResponse {
  success: false;
  message: string;
  errors: string[];
  errorCode?: string;
}

import { BookingDetailsApiResponse } from "@/app/BookingConfirmation/booking-details-api.model";
import { ItineraryData } from "@/app/components/ItineraryCard/ItineraryCard";
import apiService from "../api-service";

/**
 * Service for fetching booking details by booking reference
 * This API call is made on the BookingConfirmation page to get complete booking information
 */
export const getBookingDetails = async (
  bookingReference: string
): Promise<BookingDetailsApiResponse> => {
  const response = await apiService.get3<BookingDetailsApiResponse>(
    `api/v1/booking/${bookingReference}`
  );

  // Log successful booking details retrieval
  console.log('✅ Booking details service - API response received:', {
    bookingReference: response.booking_reference,
    status: response.status,
    paymentStatus: response.payment_status,
    serviceType: response.service_type,
    hotelName: response.hotel_booking?.provider_response?.hotelBooking?.hotel?.name,
    totalRate: response.hotel_booking?.provider_response?.hotelBooking?.totalRate
  });

  return response;
};

/**
 * Helper function to extract key booking information for display
 */
export const extractBookingDisplayData = (bookingDetails: BookingDetailsApiResponse) => {
  const hotelBooking = bookingDetails.hotel_booking?.provider_response?.hotelBooking;
  const hotel = hotelBooking?.hotel;
  const rates = hotelBooking?.rates || [];
  const rooms = hotelBooking?.rooms || [];
  const billingContact = hotelBooking?.billingContact;

  // Helper function to safely parse and validate dates
  const parseAndValidateDate = (dateString: string | undefined, fallback: string): string => {
    try {
      if (!dateString) throw new Error('No date provided');
      const date = new Date(dateString);
      if (isNaN(date.getTime())) throw new Error('Invalid date');
      return date.toISOString().split('T')[0];
    } catch (error) {
      console.warn('Invalid date encountered, using fallback:', { dateString, error });
      return fallback;
    }
  };

  // Calculate total nights with validation
  const startDate = hotelBooking?.tripStartDate ? new Date(hotelBooking.tripStartDate) : new Date();
  const endDate = hotelBooking?.tripEndDate ? new Date(hotelBooking.tripEndDate) : new Date();
  
  // Ensure dates are valid before calculations
  const isValidDateRange = !isNaN(startDate.getTime()) && !isNaN(endDate.getTime()) && endDate > startDate;
  const nights = isValidDateRange 
    ? Math.ceil((endDate.getTime() - startDate.getTime()) / (1000 * 60 * 60 * 24))
    : 1; // Default to 1 night if dates are invalid

  // Get primary rate for pricing information
  const primaryRate = rates[0];
  const totalAmount = primaryRate?.totalRate || 0;
  const baseRate = primaryRate?.baseRate || 0;
  const taxes = primaryRate?.taxes?.reduce((sum, tax) => sum + (tax.amount || 0), 0) || 0;

  // Format dates with validation
  const checkInDate = parseAndValidateDate(hotelBooking?.tripStartDate, new Date().toISOString().split('T')[0]);
  const checkOutDate = parseAndValidateDate(hotelBooking?.tripEndDate, new Date(Date.now() + 86400000).toISOString().split('T')[0]);

  // Get guest count
  const totalGuests = hotelBooking?.roomsAllocations?.reduce((total, allocation) => {
    return total + allocation.guests.length;
  }, 0) || 0;

  // Get room type (first room)
  const roomType = rooms[0]?.name || 'Room details not available';

  return {
    confirmationNumber: bookingDetails.booking_reference,
    bookingId: hotelBooking?.bookingId,
    status: bookingDetails.status,
    paymentStatus: bookingDetails.payment_status,
    hotel: {
      name: hotel?.name || 'Hotel information not available',
      address: hotel?.contact?.address?.line1 || '',
      city: hotel?.contact?.address?.city?.name || '',
      country: hotel?.contact?.address?.country?.name || '',
      phone: hotel?.contact?.phone || '',
      email: hotel?.contact?.email || '',
      starRating: hotel?.starRating || '0',
      heroImage: hotel?.heroImage || '',
      checkInTime: hotel?.checkinInfo?.beginTime || '15:00',
      checkOutTime: hotel?.checkoutInfo?.time || '12:00'
    },
    booking: {
      checkIn: checkInDate,
      checkOut: checkOutDate,
      nights: nights,
      guests: totalGuests,
      roomType: roomType,
      bookingDate: bookingDetails.created_at.split('T')[0],
      bookingStatus: hotelBooking?.bookingStatus || 'Status not available'
    },
    pricing: {
      baseRate: baseRate,
      taxes: taxes,
      totalAmount: totalAmount,
      currency: primaryRate?.currency || 'Currency not available'
    },
    guest: {
      name: `${billingContact?.firstName || ''} ${billingContact?.lastName || ''}`.trim() || 'Guest information not available',
      email: billingContact?.contact?.email || '',
      phone: billingContact?.contact?.phone || ''
    },
    rooms: rooms.map(room => ({
      id: room.id,
      name: room.name,
      description: room.description,
      facilities: room.facilities?.map(f => f.name) || []
    })),
    cancellation: {
      refundable: primaryRate?.refundable || false,
      cancellationPolicies: primaryRate?.cancellationPolicies || []
    }
  };
};

/**
 * Transform BookingDetailsApiResponse to ItineraryData format for ItineraryCard component
 */
export const transformToItineraryData = (bookingDetails: BookingDetailsApiResponse): ItineraryData => {
  const hotelBooking = bookingDetails.hotel_booking?.provider_response?.hotelBooking;
  const hotel = hotelBooking?.hotel;
  const rates = hotelBooking?.rates || [];
  const rooms = hotelBooking?.rooms || [];
  const billingContact = hotelBooking?.billingContact;

  // Helper function to safely parse dates with fallback
  const parseDateSafe = (dateString: string | undefined, fallback: Date = new Date()): Date => {
    if (!dateString) return fallback;
    const date = new Date(dateString);
    return isNaN(date.getTime()) ? fallback : date;
  };

  // Calculate total nights with validation
  const startDate = parseDateSafe(hotelBooking?.tripStartDate);
  const endDate = parseDateSafe(hotelBooking?.tripEndDate, new Date(startDate.getTime() + 86400000));
  
  // Ensure end date is after start date
  const validEndDate = endDate > startDate ? endDate : new Date(startDate.getTime() + 86400000);
  const nights = Math.ceil((validEndDate.getTime() - startDate.getTime()) / (1000 * 60 * 60 * 24));

  // Get primary rate for pricing information
  const primaryRate = rates[0];
  const totalAmount = primaryRate?.totalRate || 0;
  const baseRate = primaryRate?.baseRate || 0;
  const taxes = primaryRate?.taxes?.reduce((sum, tax) => sum + tax.amount, 0) || 0;
  const resortFee = 0; // This might need to be calculated from fees if available

  // Format dates for display with validation
  const formatDate = (dateString: string | undefined): string => {
    try {
      if (!dateString) return 'Date not available';
      const date = new Date(dateString);
      if (isNaN(date.getTime())) return 'Invalid date';
      
      return date.toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'long',
        day: 'numeric'
      });
    } catch (error) {
      console.warn('Error formatting date:', { dateString, error });
      return 'Date not available';
    }
  };

  // Get guest count from room allocations
  const totalRooms = hotelBooking?.roomsAllocations?.length || 1;
  const totalAdults = hotelBooking?.roomsAllocations?.reduce((total, allocation) => {
    return total + allocation.guests.filter(guest => guest.type === 'adult').length;
  }, 0) || 2;

  // Extract all guests from room allocations
  const extractAllGuests = () => {
    if (!hotelBooking?.roomsAllocations) return [];

    const allGuests: { title: string; name: string; type: string; roomNumber?: number }[] = [];

    hotelBooking.roomsAllocations.forEach((allocation, roomIndex) => {
      allocation.guests.forEach(guest => {
        allGuests.push({
          title: guest.title || '',
          name: `${guest.firstName || ''} ${guest.lastName || ''}`.trim(),
          type: guest.type || 'adult',
          roomNumber: roomIndex + 1
        });
      });
    });

    return allGuests;
  };

  // Get room type (first room)
  const roomType = rooms[0]?.name || 'Room details not available';

  // Format address
  const address = hotel?.contact?.address;
  const fullAddress = [
    address?.line1,
    address?.city?.name,
    address?.country?.name
  ].filter(Boolean).join(', ');

  // Format check-in/out times
  const checkInTime = hotel?.checkinInfo?.beginTime ?
    `After ${hotel.checkinInfo.beginTime}` : 'After 2:00 PM';
  const checkOutTime = hotel?.checkoutInfo?.time ?
    `Before ${hotel.checkoutInfo.time}` : 'Before 12:00 PM';

  // Determine payment status
  const isPaid = bookingDetails.payment_status === 'COMPLETED' ||
                 bookingDetails.payment_status === 'SUCCESS';

  // Calculate cancellation deadline (typically 24-48 hours before check-in)
  const calculateCancellationDeadline = (checkInDate: Date): string => {
    try {
      const cancellationDate = new Date(checkInDate);
      cancellationDate.setDate(cancellationDate.getDate() - 2); // 2 days before check-in
      
      // Ensure the date is valid before formatting
      if (isNaN(cancellationDate.getTime())) {
        throw new Error('Invalid cancellation date calculated');
      }
      
      return formatDate(cancellationDate.toISOString());
    } catch (error) {
      console.warn('Error calculating cancellation deadline:', error);
      return 'Check booking policy';
    }
  };
  
  const cancellationDeadline = calculateCancellationDeadline(startDate);

  return {
    bookingId: bookingDetails.booking_reference,
    hotelName: hotel?.name || 'Hotel information not available',
    hotelAddress: fullAddress,
    hotelRating: parseInt(hotel?.starRating || '0'),
    checkInDate: formatDate(hotelBooking?.tripStartDate || ''),
    checkOutDate: formatDate(hotelBooking?.tripEndDate || ''),
    checkInTime,
    checkOutTime,
    roomType,
    status: bookingDetails.status as 'CONFIRMED' | 'PENDING' | 'CANCELLED' | 'FAILED',
    guestCount: {
      rooms: totalRooms,
      adults: totalAdults
    },
    primaryGuest: {
      title: billingContact?.title || '',
      name: `${billingContact?.firstName || ''} ${billingContact?.lastName || ''}`.trim() || 'Guest information not available',
      email: billingContact?.contact?.email || '',
      phone: billingContact?.contact?.phone || ''
    },
    allGuests: extractAllGuests(),
    priceDetails: {
      roomRate: baseRate,
      nights: nights,
      taxesAndFees: taxes,
      resortFee: resortFee,
      currency: primaryRate?.currency || 'INR'
    },
    paymentInfo: {
      isPaid,
      cardType: 'Visa', // This might need to be extracted from credit_card_info if available
      cardLastDigits: '****' // This might need to be extracted from credit_card_info if available
    },
    cancellationDeadline,
    hotelImage: hotel?.heroImage || undefined,
    amenities: rooms[0]?.facilities?.map(f => f.name) || []
  };
};

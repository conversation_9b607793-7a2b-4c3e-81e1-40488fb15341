import { AccommodationTypeItem, AmenityItem, HotelFilterData, StarRatingItem, UserRatingItem } from "@/models/hotel/filter.model";
import { Hotel } from "@/models/hotel/list-page.model";

// --- Helper: Amenity to Icon Mapper ---
const amenityIconMap: { [key: string]: string } = {
  'Free WiFi': 'wifi',
  'Swimming Pool': 'pool',
  'Fitness Center': 'fitness',
  'Spa': 'spa',
  'Restaurant': 'restaurant',
  'Business Center': 'business',
  'Parking': 'parking',
  'Pets Allowed': 'pets',
};

/**
 * Creates the initial, static structure for the filters with counts initialized to zero.
 */
const createDefaultFilterState = (): HotelFilterData => ({
    priceRange: {
        min: Infinity,
        max: -Infinity,
        values: { minimum: 0, maximum: 0 }
    },
    popularFilters: {
        freeCancellation: { key: 'Free Cancellation', isSelected: false, count: 0 },
        breakfastIncluded: { key: 'Breakfast Included', isSelected: false, count: 0 },
        payAtHotel: { key: 'Free WiFi', isSelected: false, count: 0 },
        petsAllowed: { key: 'Pets Allowed', isSelected: false, count: 0 },
    },
    userRating: [
        { key: 'Excellent', isSelected: false, count: 0 },
        { key: 'Very Good', isSelected: false, count: 0 },
        { key: 'Good', isSelected: false, count: 0 },
    ],
    hotelStarRating: [
        { key: 5, isSelected: false, count: 0 },
        { key: 4, isSelected: false, count: 0 },
        { key: 3, isSelected: false, count: 0 },
    ],
    accommodationTypes: [],
    amenities: [],
    priceDistribution: [],
    isPriceRangeChanged: false,
    popularFiltersAnyActive: false,
    userRatingAnyActive: false,
    accommodationTypesAnyActive: false,
    amenitiesAnyActive: false,
    hotelStarRatingAnyActive: false,
});


/**
 * Populates and updates filter data based on a list of hotels.
 */
export function populateHotelFilterData( hotels: Hotel[], existingFilters?: HotelFilterData): HotelFilterData {
   const newFilterData: HotelFilterData = existingFilters
    ? (JSON.parse(JSON.stringify(existingFilters)) as HotelFilterData)
    : createDefaultFilterState();

   const accommodationTypeMap = new Map(newFilterData.accommodationTypes.map((item: AccommodationTypeItem) => [item.key, item]));
   const amenityMap = new Map();

   for (const hotel of hotels) {
     if (hotel.fareDetail.totalPrice < newFilterData.priceRange.min) {
       newFilterData.priceRange.min = Math.floor(hotel.fareDetail.totalPrice);
     }
     if (hotel.fareDetail.totalPrice > newFilterData.priceRange.max) {
       newFilterData.priceRange.max = Math.ceil(hotel.fareDetail.totalPrice);
     }

     // Create a set of special amenities for efficient lookup
    const specialAmenitiesSet = new Set(hotel.specialAmenities);

    // Check for popular filters directly from the special amenities list
    if (specialAmenitiesSet.has('Free Cancellation')) {
     newFilterData.popularFilters.freeCancellation.count++;
    }
    if (specialAmenitiesSet.has('Free Breakfast')) {
     newFilterData.popularFilters.breakfastIncluded.count++;
  }
  if (specialAmenitiesSet.has('Pay At Hotel')) {
    newFilterData.popularFilters.payAtHotel.count++;
  }
  if (specialAmenitiesSet.has('Pets Allowed')) {
   newFilterData.popularFilters.petsAllowed.count++;
  }

  // Check for other amenities, excluding those that are special
  hotel.amenities.forEach(amenity => {
    if (!specialAmenitiesSet.has(amenity.name)) {
    const amenityEntry = amenityMap.get(amenity.name);
      if (amenityEntry) {
      amenityEntry.count += 1;
      } else {
      amenityMap.set(amenity.name, {
      key: amenity.name,
      count: 1,
      isSelected: false,
      icon: amenityIconMap[amenity.name] || 'default_icon',
      });
      }
    }
  });

  const starRatingItem = newFilterData.hotelStarRating.find((s: { key: number }) => s.key === hotel.starRating);
    if (starRatingItem) starRatingItem.count++;

  const userRatingItem = newFilterData.userRating.find((u: { key: string }) => u.key === hotel.userRatingCategory);
    if (userRatingItem) userRatingItem.count++;

  const accType = hotel.hotelType;
  const accEntry = accommodationTypeMap.get(accType);
    if (accEntry) {
    accEntry.count += 1;
    } else {
    accommodationTypeMap.set(accType, { key: accType, count: 1, isSelected: false });
    }
  }
 
    const { min, max } = newFilterData.priceRange;
    if (max > min) {
      const bucketCount = 20;
      const bucketSize = (max - min) / bucketCount;
      const distribution = Array(bucketCount).fill(0);

      for (const hotel of hotels) {
      let bucketIndex = Math.floor((hotel.fareDetail.totalPrice - min) / bucketSize);
      if (bucketIndex >= bucketCount) bucketIndex = bucketCount - 1;
      distribution[bucketIndex]++;
      }
      newFilterData.priceDistribution = distribution;
    }

    newFilterData.accommodationTypes = Array.from(accommodationTypeMap.values()).sort((a: AccommodationTypeItem, b: AccommodationTypeItem) => b.count - a.count);
    newFilterData.amenities = Array.from(amenityMap.values()).sort((a: AmenityItem, b: AmenityItem) => b.count - a.count);

    if (!existingFilters) {
      newFilterData.priceRange.values.minimum = newFilterData.priceRange.min;
      newFilterData.priceRange.values.maximum = newFilterData.priceRange.max;
    }
    return newFilterData;
}


/**
 * A generic and type-safe handler to update the state of any filter.
 */
export function handleFilterChange(
    key: keyof HotelFilterData,
    subkey: string | null,
    value: string | number | { minimum: number; maximum: number; },
    currentFilters: HotelFilterData
): HotelFilterData {
    const updatedFilters = JSON.parse(JSON.stringify(currentFilters)) as HotelFilterData;

    // A helper function to check if any item in a list is selected
    const hasAnyActive = (items: (AmenityItem | AccommodationTypeItem | StarRatingItem | UserRatingItem)[]): boolean => {
        return items.some(item => item.isSelected);
    };

    if (key === 'popularFilters' && subkey) {
        const filterKey = subkey as keyof HotelFilterData['popularFilters'];
        if (updatedFilters.popularFilters[filterKey]) {
            updatedFilters.popularFilters[filterKey].isSelected = !updatedFilters.popularFilters[filterKey].isSelected;
        }
        updatedFilters.popularFiltersAnyActive = Object.values(updatedFilters.popularFilters).some(f => f.isSelected);

    } else if (key === 'userRating') {
        const itemIndex = updatedFilters.userRating.findIndex((i) => i.key === value);
        if (itemIndex > -1) {
            updatedFilters.userRating[itemIndex].isSelected = !updatedFilters.userRating[itemIndex].isSelected;
        }
        updatedFilters.userRatingAnyActive = hasAnyActive(updatedFilters.userRating);

    } else if (key === 'hotelStarRating') {
        const itemIndex = updatedFilters.hotelStarRating.findIndex((i) => i.key === value);
        if (itemIndex > -1) {
            updatedFilters.hotelStarRating[itemIndex].isSelected = !updatedFilters.hotelStarRating[itemIndex].isSelected;
        }
        updatedFilters.hotelStarRatingAnyActive = hasAnyActive(updatedFilters.hotelStarRating);

    } else if (key === 'amenities') {
        const itemIndex = updatedFilters.amenities.findIndex((i) => i.key === value);
        if (itemIndex > -1) {
            updatedFilters.amenities[itemIndex].isSelected = !updatedFilters.amenities[itemIndex].isSelected;
        }
        updatedFilters.amenitiesAnyActive = hasAnyActive(updatedFilters.amenities);

    } else if (key === 'accommodationTypes') {
        const itemIndex = updatedFilters.accommodationTypes.findIndex((i) => i.key === value);
        if (itemIndex > -1) {
            updatedFilters.accommodationTypes[itemIndex].isSelected = !updatedFilters.accommodationTypes[itemIndex].isSelected;
        }
        updatedFilters.accommodationTypesAnyActive = hasAnyActive(updatedFilters.accommodationTypes);

    } else if (key === 'priceRange') {
        // Correctly handle the price range filter
        if (typeof value === 'object' && 'minimum' in value && 'maximum' in value) {
            updatedFilters.priceRange.values.minimum = value.minimum;
            updatedFilters.priceRange.values.maximum = value.maximum;
            updatedFilters.isPriceRangeChanged = (updatedFilters.priceRange.values.minimum > updatedFilters.priceRange.min) || (updatedFilters.priceRange.values.maximum < updatedFilters.priceRange.max);
        } else {
            console.error("Invalid value provided for priceRange filter.");
        }
    }

    return updatedFilters;
}

// ==================================================================
// UPDATED HELPER FUNCTION: Apply Filters to Hotel List
// ==================================================================
export function applyFiltersHelper(
  allHotels: Hotel[],
  filters: HotelFilterData
): Hotel[] {
  // Correctly accessing top-level properties
  const selectedStarRatings = filters.hotelStarRating.filter(s => s.isSelected).map(s => s.key);
  const selectedUserRatings = filters.userRating.filter(u => u.isSelected).map(u => u.key);
  const selectedAccTypes = filters.accommodationTypes.filter(a => a.isSelected).map(a => a.key);
  const selectedAmenities = filters.amenities.filter(a => a.isSelected).map(a => a.key);

  return allHotels.filter(hotel => {

    // Check against price range filter only if it has changed
    if (filters.isPriceRangeChanged && (hotel.fareDetail.totalPrice < filters.priceRange.values.minimum || hotel.fareDetail.totalPrice > filters.priceRange.values.maximum)) {
      return false;
    }

    // Check against popular filters only if any of them are active
    const specialAmenitiesSet = new Set(hotel.specialAmenities);
    if (filters.popularFiltersAnyActive) {
      if (filters.popularFilters.freeCancellation.isSelected && !specialAmenitiesSet.has('Free Cancellation')) {
        return false;
      }
      if (filters.popularFilters.breakfastIncluded.isSelected && !specialAmenitiesSet.has('Free Breakfast')) {
        return false;
      }
      if (filters.popularFilters.payAtHotel.isSelected && !specialAmenitiesSet.has('Pay At Hotel')) {
        return false;
      }
      if (filters.popularFilters.petsAllowed.isSelected && !specialAmenitiesSet.has('Pets Allowed')) {
        return false;
      }
    }

    // Check against star and user ratings only if any of them are active
    if (filters.hotelStarRatingAnyActive && !selectedStarRatings.includes(hotel.starRating)) {
      return false;
    }
    if (filters.userRatingAnyActive && !selectedUserRatings.includes(hotel.userRatingCategory)) {
      return false;
    }

    // Check against accommodation types only if any are active
    if (filters.accommodationTypesAnyActive && !selectedAccTypes.includes(hotel.hotelType)) {
      return false;
    }

    // Check against amenities only if any are active
    if (filters.amenitiesAnyActive) {
      const hotelAmenityNames = new Set(hotel.amenities.map(a => a.name));
      const hasAllAmenities = selectedAmenities.every(selected => hotelAmenityNames.has(selected));
      if (!hasAllAmenities) {
        return false;
      }
    }

    return true;
  });
}


// Reset filter

export const resetActiveFilters = (data: HotelFilterData): HotelFilterData => {
  const newFilterData = JSON.parse(JSON.stringify(data));

  if (newFilterData.isPriceRangeChanged) {
    newFilterData.priceRange.values.minimum = newFilterData.priceRange.min;
    newFilterData.priceRange.values.maximum = newFilterData.priceRange.max;
    newFilterData.isPriceRangeChanged = false;
  }

  if (newFilterData.popularFiltersAnyActive) {
    for (const key in newFilterData.popularFilters) {
      if (newFilterData.popularFilters.hasOwnProperty(key)) {
        newFilterData.popularFilters[key].isSelected = false;
      }
    }
    newFilterData.popularFiltersAnyActive = false;
  }

  if (newFilterData.userRatingAnyActive) {
    newFilterData.userRating.forEach((item: UserRatingItem) => {
      item.isSelected = false;
    });
    newFilterData.userRatingAnyActive = false;
  }

  if (newFilterData.accommodationTypesAnyActive) {
    newFilterData.accommodationTypes.forEach((item: AccommodationTypeItem) => {
      item.isSelected = false;
    });
    newFilterData.accommodationTypesAnyActive = false;
  }

  if (newFilterData.amenitiesAnyActive) {
    newFilterData.amenities.forEach((item: AmenityItem) => {
      item.isSelected = false;
    });
    newFilterData.amenitiesAnyActive = false;
  }
  
  if (newFilterData.hotelStarRatingAnyActive) {
    newFilterData.hotelStarRating.forEach((item: StarRatingItem) => {
      item.isSelected = false;
    });
    newFilterData.hotelStarRatingAnyActive = false;
  }

  return newFilterData;
};
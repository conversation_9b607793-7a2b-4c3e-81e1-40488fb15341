'use client';
import React, { useCallback, useEffect, useMemo, useRef, useState } from 'react'
import './hotel-detail.scss'
import { useCommonContext } from '@/app/contexts/commonContext';
import { HotelSearchData, HotelSearchFormData, Room } from '@/models/hotel/search-page.model';
import { useParams, useSearchParams } from 'next/navigation';
import { getHotelDetails, getRoomsAndRates } from '@/api/hotel/hotel-detail-service';
import MobileInnerPageHeader from '@/app/components/mobile-inner-page-header/MobileInnerPageHeader';
import { HotelDetailApiResponse } from '@/models/hotel/detail-page.model';
import DateRangeDisplay from './components/date-range/DateRangeDisplay';
import { useTranslation } from '@/app/hooks/useTranslation';
import HotelInfoHeader from './components/hotel-info-header/HotelInfoHeader';
import { extractHotelSearchFormData } from '@/helpers/hotel/common-helper';

function Page() {
    const { t } = useTranslation();
    const roomsTabName = t("hotel.detail.navigation.rooms");
    const overviewTabName = t("hotel.detail.navigation.overview");
    const locationTabName = t("hotel.detail.navigation.location");
    const reviewsTabName = t("hotel.detail.navigation.reviews");
    const facilitiesTabName = t("hotel.detail.navigation.facilities");
    const policiesTabName = t("hotel.detail.navigation.policies");

    const [isMobile, setIsMobile] = useState<boolean>(false);
    const [isShimmerLoading,setIsShimmerLoading] = useState<boolean>(false);
    const [isRoomsAndRatesLoading, setIsRoomsAndRatesLoading] = useState<boolean>(false);
    const [activeTab, setActiveTab] = useState<string>(t("hotel.detail.navigation.overview"));
    const { hotelSearchData , hotelSearchFormData , setHotelSearchFormData , setHotelSearchData , setHotelDetailsResponse , hotelDetailsResponse } = useCommonContext();

    const navigationTabs = useMemo(() => [
            overviewTabName,
            roomsTabName,
            locationTabName,
            reviewsTabName,
            facilitiesTabName,
            policiesTabName,
    ],[overviewTabName,roomsTabName,locationTabName,reviewsTabName,facilitiesTabName,policiesTabName]);

    const filteredTabs = useMemo(() => {
      if (isMobile) {
        return [
          overviewTabName,
          facilitiesTabName,
          locationTabName,
          reviewsTabName,
          policiesTabName,
        ];
      }

      // Default order for larger screens
      return navigationTabs;
    }, [isMobile,navigationTabs,overviewTabName,facilitiesTabName,locationTabName,reviewsTabName,policiesTabName]);

    const headerRef = useRef<HTMLDivElement>(null);
    const overviewRef = useRef<HTMLDivElement>(null);
    const roomsRef = useRef<HTMLDivElement>(null);
    const locationRef = useRef<HTMLDivElement>(null);
    const reviewsRef = useRef<HTMLDivElement>(null);
    const facilitiesRef = useRef<HTMLDivElement>(null);
    const policiesRef = useRef<HTMLDivElement>(null);
    const params = useParams();

const sectionRefs = useMemo(() => ({
    [overviewTabName]: overviewRef,
    [roomsTabName]: roomsRef,
    [locationTabName]: locationRef,
    [reviewsTabName]: reviewsRef,
    [facilitiesTabName]: facilitiesRef,
    [policiesTabName]: policiesRef,
  }),[overviewTabName,roomsTabName,locationTabName,reviewsTabName,facilitiesTabName,policiesTabName,]);


    const getRoomsAndRatesApi = useCallback(async (hotelId:string , searchKey:string) => {
        const body = {
            search_key: searchKey,
            hotel_id: hotelId,
        }
        setIsRoomsAndRatesLoading(true);
        try{
            const api = await getRoomsAndRates(body);
            if(api && api.data.length > 0){

            }
        }catch (error){
            console.error("Error calling searchApi:", error);
        }finally{
            setIsRoomsAndRatesLoading(false);
        }
    },[])

    const getHotelDetailApi = useCallback(async (id:string , searchKey:string) => {
        setIsShimmerLoading(true);
        try{
            const api = await getHotelDetails(id);
            if(api && api.data.hotelId){
                setHotelDetailsResponse(api.data);
                getRoomsAndRatesApi(id , searchKey);
            }
        }catch (error){
            console.error("Error calling searchApi:", error);
        }finally{
            setIsShimmerLoading(false);
        }
    },[])

    const handleScrollToSection = useCallback((tabName: string) => {
        const ref = sectionRefs[tabName];
        if (ref?.current) {
            const top = ref.current.getBoundingClientRect().top + window.scrollY - 120; 
            window.scrollTo({ top, behavior: "smooth" });
        }
    },[sectionRefs]);

    const handleTableClick = useCallback((tab: string) => {
        setActiveTab(tab);
        handleScrollToSection(tab);
    },[handleScrollToSection])

    const handleGoBack = () => {
        window.history.back();
    }


    useEffect(() => {
        if (!params?.hotelId) return;

        const hotelId = parseInt(Array.isArray(params.hotelId) ? params.hotelId[0] : params.hotelId, 10);

        const reconstructedFormData = extractHotelSearchFormData(params);
        setHotelSearchFormData(reconstructedFormData);

        const searchKey = localStorage.getItem("searchKey");
        if (hotelId && searchKey) {
            getHotelDetailApi(hotelId.toString(), searchKey);
        }
    }, [params, setHotelSearchFormData, getHotelDetailApi]);

  return (
    <div className="hotel-detail-container">
        <div className="overview-section common-container">
            <div className="route-path">
                <span>Home</span> <i className="fa-solid fa-greater-than"></i>
                <span>Hotels</span> <i className="fa-solid fa-greater-than"></i>
                <span>{hotelSearchFormData?.searchQuery || "..."}</span>{" "}
                <i className="fa-solid fa-greater-than"></i> Detail
            </div>
        </div>
        {isMobile && (
            <MobileInnerPageHeader onBackClick={handleGoBack}>
                <div className="mobile-header-info">
                    <div className="hotel-name-with-rating">
                    <h3 className="hotel-name">{hotelDetailsResponse?.name || "..."}</h3>
                    <div className="rating-stars">
                        {Array.from({
                        length: hotelDetailsResponse?.starRating ?? 0,
                        }).map((_, index) => (
                        <i key={index} className="fa-solid fa-star"></i>
                        ))}
                    </div>
                    </div>
                    <div className="mobile-date-range">
                        <DateRangeDisplay
                            checkInDate={hotelSearchFormData?.checkInDate ?? ""}
                            checkOutDate={hotelSearchFormData?.checkOutDate ?? ""}
                            compact={true}
                        />
                    </div>
              </div>
            </MobileInnerPageHeader>
        )}

        <div className={`navigation-tabs-container`}>
          <div className="navigation-tabs common-container ">
            {filteredTabs.map((label, index) => (
              <div
                className={`navigation-tab ${activeTab === label ? "active" : ""}`}
                key={index}
                onClick={() => handleTableClick(label)}
              >
                {label}
              </div>
            ))}
          </div>
        </div>
        
        <div className="common-container">
            {isMobile ? (
                <div ref={overviewRef} className="mobile-header-spacer"></div>
            ):(
                <div ref={overviewRef}>
                    <HotelInfoHeader 
                        handleSave={() => {}}
                        shareHotel={() => {}}
                        handleReserveClick={() => {}}
                    />
                </div>
            )}
            
        </div>

    </div>
  )
}

export default Page
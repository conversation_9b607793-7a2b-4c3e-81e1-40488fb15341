@use "sass:color";
@use "/styles/variable" as *;
@use "/styles/zIndex" as * ;

$accent: #F9A825;
$background-light: #F8F9FC;
$background-input: #FFFFFF;
$text-dark: #2C3E50;
$text-medium: #5D6D7E;
$text-light: #8395A7;
$border-color: rgba($text-medium, 0.15);
$shadow-color: rgba(44, 62, 80, 0.1);
$range-background: rgba($primary-color, 0.15);
$range-border: rgba($primary-color, 0.3);
$today-color: #E74C3C;
$past-date-color: #DFE4E8;
$past-date-text: #B2BABB;

// Standard spacing - slightly reduced
$spacing-xs: 3px;
$spacing-sm: 6px;
$spacing-md: 10px;
$spacing-lg: 14px;
$spacing-xl: 18px;
$border-radius-sm: 5px;
$border-radius-md: 8px;
$border-radius-lg: 10px;

// Added a wrapper to control width
.calendarWrapper {
  width: 100%;
  height: 100%; // Full height
  display: flex; // Changed to flex
  flex-direction: column;
  position: relative;
  z-index: z-index(modal);
  overflow: hidden; // Prevent wrapper from creating scroll
  margin: 0; // Ensure no margin
  padding: 0; // Ensure no padding

  // Ensure calendar fills dropdown completely
  .calendarContainer {
    border-radius: 8px; // Match parent dropdown
    border: none;
    box-shadow: none;
    margin: 0;
  }
}

.calendarContainer {
  width: 100%; // Full width of parent
  background: white;
  border-radius: 8px; // Match dropdown border-radius
  box-shadow: none; // Remove shadow to eliminate visual gaps
  overflow: hidden; // Prevent container from creating scroll
  border: none; // Remove border to eliminate white space
  z-index: z-index(base);
  margin: 0; // Remove margin to eliminate white space
  display: flex;
  flex-direction: column;
  height: 100%;
  min-height: 520px; // Minimum height for sticky to work
  max-height: 85vh; // Limit height for mobile
  position: relative; // Important for sticky children

  // Improved styling for better appearance
  .twoMonthsLayout {
    @media (min-width: 769px) {
      min-width: 600px; // Ensure minimum width for desktop
    }
  }

  @media (max-width: 768px) {
    .twoMonthsLayout {
      flex-direction: column !important;
    }
  }

  .calendarContent {
    padding: 0; // Remove padding to eliminate white space
  }

  .calendarHeader {
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: linear-gradient(135deg, var(--primary-color) 0%, #1e40af 100%);
    color: white;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    margin: 0;
    padding: 0;

    h2 {
      font-size: 17px;
      font-weight: 600;
      margin: 0;
      display: flex;
      align-items: center;
      flex-wrap: wrap;
      line-height: 1.4;
      letter-spacing: -0.01em;
      gap: 6px;

      &::before {
        content: "\f073";
        font-family: "Font Awesome 6 Free";
        font-weight: 900;
        margin-right: 6px;
        background: rgba(255, 255, 255, 0.15);
        width: 32px;
        height: 32px;
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 8px;
        flex-shrink: 0;
        font-size: 14px;
        backdrop-filter: blur(10px);
        border: 1px solid rgba(255, 255, 255, 0.1);
      }

      // Faded labels (Check-in, Check-out)
      .dateLabel {
        opacity: 0.7;
        font-weight: 400;
        font-size: 14px;
        margin-right: 4px;
      }

      // Prominent selected dates
      .selectedDate {
        font-weight: 700;
        font-size: 16px;
        color: white;
        text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
      }

      // Subtle placeholder text
      .placeholderDate {
        opacity: 0.5;
        font-weight: 400;
        font-size: 14px;
        font-style: italic;
      }

      // Separator styling
      .dateSeparator {
        opacity: 0.6;
        margin: 0 8px;
        font-weight: 300;
      }

      @media (max-width: 768px) {
        font-size: 15px;
        line-height: 1.3;
        gap: 4px;

        &::before {
          width: 28px;
          height: 28px;
          margin-right: 4px;
          font-size: 12px;
        }

        .dateLabel {
          font-size: 13px;
          margin-right: 3px;
        }

        .selectedDate {
          font-size: 14px;
        }

        .placeholderDate {
          font-size: 13px;
        }

        .dateSeparator {
          margin: 0 6px;
        }
      }

      @media (max-width: 480px) {
        font-size: 14px;
        gap: 3px;

        &::before {
          width: 26px;
          height: 26px;
          margin-right: 3px;
        }

        .dateLabel {
          font-size: 12px;
          margin-right: 2px;
        }

        .selectedDate {
          font-size: 13px;
        }

        .placeholderDate {
          font-size: 12px;
        }

        .dateSeparator {
          margin: 0 4px;
        }
      }
    }

    // Nights count in header
    .nightsCount {
      margin-left: 12px;
      font-weight: 500;
      opacity: 0.95;
      font-size: 14px;
      background: rgba(255, 255, 255, 0.1);
      padding: 4px 8px;
      border-radius: 12px;
      border: 1px solid rgba(255, 255, 255, 0.15);
      backdrop-filter: blur(10px);
      white-space: nowrap;

      @media (max-width: 768px) {
        font-size: 12px;
        margin-left: 8px;
        padding: 3px 6px;
        border-radius: 10px;
      }

      @media (max-width: 480px) {
        font-size: 11px;
        margin-left: 6px;
        padding: 2px 5px;
        border-radius: 8px;
      }
    }
  }

  .selectedDaysSummary {
    margin: $spacing-sm 0 $spacing-md;
    padding: $spacing-md;
    background-color: rgba($primary-color, 0.08);
    border-radius: $border-radius-md;
    border-left: 3px solid $primary-color;

    .summaryContent {
      display: flex;
      align-items: center;
      gap: $spacing-sm;
      flex-wrap: wrap;

      .summaryLabel {
        font-size: 12px;
        font-weight: 600;
        color: $text-medium;
      }

      .summaryDates {
        font-size: 12px;
        font-weight: 600;
        color: $primary-color;
      }

      .summaryNights {
        font-size: 12px;
        color: $text-medium;
        background: rgba($primary-color, 0.1);
        padding: 2px 6px;
        border-radius: 12px;
      }
    }
  }

  .monthNavigation {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 4px;
    padding: 0 8px;

    .navButton {
      width: 28px;
      height: 28px;
      display: flex;
      align-items: center;
      justify-content: center;
      border-radius: 8px;
      border: 1px solid rgba(0, 0, 0, 0.08);
      background: white;
      color: #374151;
      cursor: pointer;
      transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
      box-shadow: 0 1px 2px rgba(0, 0, 0, 0.06);

      &:hover:not(.disabled) {
        background: var(--primary-color);
        color: white;
        border-color: var(--primary-color);
        transform: translateY(-1px);
        box-shadow: 0 4px 12px rgba(0, 59, 149, 0.15);
      }

      &:active:not(.disabled) {
        transform: translateY(0);
        box-shadow: 0 2px 4px rgba(0, 59, 149, 0.1);
      }

      i {
        font-size: 11px;
        font-weight: 600;
      }

      &.disabled {
        opacity: 0.4;
        cursor: not-allowed;
        background: #f9fafb;
        color: #9ca3af;
        border-color: #e5e7eb;

        &:hover {
          transform: none;
          box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1), 0 1px 2px rgba(0, 0, 0, 0.06);
        }
      }
    }

    .monthLabels {
      display: flex;
      flex: 1;
      justify-content: center;

      span {
        font-size: 13px;
        font-weight: 600;
        color: #1f2937;
        padding: 4px 10px;
        border-radius: 8px;
        background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
        border: 1px solid rgba(0, 0, 0, 0.05);
        box-shadow: 0 1px 2px rgba(0, 0, 0, 0.06);
        letter-spacing: -0.01em;
      }
    }
  }

  .calendarsWrapper {
    background-color: $background-light;
    border-radius: $border-radius-md;
    padding: $spacing-md; // Add proper padding for content spacing
    margin: $spacing-md; // Add margin for spacing from edges

    .twoMonthsLayout {
      display: flex;
      gap: $spacing-md;

      @media (max-width: 768px) {
        flex-direction: column;
      }
    }

    .calendarMonth {
      width: 100%;
      padding: $spacing-sm;
      background-color: $background-input;
      border-radius: $border-radius-sm;

      .monthLabel {
        text-align: center;
        padding: $spacing-xs;
        font-size: 12px;
        font-weight: 600;
        color: $primary-color;
        background-color: rgba($primary-color, 0.05);
        border-radius: $border-radius-sm;
        margin-bottom: $spacing-xs;
      }

      .dayNames {
        display: grid;
        grid-template-columns: repeat(7, 1fr);
        gap: 1px;
        margin-bottom: $spacing-xs;

        .dayName {
          height: 24px;
          display: flex;
          align-items: center;
          justify-content: center;
          font-size: 10px;
          font-weight: 600;
          color: $text-medium;
        }
      }

      .days {
        display: grid;
        grid-template-columns: repeat(7, 1fr);
        gap: 1px;

        .dayCell {
          height: 36px; // Reduced size
          position: relative;

          &.empty {
            height: 36px;
          }

          .day {
            cursor: pointer;
            height: 100%;
            width: 100%;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            position: relative;

            &.inRange {
              background: linear-gradient(135deg, rgba(0, 59, 149, 0.08) 0%, rgba(30, 64, 175, 0.12) 100%);
              border-radius: 0;
              position: relative;

              &::before {
                content: '';
                position: absolute;
                top: 0;
                left: 0;
                right: 0;
                bottom: 0;
                background: linear-gradient(90deg, transparent 0%, rgba(0, 59, 149, 0.05) 50%, transparent 100%);
                pointer-events: none;
              }

              .dayNumber {
                color: var(--primary-color);
                font-weight: 600;
              }
            }

            &.start, &.end {
              .dayNumber {
                background: linear-gradient(135deg, var(--primary-color) 0%, #1e40af 100%);
                color: white;
                box-shadow: 0 4px 12px rgba(0, 59, 149, 0.25);
                transform: translateY(-2px) scale(1.05);
                border: 2px solid rgba(255, 255, 255, 0.2);
                font-weight: 700;
              }
            }

            &.past {
              cursor: pointer; // Make past dates clickable
              background-color: rgba($past-date-color, 0.3);
              opacity: 0.7;
              position: relative;

              .dayNumber {
                color: $past-date-text;
                border: 1px dashed rgba($past-date-text, 0.5);
              }

              // Simple hover effect without tooltip
              &:hover {
                background-color: rgba($primary-color, 0.1);
                opacity: 0.9;
                transform: scale(1.05);

                .dayNumber {
                  color: $primary-color;
                  border-color: $primary-color;
                }
              }
            }

            &.today {
              .dayNumber {
                background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
                color: white;
                font-weight: 700;
                box-shadow: 0 3px 8px rgba(245, 158, 11, 0.3);
                border: 2px solid rgba(255, 255, 255, 0.3);
                position: relative;

                &::after {
                  content: '';
                  position: absolute;
                  top: -2px;
                  left: -2px;
                  right: -2px;
                  bottom: -2px;
                  border-radius: 14px;
                  border: 1px solid rgba(245, 158, 11, 0.2);
                  pointer-events: none;
                }
              }

              &.start .dayNumber {
                background: linear-gradient(135deg, var(--primary-color) 0%, #1e40af 100%);

                &::after {
                  border-color: rgba(0, 59, 149, 0.2);
                }
              }
            }

            .dayNumber {
              display: flex;
              align-items: center;
              justify-content: center;
              width: 32px;
              height: 32px;
              border-radius: 12px;
              font-size: 13px;
              font-weight: 600;
              transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
              color: #374151;
              background: transparent;

              &:hover:not(.day.past .dayNumber) {
                background: rgba(var(--primary-color-rgb), 0.1);
                color: var(--primary-color);
                transform: translateY(-1px);
                box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
              }
            }


          }
        }
      }
    }
  }

  .calendarFooter {
    margin: $spacing-md; // Add margin for proper spacing
    padding: $spacing-sm $spacing-md; // Add horizontal padding
    border-top: 1px solid $border-color;
    display: flex;
    justify-content: space-between;
    align-items: center;

    .legend {
      display: flex;
      gap: 10px;
      flex-wrap: wrap;

      .legendItem {
        display: flex;
        align-items: center;
        font-size: 10px;
        color: $text-medium;

        .legendColor {
          width: 10px;
          height: 10px;
          margin-right: $spacing-xs;

          &.circle {
            background-color: $primary-color;
            border-radius: 50%;

            &.today {
              background-color: $today-color;
            }

            &.past {
              background-color: $past-date-color;
              border: 1px dashed rgba($past-date-text, 0.5);
            }
          }

          &.inRange {
            background-color: $range-background;
            border-radius: 3px;
          }
        }
      }
    }

    .applyButton {
      white-space: nowrap;
      padding: 8px 16px;
      border: none;
      border-radius: 6px;
      font-size: 13px;
      font-weight: 600;
      color: white;
      background-color: $primary-color;
      cursor: pointer;
      transition: all 0.2s ease;
      box-shadow: 0 2px 6px rgba($primary-color, 0.25);
      display: flex;
      align-items: center;
      gap: 5px;

      &::after {
        content: "\f00c";
        font-family: "Font Awesome 6 Free";
        font-weight: 900;
        font-size: 12px;
      }

      &:hover {
        background-color: darken($primary-color, 5%);
        box-shadow: 0 3px 8px rgba($primary-color, 0.4);
      }

      &:active {
        transform: translateY(1px);
        box-shadow: 0 1px 3px rgba($primary-color, 0.25);
      }

      &:disabled {
        background-color: lighten($primary-color, 20%);
        cursor: not-allowed;
        box-shadow: none;
        opacity: 0.7;
      }
    }
  }
  // Fixed header styles
  .fixed-header {
    position: -webkit-sticky; // Safari support
    position: sticky;
    top: 0;
    left: 0;
    right: 0;
    z-index: 100;
    background: linear-gradient(135deg, var(--primary-color) 0%, #1e40af 100%);
    color: white;
    border-bottom: none;
    flex-shrink: 0;
    padding: 18px 20px; // Proper padding for content
    width: 100%;
    box-sizing: border-box;
    margin: 0; // Remove any margin
    border-radius: 8px 8px 0 0; // Match dropdown border radius

    h2 {
      color: white;
      margin: 0;
      font-size: 16px;
      font-weight: 600;
    }


  }

  // Fixed summary styles
  .fixed-summary {
    position: -webkit-sticky; // Safari support
    position: sticky;
    top: 0;
    left: 0;
    right: 0;
    z-index: 99;
    background-color: $background-light;
    border-bottom: 1px solid $border-color;
    flex-shrink: 0;
    padding: 16px 20px; // Increased padding
    width: 100%;
    box-sizing: border-box;

    // Override existing selectedDaysSummary styles when used as fixed
    margin: 0; // Remove margin for fixed positioning
    border-radius: 0; // Remove border radius for fixed positioning
    border-left: none; // Remove left border for fixed positioning
  }

  // Fixed navigation styles
  .fixed-navigation {
    position: -webkit-sticky; // Safari support
    position: sticky;
    top: 0;
    left: 0;
    right: 0;
    z-index: 98;
    background-color: $background-input;
    border-bottom: 1px solid $border-color;
    flex-shrink: 0;
    padding: 16px 20px; // Increased padding
    width: 100%;
    box-sizing: border-box;
  }

  // Scrollable content area
  .scrollable-content {
    flex: 1;
    overflow-y: auto;
    overflow-x: hidden;
    min-height: 0; // Important for flex child to be scrollable
    padding-bottom: 20px; // Add space at bottom to prevent overlap with footer

    // Custom scrollbar for better UX
    &::-webkit-scrollbar {
      width: 6px;
    }

    &::-webkit-scrollbar-track {
      background: #f1f1f1;
      border-radius: 3px;
    }

    &::-webkit-scrollbar-thumb {
      background: #c1c1c1;
      border-radius: 3px;

      &:hover {
        background: #a8a8a8;
      }
    }
  }

  // Apply button container inside scrollable content
  .apply-button-container {
    padding: 20px;
    margin-top: 20px;
    border-top: 1px solid $border-color;
    background-color: $background-light;

    .mobile-apply-button {
      width: 100%;
      height: 52px;
      font-size: 16px;
      font-weight: 600;
      background: linear-gradient(135deg, var(--primary-color) 0%, #1e40af 100%);
      color: white;
      border: none;
      border-radius: 16px;
      cursor: pointer;
      transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
      box-shadow: 0 4px 12px rgba(0, 59, 149, 0.25);
      position: relative;
      overflow: hidden;

      &::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
        transition: left 0.5s;
      }

      &:hover:not(:disabled) {
        transform: translateY(-2px);
        box-shadow: 0 6px 20px rgba(0, 59, 149, 0.3);

        &::before {
          left: 100%;
        }
      }

      &:active:not(:disabled) {
        transform: translateY(-1px);
        box-shadow: 0 4px 12px rgba(0, 59, 149, 0.25);
      }

      &:disabled {
        background: linear-gradient(135deg, #e5e7eb 0%, #d1d5db 100%);
        color: #9ca3af;
        cursor: not-allowed;
        transform: none;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);

        &::before {
          display: none;
        }
      }
    }
  }

  // Desktop-only footer
  .desktop-only {
    display: block;

    @media (max-width: 768px) {
      display: none;
    }
  }

  // Mobile-only elements (hidden on desktop) - now handled by JavaScript conditional rendering

  // Fixed footer styles
  .fixed-footer {
    position: -webkit-sticky; // Safari support
    position: sticky;
    bottom: 0;
    left: 0;
    right: 0;
    z-index: 100;
    background-color: $background-input;
    border-top: 1px solid $border-color;
    flex-shrink: 0;
    padding: 16px 20px; // Added padding
    width: 100%;
    box-sizing: border-box;
    margin-top: auto; // Push to bottom

    // Ensure footer is always visible
    min-height: 80px; // Minimum height to ensure visibility
    display: flex;
    flex-direction: column;
    justify-content: center;

    .applyButton {
      margin-top: 8px; // Space above button
      min-height: 44px; // Touch-friendly height
    }
  }

  @media screen and (max-width: 768px) {
    margin-top: 0px;
    border-radius: 0px;
    max-height: 100vh; // Full height on mobile

    // Ensure footer is always visible on mobile
    .fixed-footer {
      position: sticky; // Keep sticky but ensure it works
      bottom: 0;
      left: 0;
      right: 0;
      z-index: 1000; // Higher z-index for mobile
      background-color: $background-input;
      border-top: 2px solid $border-color; // Stronger border for visibility
      box-shadow: 0 -2px 8px rgba(0, 0, 0, 0.1); // Add shadow for better visibility
    }

    .fixed-header {
      position: sticky;
      background-color: var(--primary-color);
      color: white;
    }

    .fixed-summary,
    .fixed-navigation,
    .fixed-footer {
      position: sticky;
      background-color: $background-input;
    }
  }

  // Mobile popup specific styles - apply when inside popup body
  .body-div & {
    border: none;
    box-shadow: none;
    margin-top: 0;
    border-radius: 0;
    width: 100%;
    height: 100%;
    max-height: 100vh;
    min-height: 100vh; // Ensure full height for sticky to work
    overflow: hidden; // Prevent this container from creating scroll

    // Fixed header in mobile popup
    .fixed-header {
      position: -webkit-sticky;
      position: sticky;
      top: 0;
      left: 0;
      right: 0;
      z-index: 100;
      padding: 16px 16px;
      background: linear-gradient(135deg, var(--primary-color) 0%, #1e40af 100%);
      color: white;
      width: 100%;
      box-sizing: border-box;
      margin: 0;
      border-radius: 0; // No border radius on mobile

      h2 {
        font-size: 16px;
        margin: 0;
        color: white;
      }


    }

    // Fixed summary in mobile popup
    .fixed-summary {
      position: -webkit-sticky;
      position: sticky;
      top: 0;
      left: 0;
      right: 0;
      z-index: 99;
      padding: 16px 12px; // Increased padding
      width: 100%;
      box-sizing: border-box;
      background-color: $background-light;
      border-bottom: 1px solid $border-color;
      margin: 0; // Remove margin for fixed positioning
      border-radius: 0; // Remove border radius for fixed positioning
      border-left: none; // Remove left border for fixed positioning
    }

    // Fixed navigation in mobile popup
    .fixed-navigation {
      position: -webkit-sticky;
      position: sticky;
      top: 0;
      z-index: 98;
      padding: 12px;
    }

    // Scrollable content in mobile popup
    .scrollable-content {
      flex: 1;
      overflow-y: auto;
      overflow-x: hidden;
      min-height: 0; // Important for flex child scrolling
      padding: 8px; // Minimal padding
      padding-bottom: 20px; // Normal padding since apply button is inside scroll area

      .twoMonthsLayout {
        min-width: auto;
        width: 100%;
        flex-direction: column;
        gap: 8px; // Reduced gap
      }

      .calendarMonth {
        width: 100%;
        padding: 4px; // Reduced padding

        .dayCell {
          height: 40px !important; // Larger touch targets for mobile popup

          .day {
            font-size: 14px;
            font-weight: 500;
          }
        }

        .monthLabel {
          font-size: 14px;
          padding: 6px 4px; // Reduced padding
          margin-bottom: 6px;
        }

        .dayNames {
          margin-bottom: 4px; // Reduced margin

          .dayName {
            height: 28px;
            font-size: 11px;
          }
        }

        .days {
          gap: 1px; // Minimal gap between days
        }
      }
    }

    // Apply button container in mobile popup
    .apply-button-container {
      padding: 16px 12px;
      margin-top: 16px;
      background-color: white;
      border-top: 1px solid $border-color;

      .mobile-apply-button {
        width: 100%;
        height: 52px;
        font-size: 16px;
        font-weight: 600;
        background: linear-gradient(135deg, var(--primary-color) 0%, #1e40af 100%);
        color: white;
        border: none;
        border-radius: 16px;
        cursor: pointer;
        transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
        box-shadow: 0 4px 12px rgba(0, 59, 149, 0.25);
        position: relative;
        overflow: hidden;

        &::before {
          content: '';
          position: absolute;
          top: 0;
          left: -100%;
          width: 100%;
          height: 100%;
          background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
          transition: left 0.5s;
        }

        &:hover:not(:disabled) {
          transform: translateY(-2px);
          box-shadow: 0 6px 20px rgba(0, 59, 149, 0.3);

          &::before {
            left: 100%;
          }
        }

        &:active:not(:disabled) {
          transform: translateY(-1px);
          box-shadow: 0 4px 12px rgba(0, 59, 149, 0.25);
        }

        &:disabled {
          background: linear-gradient(135deg, #e5e7eb 0%, #d1d5db 100%);
          color: #9ca3af;
          cursor: not-allowed;
          transform: none;
          box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);

          &::before {
            display: none;
          }
        }
      }
    }

    // Hide desktop footer in mobile popup
    .desktop-only {
      display: none;
    }
  }

}

// Styles for date field container can be removed if not needed
.dateFieldContainer {
  width: 100%;
  min-height: 62px;
  display: flex;
  align-items: center;
  position: relative;

  .dateField {
    display: flex;
    align-items: center;
    gap: 16px;
    font-size: 15px;
    font-weight: 500;
    width: 100%;
    height: 100%;
    padding: 12px 18px;
    cursor: pointer;
    position: relative;
    background-color: $background-input;
    border-radius: $border-radius-md;
    border: 1px solid $border-color;
    box-shadow: 0 2px 6px $shadow-color;
    transition: all 0.2s ease;

    &:hover, &:focus, &.active {
      border-color: rgba($primary-color, 0.5);
      box-shadow: 0 3px 10px rgba($primary-color, 0.15);
    }

    .fa-calendar-days {
      font-size: 20px;
      color: $primary-color;
      background: rgba($primary-color, 0.1);
      height: 38px;
      width: 38px;
      display: flex;
      align-items: center;
      justify-content: center;
      border-radius: 50%;
    }

    .label-inputField {
      display: flex;
      flex-direction: column;
      flex: 1;

      .label {
        font-size: 13px;
        font-weight: 600;
        color: $primary-color;
        margin: 0 0 4px 0;
        text-transform: uppercase;
        letter-spacing: 0.5px;
      }

      .inputField {
        font-size: 15px;
        font-weight: 500;
        color: $text-dark;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }
    }
  }
}
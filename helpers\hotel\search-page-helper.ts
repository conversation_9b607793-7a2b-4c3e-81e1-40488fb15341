import { autoSuggestion, HotelSearchFormData } from "@/models/hotel/search-page.model";

export const saveToRecentLocations = (newLocation: autoSuggestion) => {
  if (typeof window === "undefined") return;
  try {
    const existingRaw = localStorage.getItem("RecentLocations");
    let locations: autoSuggestion[] = existingRaw ? JSON.parse(existingRaw) : [];
    
    // Remove if it already exists to move it to the top
    locations = locations.filter(loc => loc.id !== newLocation.id);
    
    // Add to the beginning
    locations.unshift(newLocation);
    
    // Trim the array if it's too long
    if (locations.length > 5) {
      locations = locations.slice(0, 5);
    }
    
    localStorage.setItem("RecentLocations", JSON.stringify(locations));
  } catch (error) {
    console.error("Failed to save recent location:", error);
  }
};

export const getRecentLocations = (): autoSuggestion[] => {
    if (typeof window === "undefined") return [];
    try {
        const stored = localStorage.getItem("RecentLocations");
        return stored ? JSON.parse(stored) : [];
    } catch (error) {
        console.error("Failed to get recent locations:", error);
        return [];
    }
};

export const saveToRecentSearches = (newSearch: HotelSearchFormData) => {
  if (typeof window === "undefined") return;
  try {
    const existingRaw = localStorage.getItem("recentSearches");
    let searches: HotelSearchFormData[] = existingRaw ? JSON.parse(existingRaw) : [];

    // Use locationId as a unique identifier for the search
    searches = searches.filter(search => search.locationId !== newSearch.locationId);

    searches.unshift(newSearch);

    if (searches.length > 3) {
      searches = searches.slice(0, 3);
    }

    localStorage.setItem("recentSearches", JSON.stringify(searches));
  } catch (error) {
    console.error("Failed to save recent search:", error);
  }
};

export const getRecentSearches = (): HotelSearchFormData[] => {
    if (typeof window === "undefined") return [];
    try {
        const stored = localStorage.getItem("recentSearches");
        return stored ? JSON.parse(stored) : [];
    } catch (error) {
        console.error("Failed to get recent searches:", error);
        return [];
    }
};

export function filterHotelLocationByName( suggestions: autoSuggestion[], searchTerm: string): autoSuggestion[] {
  const term = searchTerm.trim().toLowerCase();
  return suggestions.filter(s =>
    s.name.toLowerCase().includes(term)
  );
}

@use "/styles/variable" as *;
@use "sass:color";
@use "/styles/zIndex" as *;

.searchContainer {
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  background-color: $white_color;
  border: 1px solid $border_color;

  &:hover {
    box-shadow: 0 20px 50px rgba(0, 0, 0, 0.2);
  }

  /* Add compact mode styles */
  &.modifyMode {
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
    width: 100%; /* Make sure it takes full width */

    /* Smaller overall container */
    .searchButton {
      padding: 8px 16px;
      font-size: 14px;
      border-radius: 4px;
    }

    input {
      font-size: 12px;
    }

    /* Media query for responsive layout in modify mode */
    @media (max-width: 768px) {
      .searchButton {
        width: 100%;
      }
    }
  }

  // Remove border radius for mobile screens (≤950px)
  @media (max-width: 950px) {
    border-radius: 0 !important;
  }
}

.shareGroup {
  background: #fff;
  border: 1px solid #e3e7ea;
  border-radius: 5px;
  //padding: 5px 3px;
  margin: 5px 0;
  margin-right: 10px;
  display: flex;
  flex-direction: row;
  align-items: center;

  opacity: 0;
  transform: translateX(30%);
  transition: opacity 0.6s ease, transform 0.6s ease;
  pointer-events: none;

  &.active {
    opacity: 1;
    transform: translateX(0);
    pointer-events: auto;
  }

  .labelWrapper {
    padding: 0 5px 0 2px;
    max-width: 100px;
    border-right: 1px solid #efefef;

    .label {
      padding: 0 8px;
      border-radius: 6px;
      background-color: $primary-color;
      color: #fff;
      display: flex;
      flex-direction: row;
      align-items: center;
      flex-wrap: nowrap;

      .count {
        font-size: 30px;
        font-weight: 400;
      }

      .label {
        font-size: 12px;
        line-height: 1.2;
        font-weight: 400;
      }
    }
  }

  .item {
    padding: 0 10px;
    height: 100%;
    border-right: 1px solid #efefef;
    display: flex;
    align-items: center;

    &:last-child {
      border-right: none;
    }

    .socialMediaIcon {
      font-size: 22px;
      color: #25D366;
      cursor: pointer;
    }

    .envelopeIcon {
      font-size: 20px;
      cursor: pointer;
    }
  }
}


.shareButtonContainer {
  display: flex;
  justify-content: center;
  align-items: center;
}

.shareButton {
  color: #000;
  height: 34px;
  padding: 0 10px;
  border-radius: 4px;
  font-weight: 500;
  border: 1px solid #002543;
  font-size: 16px;
  color: #000;
  position: relative;
  white-space: nowrap;
  background: #fff;
  margin-right: 15px;
  cursor: pointer;
  transition: background-color 0.2s ease, color 0.2s ease,
    border-color 0.2s ease;
  display: flex;
  align-items: center;

  &:hover {
    background-color: $primary-color;
    color: #fff;
    border-color: $primary-color;
  }
  .shareIcon {
    font-size: 20px;
  }
}

.searchButton {
  background-color: $primary-color;
  // background: linear-gradient(
  //   to right,
  //   color.adjust($primary_color, $lightness: -5%),
  //   color.adjust($secondary_color, $lightness: -5%)
  // );
  color: $white_color;
  border: none;
  padding: 12px 24px;
  border-radius: 4px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;

  @media (max-width: $breakpoint-md) {
    width: 100%;
  }

  &:hover {
    background: linear-gradient(
      to right,
      color.adjust($primary_color, $lightness: -5%),
      color.adjust($secondary_color, $lightness: -5%)
    );
  }

  /* Modify button style */
  &.modifyButton {
    padding: 8px 16px;
    font-size: 14px;
  }
}

.textPrimary {
  color: $primary_color;
}

.textSecondary {
  color: $secondary_color;
}

.bgPrimary {
  background-color: $primary_color;
}

.bgSecondary {
  background-color: $secondary_color;
}

.hoverTextPrimary {
  &:hover {
    color: color.adjust($primary_color, $lightness: -10%);
  }
}

.hoverTextSecondary {
  &:hover {
    color: color.adjust($secondary_color, $lightness: -10%);
  }
}

.focusRing {
  &:focus {
    outline: none;
    box-shadow: 0 0 0 2px rgba($primary_color, 0.3);
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(100%);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

// Dynamic Mobile Modal System
.mobileModal {
  animation: slideUp 0.3s ease-out;

  // Use CSS custom properties for dynamic height
  max-height: var(--modal-max-height, 80vh);
  height: var(--modal-max-height, 80vh);
  min-height: 300px;

  // Enable internal scrolling when content exceeds available space
  overflow-y: auto;
  overflow-x: hidden;

  // Smooth transitions for height changes
  transition: height 0.3s ease, max-height 0.3s ease;

  @media (max-width: 480px) {
    max-width: 100%;
    border-radius: 16px 16px 0 0;
    // Adjust for smaller screens with safety padding
    max-height: calc(100vh - var(--modal-safety-padding, 40px));
    height: calc(100vh - var(--modal-safety-padding, 40px));
    min-height: 250px;
  }
}

// Dynamic height modal for location selection
.fullHeight {
  // For mobile: take full viewport height minus safety padding
  height: calc(100vh - var(--modal-safety-padding, 40px));
  max-height: calc(100vh - var(--modal-safety-padding, 40px));

  @media (max-width: 480px) {
    height: calc(100vh - var(--modal-safety-padding, 20px));
    max-height: calc(100vh - var(--modal-safety-padding, 20px));
  }
}

// Dynamic height modal for date and travelers
.partialHeight {
  // Use calculated height from JavaScript
  height: var(--modal-max-height, 80vh);
  max-height: var(--modal-max-height, 80vh);
  min-height: 300px;

  @media (max-width: 480px) {
    min-height: 250px;
  }
}

// Dynamic Modal Height System
.dropdown {
  background-color: $white_color;
  border: 1px solid $border_color;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
  border-radius: 8px;
  position: fixed; /* Fixed positioning for better control */
  z-index: z-index(dropdown); /* High z-index to ensure it appears above other elements */
  max-width: 100%;
  width: 350px; /* Default width */
  animation: fadeIn 0.2s ease-out;
  overscroll-behavior: contain;

  // Dynamic height management using CSS custom properties
  max-height: var(--modal-max-height, 500px);
  height: var(--modal-max-height, auto); // Use dynamic height
  overflow: visible; // Allow content to be visible (prevent clipping of flags and other elements)

  // Ensure minimum height is respected
  min-height: 300px;

  // Smooth transitions for height changes
  transition: max-height 0.3s ease, height 0.3s ease;

  // Flex layout for proper height distribution
  display: flex;
  flex-direction: column;

  /* Ensure consistent border in both LTR and RTL */
  &.travelerDropdown {
    border: 1px solid $border_color;
    max-height: var(--modal-max-height, 400px);
    height: var(--modal-max-height, auto);
    min-height: 250px;
    overflow-y: auto; // Travelers dropdown can scroll normally
  }

  /* Special styling for date picker dropdown */
  &.dateDropdown {
    width: 650px; /* Wider for the date picker */
    max-height: var(--modal-max-height, 600px);
    height: var(--modal-max-height, auto);
    min-height: 400px;
    overflow-y: auto; // Date picker can scroll normally

    @media (max-width: 992px) {
      width: 95vw !important;
      left: 2.5vw !important;
      max-height: var(--modal-max-height, calc(100vh - 100px));
    }
  }

  // Location dropdown specific styling
  &.locationDropdown {
    max-height: var(--modal-max-height, 450px);
    min-height: 200px;
    width: 420px; // Increased width to accommodate flag + country name
    padding: 0; // Remove default padding to let LocationSearch handle its own layout
    overflow: visible; // Allow content to be visible (flags won't be clipped)
    display: flex;
    flex-direction: column;

    // Ensure LocationSearch component uses full height
    > * {
      height: 100%;
      max-height: 100%;
    }

    @media (max-width: 768px) {
      width: 95vw; // Full width on mobile
      left: 2.5vw !important;
    }
  }

  @media (max-width: 768px) {
    width: 95vw !important;
    left: 2.5vw !important;
    max-height: var(--modal-max-height, calc(100vh - 80px));
    min-height: 250px;
  }

  // Responsive breakpoints for mobile optimization
  @media (max-width: 480px) {
    width: calc(100vw - var(--modal-safety-padding, 40px)) !important;
    left: calc(var(--modal-safety-padding, 40px) / 2) !important;
    max-height: var(--modal-max-height, calc(100vh - 60px));
    min-height: 200px;
  }
}

/* Create a local class for RTL border styling */
.rtlBorder {
  /* This class will be applied conditionally in the component */
  html[dir="rtl"] & {
    border-right: 1px solid $border_color !important;
    // border-radius: 8px !important;
    overflow: hidden !important;
  }
}

/* Mobile modal date picker fixes */
.mobileModalDateContainer {
  // Force containment of all calendar elements
  overflow: hidden !important;
  position: relative !important;
  width: 100% !important;
  height: 100% !important;

  // Override the calendar wrapper positioning
  :global(.calendarWrapper) {
    position: static !important;
    z-index: auto !important;
    width: 100% !important;
    height: auto !important;
    max-height: 100% !important;
    display: block !important;
    overflow: hidden !important;
  }

  // Override the calendar container
  :global(.calendarContainer) {
    position: static !important;
    z-index: auto !important;
    width: 100% !important;
    max-width: 100% !important;
    margin: 0 !important;
    box-shadow: none !important;
    border: none !important;
    border-radius: 8px !important;
    overflow: hidden !important;
    max-height: 100% !important;
  }

  // Make the calendar content fit properly and scrollable
  :global(.calendarContent) {
    padding: 8px !important;
    height: 100% !important;
    max-height: none !important;
    overflow-y: auto !important;
    overflow-x: hidden !important;
    display: flex !important;
    flex-direction: column !important;
  }

  // Adjust the two months layout for mobile
  :global(.twoMonthsLayout) {
    flex-direction: column !important;
    min-width: auto !important;
    gap: 12px !important;
    width: 100% !important;
  }

  // Make calendar months responsive
  :global(.calendarMonth) {
    width: 100% !important;
    max-width: 100% !important;
    min-width: auto !important;
  }

  // Adjust calendar header - make it smaller
  :global(.calendarHeader) {
    margin: -12px -12px 12px -12px !important;
    padding: 12px !important;

    h2 {
      font-size: 14px !important;
    }
  }

  // Make month navigation more compact
  :global(.monthNavigation) {
    margin-bottom: 8px !important;
    padding: 0 4px !important;

    .monthLabels span {
      font-size: 12px !important;
      padding: 4px 8px !important;
    }

    .navButton {
      width: 24px !important;
      height: 24px !important;

      i {
        font-size: 10px !important;
      }
    }
  }

  // Make calendars wrapper fill available space
  :global(.calendarsWrapper) {
    padding: 8px !important;
    flex: 1 !important;
    display: flex !important;
    flex-direction: column !important;
  }

  // Ensure footer is visible and compact
  :global(.calendarFooter) {
    margin-top: 12px !important;
    padding-top: 8px !important;

    .legend {
      gap: 8px !important;

      .legendItem {
        font-size: 9px !important;
      }
    }

    .applyButton {
      padding: 6px 12px !important;
      font-size: 12px !important;
    }
  }

  // Override any react-datepicker styles if they exist
  :global(.react-datepicker),
  :global(.react-datepicker-wrapper),
  :global(.react-datepicker__tab-loop) {
    position: static !important;
    top: auto !important;
    left: auto !important;
    right: auto !important;
    bottom: auto !important;
    transform: none !important;
    width: 100% !important;
    max-width: 100% !important;
  }
}

// Mobile Search Container - Stacked Cards Approach (≤950px)
.mobileSearchContainer {
  display: flex;
  flex-direction: column;
  gap: 16px;
  padding: 20px 16px;

  @media (min-width: 951px) {
    display: none; // Hide on desktop
  }

  // Apply lightest gradient of #003b95 for mobile view (≤950px)
  @media (max-width: 950px) {
    background: linear-gradient(135deg,
      rgba(0, 59, 149, 0.12) 0%,
      rgba(0, 59, 149, 0.18) 50%,
      rgba(0, 59, 149, 0.15) 100%
    );
    border-radius: 0 !important;
  }

  // Adjust for smaller mobile screens
  @media (max-width: 480px) {
    padding: 16px 12px;
    gap: 14px;
  }
}

.mobileSearchCard {
  background: white;
  border: 1px solid #e5e7eb;
  border-radius: 12px;
  padding: 16px;
  cursor: pointer;
  transition: all 0.2s ease;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);

  &:hover {
    border-color: $primary-color;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    transform: translateY(-1px);
  }

  &:active {
    transform: translateY(0);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  }
}

.mobileCardHeader {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 8px;

  .mobileCardLabel {
    font-size: 14px;
    font-weight: 600;
    color: #374151;
    flex: 1;
  }

  .mobileErrorText {
    font-size: 12px;
    color: #dc2626;
    font-weight: 500;
  }
}

.mobileCardContent {
  display: flex;
  align-items: center;
  gap: 12px;
   

  .mobileInput {
    flex: 1;
    font-size: 16px !important;
    font-weight: 500;
    color: #111827;
    background: transparent;
    border: none;
    outline: none;
    cursor: pointer;

    &::placeholder {
      color: #9ca3af;
      font-weight: 400;
    }
  }

  .mobileCardValue {
    flex: 1;
    font-size: 16px;
    font-weight: 500;
    color: #111827;
  }

  .mobileLocationButton {
    padding: 8px;
    background: #f3f4f6;
    border: 1px solid #d1d5db;
    border-radius: 8px;
    color: #6b7280;
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    justify-content: center;

    &:hover {
      background: #e5e7eb;
      color: #374151;
    }

    &:disabled {
      opacity: 0.6;
      cursor: not-allowed;
    }
  }
}

.mobileSearchButton {
  background: linear-gradient(135deg, $primary-color 0%, color.adjust($primary-color, $lightness: -10%) 100%);
  color: white;
  border: none;
  border-radius: 12px;
  padding: 18px 24px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  box-shadow: 0 4px 12px rgba($primary-color, 0.3);
  margin-top: 8px;

  &:hover {
    background: linear-gradient(135deg, color.adjust($primary-color, $lightness: -5%) 0%, color.adjust($primary-color, $lightness: -15%) 100%);
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba($primary-color, 0.4);
  }

  &:active {
    transform: translateY(0);
    box-shadow: 0 2px 8px rgba($primary-color, 0.3);
  }

  // Remove border radius for mobile screens (≤950px)
  @media (max-width: 950px) {
    border-radius: 12px !important;
  }
}

// Dynamic Modal Content Wrapper
.dynamicModalContent {
  height: 100%;
  display: flex;
  flex-direction: column;
  overflow: hidden;

  // Modal header - always visible
  .modalHeader {
    flex-shrink: 0;
    padding: 16px;
    border-bottom: 1px solid #e5e7eb;
    background: white;
    position: sticky;
    top: 0;
    z-index: 10;
  }

  // Modal body - scrollable content area
  .modalBody {
    flex: 1;
    overflow-y: auto;
    overflow-x: hidden;
    padding: 16px;

    // Custom scrollbar styling
    &::-webkit-scrollbar {
      width: 6px;
    }

    &::-webkit-scrollbar-track {
      background: #f1f1f1;
      border-radius: 3px;
    }

    &::-webkit-scrollbar-thumb {
      background: #c1c1c1;
      border-radius: 3px;

      &:hover {
        background: #a8a8a8;
      }
    }
  }

  // Modal footer - always visible
  .modalFooter {
    flex-shrink: 0;
    padding: 16px;
    border-top: 1px solid #e5e7eb;
    background: white;
    position: sticky;
    bottom: 0;
    z-index: 10;
  }
}

// Responsive modal adjustments
.responsiveModal {
  // Desktop: dynamic height based on available space
  @media (min-width: 769px) {
    position: fixed;
    top: var(--modal-top-position, 50%);
    left: var(--modal-left-position, 50%);
    transform: translateX(-50%);
    width: auto;
    min-width: 350px;
    max-width: 90vw;
  }

  // Mobile: full width with dynamic height
  @media (max-width: 768px) {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    width: 100%;
    border-radius: 16px 16px 0 0;
    transform: none;
  }

  // Special handling for location dropdown
  &.locationDropdown {
    @media (min-width: 769px) {
      // Don't center horizontally for location dropdown
      left: var(--modal-left-position, auto);
      transform: none;
    }
  }
}

// Location dropdown specific fixes
.locationDropdownContent {
  display: flex;
  flex-direction: column;
  height: 100%;

  // Fixed search header
  .locationSearchHeader {
    flex-shrink: 0;
    background: white;
    border-bottom: 1px solid #e5e7eb;
    padding: 16px;
    position: sticky;
    top: 0;
    z-index: 10;
  }

  // Scrollable results area
  .locationSearchResults {
    flex: 1;
    overflow-y: auto;
    overflow-x: hidden;

    // Custom scrollbar
    &::-webkit-scrollbar {
      width: 6px;
    }

    &::-webkit-scrollbar-track {
      background: #f1f1f1;
      border-radius: 3px;
    }

    &::-webkit-scrollbar-thumb {
      background: #c1c1c1;
      border-radius: 3px;

      &:hover {
        background: #a8a8a8;
      }
    }
  }
}

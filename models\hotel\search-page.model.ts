export interface HotelSearchFormData {
  searchQuery: string;
  fullLocationData?: string;
  locationId?: string;
  geoCode?: { lat: string; long: string };
  checkInDate: string | null;
  checkOutDate: string | null;
  travelers: {
    adults: number;
    rooms: number;
    children: number;
  };
  roomsData: SearchRoom[];
}

export interface ChildAge {
  id: number;
  age: number;
}

export interface SearchRoom{
  id: number;
  adults: number;
  children: number;
  childrenAges: ChildAge[];
}


// Hotel Search Data Models
export interface HotelSearchData {
  geoCode: GeoCode
  locationId: string
  currency: string
  culture: string
  checkIn: string
  checkOut: string
  rooms: Room[]
}

export interface GeoCode {
  lat: string
  long: string
}

export interface Room {
  adults: string
  children: string
  childAges: number[]
}

// Search API Models
export interface SearchApiRequest {
  search_key: string
}

export interface LocationApiResponse {
  message: string;
}

export interface autoSuggestion { 
  coordinates: Coordinates;
  country: string;
  fullName: string;
  id: string;
  name: string;
  referenceScore: number;
  state?: string;
  type?: autoSuggestLocationTypes;
  code?: string; // only for airports
  city?: string; // only for hotels
  referenceId?: string; // only for hotels
}

export type autoSuggestLocationTypes =
  | "City"
  | "State"
  | "Airport"
  | "PointOfInterest"
  | "MultiCity"
  | "Neighborhood"
  | "Region"
  | "Country"
  | "Hotel";

export interface Coordinates {
  lat: number;
  long: number;
}



export interface autoSuggestionsData {
  locationSuggestions: autoSuggestion[];
  status: "success" | "error";
}

export interface autoSuggestionsResponse {
  provider: string;
  data: autoSuggestionsData;
}

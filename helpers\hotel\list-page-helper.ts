import { Hotel, SearchInitApiResponse, SearchInitHotel, SearchResultBatch } from "@/models/hotel/list-page.model";

function getUserRatingCategory(rating: number): string {
  if (rating >= 4.5) return 'Excellent';
  if (rating >= 4.0) return 'Very Good';
  if (rating >= 3.5) return 'Good';
  if (rating >= 3.0) return 'Average';
  if (rating > 0) return 'Fair';
  return 'Not Rated';
}

export function transformSearchInitApiResponse(apiResponse: SearchInitApiResponse): Hotel[] {
  const allHotels: Hotel[] = [];

  if (!apiResponse || !apiResponse.results) {
    return [];
  }

  apiResponse.results.forEach((resultBatch: SearchResultBatch) => {
    if (!resultBatch || !resultBatch.hotels) {
      return; 
    }

    resultBatch.hotels.forEach((apiHotel: SearchInitHotel) => {
      const mainFare = apiHotel.fareDetail?.[0] || { totalPrice: 0, taxes: 0 };
      const userRatingCategory = apiHotel.userRatingCategoty ?? getUserRatingCategory(apiHotel.userRating);
      const specialAmenitiesSet = new Set(apiHotel.specialAmenities);
      const sanitizedAmenities = apiHotel.amenities.filter(amenity => !specialAmenitiesSet.has(amenity));

      const transformedHotel: Hotel = {
        id: apiHotel.id,
        hotelId: parseInt(apiHotel.hotelId, 10),
        locationId: parseInt(resultBatch.location_id, 10),
        name: apiHotel.name,
        address: apiHotel.address,
        city: apiHotel.city,
        userRating: apiHotel.userRating ? apiHotel.userRating.toString() : '0',
        userRatingCategory: userRatingCategory,
        starRating: parseInt(apiHotel.starRating, 10) || 0,
        comfortRating: apiHotel.comfortRating ?? 0,
        category: apiHotel.category,
        hotelType: apiHotel.HotelType ?? 'Hotel',
        accommodationType: apiHotel.HotelType ?? 'Hotel',
        roomsCountLeft: apiHotel.roomCountLeft ?? 0,
        geoLocationInfo: apiHotel.geoLocationInfo,
        isVisible: apiHotel.isVisible,
        about: apiHotel.about ?? '',
        amenities: sanitizedAmenities.map((name: string) => ({
          name: name,
          amenityUrl: '',
        })),
        specialAmenities: apiHotel.specialAmenities ?? [],
        imageInfoList: apiHotel.images.map((img) => ({
          pictureId: img.id.toString(),
          url: img.image_path,
          caption: img.alt_text,
          imageCategory: img.image_category_type,
          rank: img.sort_order,
        })),
        // topOfferings: apiHotel.attributes?.map((attr) => attr.value) ?? [],
        topOfferings: [],

        roomDetails: [
          {
            type: apiHotel.roomDetails.type ?? 'Standard Room',
            bedroom: parseInt(apiHotel.roomDetails.bedroom ?? '1', 10),
            livingRoom: parseInt(apiHotel.roomDetails.livingRoom ?? '0', 10),
            bathroom: parseInt(apiHotel.roomDetails.bathroom ?? '1', 10),
            size: apiHotel.roomDetails.size ?? 'N/A',
            bed: apiHotel.roomDetails.bed ?? 'Standard',
          },
        ],
        fareDetail: {
          totalPrice: mainFare.totalPrice,
          displayedBaseFare: mainFare.displayedBaseRate,
          board_basis: mainFare.board_basis ?? 'Room Only',
          currency_code: mainFare.currency_code,
          refundable: mainFare.refundable,
          taxes: mainFare.taxes,
        },
        taxesAndCharges: mainFare.taxes ?? 0,
        userRatingCount: 0, // Not available in the new API response
        distanceFromSearchedEntity: '', // Needs to be calculated separately
        fomoTags: [], // Not available in the new API response
        offer: {
          couponCode: '',
          instantDiscount: 0,
          displayedInstantDiscount: 0,
          cashback: 0,
          displayedCashback: 0,
          applyMessage: '',
        },
      };

      allHotels.push(transformedHotel);
    });
  });

  return allHotels;
}

export const updatePaginatedHotels = (
  newHotels: Hotel[],
  previousPages: Hotel[][],
  pageSize: number = 40
): Hotel[][] => {
  // If there are no previous pages, just paginate the new hotels and return.
  if (!previousPages || previousPages.length === 0) {
    const pages: Hotel[][] = [];
    for (let i = 0; i < newHotels.length; i += pageSize) {
      pages.push(newHotels.slice(i, i + pageSize));
    }
    return pages;
  }

  // Create a mutable copy of the previous pages.
  const updatedPages = [...previousPages];
  
  // Get the last page from the list. It might be partially filled.
  const lastPage = updatedPages.pop() || [];
  
  // Combine the hotels from the last page with the new batch of hotels.
  const combinedHotels = [...lastPage, ...newHotels];

  // Re-paginate the combined list.
  for (let i = 0; i < combinedHotels.length; i += pageSize) {
    const newPage = combinedHotels.slice(i, i + pageSize);
    updatedPages.push(newPage);
  }

  return updatedPages;
};
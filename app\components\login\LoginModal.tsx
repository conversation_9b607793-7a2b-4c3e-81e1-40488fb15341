// 'use client';

// import { useState, useEffect } from 'react';
// import Image from 'next/image';
// import Logo2 from "../../../public/assets/img/logo2.jpg";
// import { X, Check, AlertCircle, ArrowLeft } from 'lucide-react';
// import { useCommonContext } from '@/app/contexts/commonContext';
// import OtpInput from '../common/OtpInput';
// import './LoginSignupPopup.scss';
// import './LoginModal.scss';

// interface LoginModalProps {
//   isOpen: boolean;
//   onClose: () => void;
// }

// // Mock user database for testing
// const MOCK_USERS = [
//   { id: 1, name: '<PERSON>', email: '<EMAIL>', whatsapp: '9876543210', isRegistered: true },
//   { id: 2, name: '<PERSON>', email: '<EMAIL>', whatsapp: '9876543211', isRegistered: true },
//   // Add more mock users as needed
// ];

// // Login flow states
// type LoginStep = 'initial' | 'otp-verification' | 'registration';

// const LoginModal: React.FC<LoginModalProps> = ({ isOpen, onClose }) => {
//   // Common state
//   const [loginStep, setLoginStep] = useState<LoginStep>('initial');
//   const [identifier, setIdentifier] = useState(''); // Email or WhatsApp
//   const [message, setMessage] = useState<{type: 'success' | 'error', text: string} | null>(null);
//   const { isLoading, setIsLoading, setUserData, setIsLoggedIn } = useCommonContext();
//   const [showSuccessBeforeClose, setShowSuccessBeforeClose] = useState(false);

//   // OTP verification state
//   const [otp, setOtp] = useState(['', '', '', '', '', '']);
//   const [otpTimer, setOtpTimer] = useState(60);
//   const [isOtpSent, setIsOtpSent] = useState(false);

//   // Registration state
//   const [name, setName] = useState('');
//   const [email, setEmail] = useState('');
//   const [whatsapp, setWhatsapp] = useState('');
//   const [emailOtp, setEmailOtp] = useState(['', '', '', '']);
//   const [whatsappOtp, setWhatsappOtp] = useState(['', '', '', '']);
//   const [isEmailVerified, setIsEmailVerified] = useState(false);
//   const [isWhatsappVerified, setIsWhatsappVerified] = useState(false);

//   // Reset form when modal is opened/closed
//   useEffect(() => {
//     if (isOpen) {
//       resetForm();
//     }
//   }, [isOpen]);

//   // Handle success messages with delay
//   useEffect(() => {
//     if (showSuccessBeforeClose) {
//       const timer = setTimeout(() => {
//         onClose();
//         setShowSuccessBeforeClose(false);
//       }, 2000);

//       return () => clearTimeout(timer);
//     }
//   }, [showSuccessBeforeClose, onClose]);

//   // OTP timer countdown
//   useEffect(() => {
//     let interval: NodeJS.Timeout;
//     if (isOtpSent && otpTimer > 0) {
//       interval = setInterval(() => {
//         setOtpTimer((prev) => prev - 1);
//       }, 1000);
//     } else if (otpTimer === 0) {
//       setIsOtpSent(false);
//     }
//     return () => clearInterval(interval);
//   }, [isOtpSent, otpTimer]);

//   if (!isOpen) return null;

//   // Reset all form fields
//   const resetForm = () => {
//     setLoginStep('initial');
//     setIdentifier('');
//     setMessage(null);
//     setOtp(['', '', '', '', '', '']);
//     setOtpTimer(60);
//     setIsOtpSent(false);
//     setName('');
//     setEmail('');
//     setWhatsapp('');
//     setEmailOtp(['', '', '', '']);
//     setWhatsappOtp(['', '', '', '']);
//     setIsEmailVerified(false);
//     setIsWhatsappVerified(false);
//   };

//   // Handle initial form submission
//   const handleInitialSubmit = (e: React.FormEvent) => {
//     e.preventDefault();
//     setIsLoading(true);
//     setMessage(null);

//     // Validate input
//     if (!identifier) {
//       setMessage({
//         type: 'error',
//         text: 'Please enter your email or WhatsApp number.'
//       });
//       setIsLoading(false);
//       return;
//     }

//     // Check if user exists in mock database
//     const userExists = MOCK_USERS.some(
//       user => user.email === identifier || user.whatsapp === identifier
//     );

//     // Simulate API call with setTimeout
//     setTimeout(() => {
//       if (userExists) {
//         // User found, proceed to OTP verification
//         setLoginStep('otp-verification');
//         setIsOtpSent(true);
//         setMessage({
//           type: 'success',
//           text: 'OTP sent successfully. Please check your email or WhatsApp.'
//         });
//       } else {
//         // User not found, proceed to registration
//         setLoginStep('registration');

//         // Pre-fill email or WhatsApp based on input format
//         if (identifier.includes('@')) {
//           setEmail(identifier);
//         } else {
//           setWhatsapp(identifier);
//         }
//       }
//       setIsLoading(false);
//     }, 1000);
//   };

//   // Handle OTP verification
//   const handleOtpVerification = (e: React.FormEvent) => {
//     e.preventDefault();
//     setIsLoading(true);
//     setMessage(null);

//     // Validate OTP
//     const fullOtp = otp.join('');
//     if (fullOtp.length !== 6) {
//       setMessage({
//         type: 'error',
//         text: 'Please enter a valid 6-digit OTP.'
//       });
//       setIsLoading(false);
//       return;
//     }

//     // Simulate API call with setTimeout
//     setTimeout(() => {
//       // For demo, any 6-digit OTP is valid
//       if (fullOtp === '123456') {
//         setMessage({
//           type: 'success',
//           text: 'Login successful! Welcome back.'
//         });

//         // Find the user from mock database
//         const user = MOCK_USERS.find(
//           user => user.email === identifier || user.whatsapp === identifier
//         );

//         // Set user data and logged in state
//         if (user) {
//           const userData = {
//             first_name: user.name,
//             second_name: '',
//             email: user.email,
//             phone: user.whatsapp
//           };
//           localStorage.setItem('userData', JSON.stringify(userData));
//           setUserData(userData);
//           setIsLoggedIn(true);
//         }

//         // Show success message before closing
//         setShowSuccessBeforeClose(true);
//       } else {
//         setMessage({
//           type: 'error',
//           text: 'Invalid OTP. Please try again.'
//         });
//       }
//       setIsLoading(false);
//     }, 1000);
//   };

//   // Handle registration form submission
//   const handleRegistration = (e: React.FormEvent) => {
//     e.preventDefault();
//     setIsLoading(true);
//     setMessage(null);

//     // Validate form
//     if (!name || !email || !whatsapp) {
//       setMessage({
//         type: 'error',
//         text: 'Please fill in all required fields.'
//       });
//       setIsLoading(false);
//       return;
//     }

//     // Validate email format
//     const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
//     if (!emailRegex.test(email)) {
//       setMessage({
//         type: 'error',
//         text: 'Please enter a valid email address.'
//       });
//       setIsLoading(false);
//       return;
//     }

//     // Validate WhatsApp number (simple validation)
//     if (!/^\d{10}$/.test(whatsapp)) {
//       setMessage({
//         type: 'error',
//         text: 'Please enter a valid 10-digit WhatsApp number.'
//       });
//       setIsLoading(false);
//       return;
//     }

//     // Check if both email and WhatsApp are verified
//     if (!isEmailVerified || !isWhatsappVerified) {
//       setMessage({
//         type: 'error',
//         text: 'Please verify both your email and WhatsApp number.'
//       });
//       setIsLoading(false);
//       return;
//     }

//     // Simulate API call with setTimeout
//     setTimeout(() => {
//       setMessage({
//         type: 'success',
//         text: 'Registration successful! Welcome to EcoGO.'
//       });

//       // Create user data and update state
//       const userData = {
//         first_name: name,
//         second_name: '',
//         email: email,
//         phone: whatsapp
//       };
//       localStorage.setItem('userData', JSON.stringify(userData));
//       setUserData(userData);
//       setIsLoggedIn(true);

//       // Show success message before closing
//       setShowSuccessBeforeClose(true);
//       setIsLoading(false);
//     }, 1000);
//   };

//   // Handle OTP input change
//   const handleOtpChange = (newOtp: string[], setter: React.Dispatch<React.SetStateAction<string[]>>) => {
//     setter(newOtp);
//   };

//   // Send OTP to email or WhatsApp
//   const sendOtp = (type: 'email' | 'whatsapp') => {
//     setIsLoading(true);

//     // Simulate API call with setTimeout
//     setTimeout(() => {
//       setIsOtpSent(true);
//       setOtpTimer(60);
//       setMessage({
//         type: 'success',
//         text: `OTP sent to your ${type}. Please check and enter below.`
//       });
//       setIsLoading(false);
//     }, 1000);
//   };

//   // Verify OTP for email or WhatsApp during registration
//   const verifyOtp = (type: 'email' | 'whatsapp') => {
//     setIsLoading(true);
//     const otpToVerify = type === 'email' ? emailOtp.join('') : whatsappOtp.join('');

//     // Validate OTP
//     if (otpToVerify.length !== 4) {
//       setMessage({
//         type: 'error',
//         text: `Please enter a valid 4-digit OTP for your ${type}.`
//       });
//       setIsLoading(false);
//       return;
//     }

//     // Simulate API call with setTimeout
//     setTimeout(() => {
//       // For demo, any 4-digit OTP is valid
//       if (otpToVerify === '1234') {
//         if (type === 'email') {
//           setIsEmailVerified(true);
//         } else {
//           setIsWhatsappVerified(true);
//         }
//         setMessage({
//           type: 'success',
//           text: `Your ${type} has been verified successfully.`
//         });
//       } else {
//         setMessage({
//           type: 'error',
//           text: `Invalid OTP for ${type}. Please try again.`
//         });
//       }
//       setIsLoading(false);
//     }, 1000);
//   };

//   return (
//     <div className="fixed inset-0 z-[9999] flex items-center justify-center bg-black/40 backdrop-blur-sm p-4">
//       <div className="relative w-full max-w-md bg-white rounded-lg shadow-xl overflow-hidden">
//         {/* Close button */}
//         <div className="absolute top-3 right-3">
//           <button
//             onClick={onClose}
//             className="text-gray-400 hover:text-gray-600 transition-colors"
//             aria-label="Close popup"
//           >
//             <X size={18} />
//           </button>
//         </div>

//         {/* Logo centered at top */}
//         <div className="flex justify-center pt-8 pb-4">
//           <div className="w-14 h-14 relative">
//             <Image
//               src={Logo2}
//               alt="Readdy Logo"
//               className="object-contain"
//               fill
//               priority
//             />
//           </div>
//         </div>

//         {/* Form Header */}
//         <div className="text-center px-8">
//           <h1 className="text-2xl font-bold text-gray-900 mb-2">
//             {loginStep === 'initial' ? 'Sign in to Readdy' :
//              loginStep === 'otp-verification' ? 'Verify Your Identity' :
//              'Create Your Account'}
//           </h1>
//           <p className="text-sm text-gray-500 mb-6">
//             {loginStep === 'initial' ? 'Welcome back! Please sign in to continue' :
//              loginStep === 'otp-verification' ? 'Enter the OTP sent to your email or WhatsApp' :
//              'Please fill in your details to complete registration'}
//           </p>
//         </div>

//         <div className="px-8 pb-8">
//           {/* Back Button (for multi-step forms) */}
//           {loginStep !== 'initial' && (
//             <button
//               onClick={() => setLoginStep('initial')}
//               className="flex items-center text-indigo-600 hover:text-indigo-800 mb-4 text-sm font-medium"
//             >
//               <ArrowLeft size={16} className="mr-1" />
//               Back to login
//             </button>
//           )}

//           {/* Status Message */}
//           {message && (
//             <div className={`p-3 mb-4 rounded-lg flex items-start gap-2 text-sm ${
//               message.type === 'success'
//                 ? 'bg-green-50 text-green-800 border border-green-200'
//                 : 'bg-red-50 text-red-800 border border-red-200'
//             }`}>
//               {message.type === 'success'
//                 ? <Check size={16} className="text-green-500 mt-0.5 flex-shrink-0" />
//                 : <AlertCircle size={16} className="text-red-500 mt-0.5 flex-shrink-0" />
//               }
//               <span>{message.text}</span>
//             </div>
//           )}

//           {/* Initial Login Form */}
//           {loginStep === 'initial' && (
//             <form onSubmit={handleInitialSubmit}>
//               {/* Google Sign In Button */}
//               <button
//                 type="button"
//                 className="w-full flex items-center justify-center gap-3 border border-gray-300 rounded-md py-3 px-4 text-gray-700 font-medium mb-6 hover:bg-gray-50 transition-colors whitespace-nowrap cursor-pointer text-sm"
//               >
//                 <svg
//                   width="18"
//                   height="18"
//                   xmlns="http://www.w3.org/2000/svg"
//                   viewBox="0 0 48 48"
//                 >
//                   <path
//                     fill="#EA4335"
//                     d="M24 9.5c3.54 0 6.71 1.22 9.21 3.6l6.85-6.85C35.9 2.38 30.47 0 24 0 14.62 0 6.51 5.38 2.56 13.22l7.98 6.19C12.43 13.72 17.74 9.5 24 9.5z"
//                   />
//                   <path
//                     fill="#4285F4"
//                     d="M46.98 24.55c0-1.57-.15-3.09-.38-4.55H24v9.02h12.94c-.58 2.96-2.26 5.48-4.78 7.18l7.73 6c4.51-4.18 7.09-10.36 7.09-17.65z"
//                   />
//                   <path
//                     fill="#FBBC05"
//                     d="M10.53 28.59c-.48-1.45-.76-2.99-.76-4.59s.27-3.14.76-4.59l-7.98-6.19C.92 16.46 0 20.12 0 24c0 3.88.92 7.54 2.56 10.78l7.97-6.19z"
//                   />
//                   <path
//                     fill="#34A853"
//                     d="M24 48c6.48 0 11.93-2.13 15.89-5.81l-7.73-6c-2.15 1.45-4.92 2.3-8.16 2.3-6.26 0-11.57-4.22-13.47-9.91l-7.98 6.19C6.51 42.62 14.62 48 24 48z"
//                   />
//                 </svg>
//                 Continue with Google
//               </button>

//               {/* Divider */}
//               <div className="flex items-center mb-6">
//                 <div className="flex-1 border-t border-gray-200"></div>
//                 <span className="px-4 text-gray-400 text-xs font-medium">or</span>
//                 <div className="flex-1 border-t border-gray-200"></div>
//               </div>

//               {/* Email Input */}
//               <div className="mb-4">
//                 <label
//                   htmlFor="identifier"
//                   className="block text-sm font-medium text-gray-700 mb-1.5"
//                 >
//                   Email / whatsapp 
//                 </label>
//                 <input
//                   type="text"
//                   id="identifier"
//                   value={identifier}
//                   onChange={(e) => setIdentifier(e.target.value)}
//                   className="w-full px-4 py-3 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-transparent transition-all duration-200 text-sm"
//                   placeholder="Enter your email address"
//                 />
//               </div>

//               {/* Continue Button */}
//               <button
//                 type="submit"
//                 disabled={isLoading}
//                 className="w-full bg-gray-800 text-white py-3 px-4 rounded-md hover:bg-gray-700 transition-all duration-300 mt-4 mb-6 whitespace-nowrap cursor-pointer font-medium disabled:opacity-70 disabled:cursor-not-allowed text-sm"
//               >
//                 {isLoading ? (
//                   <span className="flex items-center justify-center">
//                     <svg
//                       className="animate-spin -ml-1 mr-3 h-4 w-4 text-white"
//                       xmlns="http://www.w3.org/2000/svg"
//                       fill="none"
//                       viewBox="0 0 24 24"
//                     >
//                       <circle
//                         className="opacity-25"
//                         cx="12"
//                         cy="12"
//                         r="10"
//                         stroke="currentColor"
//                         strokeWidth="4"
//                       ></circle>
//                       <path
//                         className="opacity-75"
//                         fill="currentColor"
//                         d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
//                       ></path>
//                     </svg>
//                     Processing...
//                   </span>
//                 ) : (
//                   <span className="flex items-center justify-center">
//                     Continue <span className="ml-1">›</span>
//                   </span>
//                 )}
//               </button>

//               {/* Sign Up Link */}
//               <div className="text-center text-gray-500 text-sm">
//                 Don&apos;t have an account?
//                 <button
//                   type="button"
//                   onClick={() => setLoginStep('registration')}
//                   className="text-indigo-600 font-medium ml-1 hover:underline cursor-pointer"
//                 >
//                   Sign up
//                 </button>
//               </div>
//             </form>
//           )}

//           {/* OTP Verification Form */}
//           {loginStep === 'otp-verification' && (
//             <form onSubmit={handleOtpVerification}>
//               <div className="mb-6">
//                 <div className="text-center mb-6">
//                   <p className="text-gray-700 text-sm flex items-center justify-center">
//                     {identifier}
//                     <button
//                       type="button"
//                       onClick={() => setLoginStep('initial')}
//                       className="ml-2 text-gray-500 hover:text-gray-700"
//                     >
//                       <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
//                         <path d="M16.5 3.5L7.5 12.5L16.5 21.5" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
//                       </svg>
//                     </button>
//                   </p>
//                 </div>

//                 <OtpInput
//                   length={6}
//                   value={otp}
//                   onChange={(newOtp) => handleOtpChange(newOtp, setOtp)}
//                   autoFocus={true}
//                   className="mb-4"
//                 />

//                 <div className="text-sm text-gray-500 text-center mb-6">
//                   Didn&apos;t receive a code?
//                   <button
//                     type="button"
//                     onClick={() => {
//                       setIsOtpSent(true);
//                       setOtpTimer(60);
//                     }}
//                     className="text-indigo-600 ml-1 hover:underline font-medium"
//                   >
//                     Resend {isOtpSent && otpTimer > 0 ? `(${otpTimer})` : ''}
//                   </button>
//                 </div>
//               </div>

//               <button
//                 type="submit"
//                 disabled={isLoading}
//                 className="w-full bg-gray-800 text-white py-3 px-4 rounded-md hover:bg-gray-700 transition-all duration-300 mb-4 whitespace-nowrap cursor-pointer font-medium disabled:opacity-70 disabled:cursor-not-allowed text-sm"
//               >
//                 {isLoading ? (
//                   <span className="flex items-center justify-center">
//                     <svg
//                       className="animate-spin -ml-1 mr-3 h-4 w-4 text-white"
//                       xmlns="http://www.w3.org/2000/svg"
//                       fill="none"
//                       viewBox="0 0 24 24"
//                     >
//                       <circle
//                         className="opacity-25"
//                         cx="12"
//                         cy="12"
//                         r="10"
//                         stroke="currentColor"
//                         strokeWidth="4"
//                       ></circle>
//                       <path
//                         className="opacity-75"
//                         fill="currentColor"
//                         d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
//                       ></path>
//                     </svg>
//                     Verifying...
//                   </span>
//                 ) : (
//                   <span className="flex items-center justify-center">
//                     Continue <span className="ml-1">›</span>
//                   </span>
//                 )}
//               </button>

//               <div className="text-center">
//                 <button
//                   type="button"
//                   className="text-gray-500 text-sm hover:underline"
//                 >
//                   Use another method
//                 </button>
//               </div>
//             </form>
//           )}

//           {/* Registration Form */}
//           {loginStep === 'registration' && (
//             <form onSubmit={handleRegistration}>
//               {/* Full Name */}
//               <div className="mb-4">
//                 <label htmlFor="name" className="block text-sm font-medium text-gray-700 mb-1.5">
//                   Full Name
//                 </label>
//                 <input
//                   type="text"
//                   id="name"
//                   value={name}
//                   onChange={(e) => setName(e.target.value)}
//                   className="w-full px-4 py-3 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-transparent transition-all duration-200 text-sm"
//                   placeholder="Enter your full name"
//                 />
//               </div>

//               {/* Email with OTP verification */}
//               <div className="mb-4">
//                 <label htmlFor="email" className="block text-sm font-medium text-gray-700 mb-1.5">
//                   Email Address
//                 </label>
//                 <div className="flex gap-2">
//                   <input
//                     type="email"
//                     id="email"
//                     value={email}
//                     onChange={(e) => setEmail(e.target.value)}
//                     className="w-full px-4 py-3 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-transparent transition-all duration-200 text-sm"
//                     placeholder="Enter your email address"
//                     disabled={isEmailVerified}
//                   />
//                   <button
//                     type="button"
//                     onClick={() => sendOtp('email')}
//                     disabled={!email || isEmailVerified || isLoading}
//                     className={`px-4 py-3 rounded-md text-sm font-medium transition-colors ${
//                       isEmailVerified
//                         ? 'bg-green-100 text-green-800 cursor-default'
//                         : 'bg-indigo-600 text-white hover:bg-indigo-700'
//                     }`}
//                   >
//                     {isEmailVerified ? 'Verified' : 'Verify'}
//                   </button>
//                 </div>

//                 {/* Email OTP Input (conditionally rendered) */}
//                 {email && !isEmailVerified && isOtpSent && (
//                   <div className="mt-3">
//                     <label className="block text-xs font-medium text-gray-700 mb-1.5">
//                       Enter Email OTP
//                     </label>
//                     <div className="flex gap-2 items-center">
//                       <OtpInput
//                         length={4}
//                         value={emailOtp}
//                         onChange={(newOtp) => handleOtpChange(newOtp, setEmailOtp)}
//                         inputClassName="w-10 h-10 text-sm"
//                       />
//                       <button
//                         type="button"
//                         onClick={() => verifyOtp('email')}
//                         disabled={emailOtp.join('').length !== 4 || isLoading}
//                         className="px-3 py-2 bg-gray-200 text-gray-800 rounded-md text-xs font-medium hover:bg-gray-300 transition-all duration-200"
//                       >
//                         Submit
//                       </button>
//                     </div>
//                   </div>
//                 )}
//               </div>

//               {/* WhatsApp with OTP verification */}
//               <div className="mb-4">
//                 <label htmlFor="whatsapp" className="block text-sm font-medium text-gray-700 mb-1.5">
//                   WhatsApp Number
//                 </label>
//                 <div className="flex gap-2">
//                   <input
//                     type="tel"
//                     id="whatsapp"
//                     value={whatsapp}
//                     onChange={(e) => setWhatsapp(e.target.value)}
//                     className="w-full px-4 py-3 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-transparent transition-all duration-200 text-sm"
//                     placeholder="Enter your WhatsApp number"
//                     disabled={isWhatsappVerified}
//                   />
//                   <button
//                     type="button"
//                     onClick={() => sendOtp('whatsapp')}
//                     disabled={!whatsapp || isWhatsappVerified || isLoading}
//                     className={`px-4 py-3 rounded-md text-sm font-medium transition-colors ${
//                       isWhatsappVerified
//                         ? 'bg-green-100 text-green-800 cursor-default'
//                         : 'bg-indigo-600 text-white hover:bg-indigo-700'
//                     }`}
//                   >
//                     {isWhatsappVerified ? 'Verified' : 'Verify'}
//                   </button>
//                 </div>

//                 {/* WhatsApp OTP Input (conditionally rendered) */}
//                 {whatsapp && !isWhatsappVerified && isOtpSent && (
//                   <div className="mt-3">
//                     <label className="block text-xs font-medium text-gray-700 mb-1.5">
//                       Enter WhatsApp OTP
//                     </label>
//                     <div className="flex gap-2 items-center">
//                       <OtpInput
//                         length={4}
//                         value={whatsappOtp}
//                         onChange={(newOtp) => handleOtpChange(newOtp, setWhatsappOtp)}
//                         inputClassName="w-10 h-10 text-sm"
//                       />
//                       <button
//                         type="button"
//                         onClick={() => verifyOtp('whatsapp')}
//                         disabled={whatsappOtp.join('').length !== 4 || isLoading}
//                         className="px-3 py-2 bg-gray-200 text-gray-800 rounded-md text-xs font-medium hover:bg-gray-300 transition-all duration-200"
//                       >
//                         Submit
//                       </button>
//                     </div>
//                   </div>
//                 )}
//               </div>

//               {/* Submit Button */}
//               <button
//                 type="submit"
//                 disabled={isLoading || !name || !email || !whatsapp || !isEmailVerified || !isWhatsappVerified}
//                 className="w-full bg-indigo-600 text-white py-3 px-4 rounded-md hover:bg-indigo-700 transition-all duration-300 mt-4 mb-6 whitespace-nowrap cursor-pointer font-medium disabled:opacity-70 disabled:cursor-not-allowed text-sm"
//               >
//                 {isLoading ? (
//                   <span className="flex items-center justify-center">
//                     <svg
//                       className="animate-spin -ml-1 mr-3 h-4 w-4 text-white"
//                       xmlns="http://www.w3.org/2000/svg"
//                       fill="none"
//                       viewBox="0 0 24 24"
//                     >
//                       <circle
//                         className="opacity-25"
//                         cx="12"
//                         cy="12"
//                         r="10"
//                         stroke="currentColor"
//                         strokeWidth="4"
//                       ></circle>
//                       <path
//                         className="opacity-75"
//                         fill="currentColor"
//                         d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
//                       ></path>
//                     </svg>
//                     Processing...
//                   </span>
//                 ) : (
//                   <span className="flex items-center justify-center">
//                     Create Account
//                   </span>
//                 )}
//               </button>

//               {/* Sign In Link */}
//               <div className="text-center text-gray-500 text-sm">
//                 Already have an account?
//                 <button
//                   type="button"
//                   onClick={() => setLoginStep('initial')}
//                   className="text-indigo-600 font-medium ml-1 hover:underline cursor-pointer"
//                 >
//                   Sign in
//                 </button>
//               </div>
//             </form>
//           )}
//         </div>
//       </div>
//     </div>
//   );
// };

// export default LoginModal;


'use client';

import { useState, useEffect } from 'react';
import Image from 'next/image';
import { useRouter } from 'next/navigation';
import Logo2 from "../../../public/assets/img/logo2.jpg";
import { X, Check, AlertCircle, Edit2, Loader2 } from 'lucide-react';
import { useCommonContext } from '@/app/contexts/commonContext';
import OtpInput from '../common/OtpInput';
import authService from '../../../api/auth/authService';
import './LoginSignupPopup.scss';
import './LoginModal.scss';

interface LoginModalProps {
  isOpen: boolean;
  onClose: () => void;
  registerRoute: string; // Route to redirect for new users
}

// Real API functions using authService
const initiateOtpApi = async (identifier: string) => {
  try {
    const response = await authService.initiateOtp(identifier);
    return response;
  } catch (error) {
    console.error('Error initiating OTP:', error);
    return {
      success: false,
      message: 'Failed to send OTP. Please try again.',
      error
    };
  }
};

const verifyOtpApi = async (identifier: string, otp: string) => {
  try {
    const response = await authService.verifyOtp(identifier, otp);
    return response;
  } catch (error) {
    console.error('Error verifying OTP:', error);
    return {
      success: false,
      message: 'Failed to verify OTP. Please try again.',
      error
    };
  }
};

// Login flow states
type LoginStep = 'initial' | 'otp-verification';

const LoginModal: React.FC<LoginModalProps> = ({ isOpen, onClose, registerRoute }) => {
  // Router for redirects
  const router = useRouter();
  
  // Common state
  const [loginStep, setLoginStep] = useState<LoginStep>('initial');
  const [identifier, setIdentifier] = useState(''); // Email or WhatsApp
  const [message, setMessage] = useState<{type: 'success' | 'error', text: string} | null>(null);
  const { setIsLoading, setUserData, setIsLoggedIn } = useCommonContext();
  const [loading, setLoading] = useState(false);
  const [isNewUser, setIsNewUser] = useState(false);

  // OTP verification state
  const [otp, setOtp] = useState(['', '', '', '', '', '']);
  const [otpTimer, setOtpTimer] = useState(60);
  const [isOtpSent, setIsOtpSent] = useState(false);



  // Reset form when modal is opened/closed
  useEffect(() => {
    if (isOpen) {
      resetForm();
    }
  }, [isOpen]);

  // OTP timer countdown
  useEffect(() => {
    let interval: NodeJS.Timeout;
    if (isOtpSent && otpTimer > 0) {
      interval = setInterval(() => {
        setOtpTimer((prev) => prev - 1);
      }, 1000);
    } else if (otpTimer === 0) {
      setIsOtpSent(false);
    }
    return () => clearInterval(interval);
  }, [isOtpSent, otpTimer]);

  if (!isOpen) return null;

  // Reset all form fields
  const resetForm = () => {
    setLoginStep('initial');
    setIdentifier('');
    setMessage(null);
    setOtp(['', '', '', '', '', '']);
    setOtpTimer(60);
    setIsOtpSent(false);
    setIsNewUser(false);
    setLoading(false);
  };

  // Validate email format
  const isValidEmail = (email: string) => {
    return /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email);
  };

  // Validate phone number format (basic validation)
  const isValidPhone = (phone: string) => {
    return /^\d{10,15}$/.test(phone);
  };

  // Handle initial form submission
  const handleInitialSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setMessage(null);

    // Validate input
    if (!identifier) {
      setMessage({
        type: 'error',
        text: 'Please enter your email or WhatsApp number.'
      });
      return;
    }

    // Basic validation
    const isEmail = identifier.includes('@');
    if (isEmail && !isValidEmail(identifier)) {
      setMessage({
        type: 'error',
        text: 'Please enter a valid email address.'
      });
      return;
    } else if (!isEmail && !isValidPhone(identifier)) {
      setMessage({
        type: 'error',
        text: 'Please enter a valid phone number (10-15 digits).'
      });
      return;
    }

    try {
      setLoading(true);
      setIsLoading(true);

      // Send OTP directly (the API will handle user existence check)
      const otpResponse = await initiateOtpApi(identifier);

      if (otpResponse.success) {
        // For now, assume all users are new until we get user data from verify OTP
        // This will be determined in the verify step
        setIsNewUser(true); // Will be updated after OTP verification

        // Proceed to OTP verification step
        setLoginStep('otp-verification');
        setIsOtpSent(true);
        setOtpTimer(60);
        setMessage({
          type: 'success',
          text: `OTP sent successfully to ${isEmail ? 'email' : 'WhatsApp'}.`
        });
      } else {
        throw new Error(otpResponse.message || 'Failed to send OTP');
      }
    } catch (error: any) {
      setMessage({
        type: 'error',
        text: error.message || 'Something went wrong. Please try again.'
      });
    } finally {
      setLoading(false);
      setIsLoading(false);
    }
  };

  // Handle OTP verification
  const handleOtpVerification = async (e: React.FormEvent, otpToVerify?: string[]) => {
    e.preventDefault();
    setMessage(null);

    // Use provided OTP or fall back to state OTP
    const currentOtp = otpToVerify || otp;
    const fullOtp = currentOtp.join('');
    if (fullOtp.length !== 6) {
      setMessage({
        type: 'error',
        text: 'Please enter a valid 6-digit OTP.'
      });
      return;
    }

    try {
      setLoading(true);
      setIsLoading(true);

      // Verify OTP
      const response = await verifyOtpApi(identifier, fullOtp);

      try {
        if (response.success && response.data) {
          const { user, isNewUser: apiIsNewUser } = response.data;

        // Update isNewUser based on API response
        setIsNewUser(apiIsNewUser);

        // Handle successful verification
        if (apiIsNewUser) {
          // New user - show success and redirect after modal closes
          setMessage({
            type: 'success',
            text: 'Verification successful! Redirecting to complete registration.'
          });

          // Close modal after delay, then redirect
          setTimeout(() => {
            onClose();
            router.push(registerRoute);
          }, 1500);
        } else {
          // Existing user - set user data and log in
          if (user) {
            // Transform API user data to match your existing format
            const userData = {
              first_name: user.firstName || user.name || (identifier.includes('@') ? identifier.split('@')[0] : 'User'),
              second_name: user.lastName || '',
              email: user.email || (identifier.includes('@') ? identifier : ''),
              phone: user.phone || (!identifier.includes('@') ? identifier : '')
            };

            // Save user data
            localStorage.setItem('userData', JSON.stringify(userData));
            setUserData(userData);
            setIsLoggedIn(true);
          }

          setMessage({
            type: 'success',
            text: 'Login successful! Welcome back.'
          });

          // Close modal after a short delay
          setTimeout(() => {
            onClose();
          }, 1500);
        }
        setIsLoggedIn(true);
      } else {
        throw new Error(response.message || 'Invalid OTP. Please try again.');
      }
      } catch (processingError) {
        console.error('Error in verification processing:', processingError);
        throw processingError;
      }
    } catch (error: any) {
      console.error('Error in handleOtpVerification:', error);
      setMessage({
        type: 'error',
        text: error.message || 'Failed to verify OTP. Please try again.'
      });
    } finally {
      setLoading(false);
      setIsLoading(false);
    }
  };

  // Handle OTP input change
  const handleOtpChange = (newOtp: string[]) => {
    setOtp(newOtp);
    // Clear error message when user starts typing
    if (message?.type === 'error') {
      setMessage(null);
    }

    // Check if OTP is complete (6 digits) and auto-submit
    const fullOtp = newOtp.join('');
    if (fullOtp.length === 6) {
      // Auto-submit when OTP is complete, passing the current OTP
      handleOtpVerification({ preventDefault: () => {} } as React.FormEvent, newOtp);
    }
  };

  // Resend OTP
  const resendOtp = async () => {
    if (isOtpSent && otpTimer > 0) return;
    
    try {
      setLoading(true);
      setIsLoading(true);
      setMessage(null);
      
      // Send OTP again
      const response = await initiateOtpApi(identifier);

      if (response.success) {
        setIsOtpSent(true);
        setOtpTimer(60);
        setMessage({
          type: 'success',
          text: 'OTP resent successfully.'
        });
      } else {
        throw new Error(response.message || 'Failed to resend OTP');
      }
    } catch (error: any) {
      setMessage({
        type: 'error',
        text: error.message || 'Failed to resend OTP. Please try again.'
      });
    } finally {
      setLoading(false);
      setIsLoading(false);
    }
  };

  // Handle input change
  const handleIdentifierChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setIdentifier(e.target.value);
    // Clear error message when user starts typing
    if (message?.type === 'error') {
      setMessage(null);
    }
  };

  // Handle edit/back button
  const handleBackToInitial = () => {
    // Reset OTP values when going back
    setOtp(['', '', '', '', '', '']);
    setLoginStep('initial');
    setMessage(null);
  };

  return (
    <div className="fixed inset-0 z-[9999] flex items-center justify-center bg-black/50 backdrop-blur-sm p-4 overflow-y-auto">
      <div className="relative w-full max-w-md bg-white rounded-xl shadow-2xl overflow-hidden animate-fadeIn">
        {/* Close button */}
        <button
          onClick={onClose}
          className="absolute top-4 right-4 text-gray-400 hover:text-gray-600 transition-colors focus:outline-none focus:ring-2 focus:ring-gray-400 focus:ring-offset-2 rounded-full p-1"
          aria-label="Close popup"
        >
          <X size={20} />
        </button>

        {/* Logo centered at top */}
        <div className="flex justify-center pt-8 pb-4">
          <div className="w-16 h-16 relative">
            <Image
              src={Logo2}
              alt="Readdy Logo"
              className="object-contain"
              fill
              priority
            />
          </div>
        </div>

        {/* Form Header */}
        <div className="text-center px-8">
          <h1 className="text-2xl font-bold text-gray-900 mb-2">
            {loginStep === 'initial' ? 'Sign in to Readdy' : 'Verify Your Identity'}
          </h1>
          <p className="text-sm text-gray-500 mb-6">
            {loginStep === 'initial' 
              ? 'Welcome back! Please sign in to continue' 
              : `Enter the 6-digit code sent to ${identifier}`}
          </p>
        </div>

        <div className="px-8 pb-8">
          {/* Status Message */}
          {message && (
            <div className={`p-3 mb-4 rounded-lg flex items-start gap-2 text-sm ${
              message.type === 'success'
                ? 'bg-green-50 text-green-800 border border-green-200'
                : 'bg-red-50 text-red-800 border border-red-200'
            }`}>
              {message.type === 'success'
                ? <Check size={16} className="text-green-500 mt-0.5 flex-shrink-0" />
                : <AlertCircle size={16} className="text-red-500 mt-0.5 flex-shrink-0" />
              }
              <span>{message.text}</span>
            </div>
          )}

          {/* Initial Login Form */}
          {loginStep === 'initial' && (
            <form onSubmit={handleInitialSubmit} className="space-y-5">
              {/* Google Sign In Button */}
              <button
                type="button"
                className="w-full flex items-center justify-center gap-3 border border-gray-300 rounded-lg py-3 px-4 text-gray-700 font-medium hover:bg-gray-50 transition-colors text-sm focus:outline-none focus:ring-2 focus:ring-gray-400 focus:ring-offset-2"
              >
                <svg
                  width="18"
                  height="18"
                  xmlns="http://www.w3.org/2000/svg"
                  viewBox="0 0 48 48"
                >
                  <path
                    fill="#EA4335"
                    d="M24 9.5c3.54 0 6.71 1.22 9.21 3.6l6.85-6.85C35.9 2.38 30.47 0 24 0 14.62 0 6.51 5.38 2.56 13.22l7.98 6.19C12.43 13.72 17.74 9.5 24 9.5z"
                  />
                  <path
                    fill="#4285F4"
                    d="M46.98 24.55c0-1.57-.15-3.09-.38-4.55H24v9.02h12.94c-.58 2.96-2.26 5.48-4.78 7.18l7.73 6c4.51-4.18 7.09-10.36 7.09-17.65z"
                  />
                  <path
                    fill="#FBBC05"
                    d="M10.53 28.59c-.48-1.45-.76-2.99-.76-4.59s.27-3.14.76-4.59l-7.98-6.19C.92 16.46 0 20.12 0 24c0 3.88.92 7.54 2.56 10.78l7.97-6.19z"
                  />
                  <path
                    fill="#34A853"
                    d="M24 48c6.48 0 11.93-2.13 15.89-5.81l-7.73-6c-2.15 1.45-4.92 2.3-8.16 2.3-6.26 0-11.57-4.22-13.47-9.91l-7.98 6.19C6.51 42.62 14.62 48 24 48z"
                  />
                </svg>
                Continue with Google
              </button>

              {/* Divider */}
              <div className="flex items-center">
                <div className="flex-1 border-t border-gray-200"></div>
                <span className="px-4 text-gray-400 text-xs font-medium">or</span>
                <div className="flex-1 border-t border-gray-200"></div>
              </div>

              {/* Email/WhatsApp Input */}
              <div>
                <label
                  htmlFor="identifier"
                  className="block text-sm font-medium text-gray-700 mb-1.5"
                >
                  Email / WhatsApp
                </label>
                <input
                  type="text"
                  id="identifier"
                  value={identifier}
                  onChange={handleIdentifierChange}
                  className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-transparent transition-all duration-200 text-sm"
                  placeholder="Enter your email or WhatsApp number"
                  disabled={loading}
                />
              </div>

              {/* Continue Button */}
              <button
                type="submit"
                disabled={loading || !identifier.trim()}
                className="w-full bg-indigo-600 text-white py-3 px-4 rounded-lg hover:bg-indigo-700 transition-all duration-300 font-medium disabled:opacity-70 disabled:cursor-not-allowed text-sm focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2"
              >
                {loading ? (
                  <span className="flex items-center justify-center">
                    <Loader2 className="animate-spin mr-2 h-4 w-4" />
                    Processing...
                  </span>
                ) : (
                  <span>Continue</span>
                )}
              </button>

              <p className="text-center text-xs text-gray-500 mt-4">
                By continuing, you agree to our <a href="#" className="text-indigo-600 hover:underline">Terms of Service</a> and <a href="#" className="text-indigo-600 hover:underline">Privacy Policy</a>.
              </p>
            </form>
          )}

          {/* OTP Verification Form */}
          {loginStep === 'otp-verification' && (
            <form onSubmit={handleOtpVerification} className="space-y-5">
              <div>
                <div className="text-center mb-4">
                  <div className="text-gray-700 text-sm py-1 px-3 bg-gray-100 rounded-full inline-flex items-center">
                    {identifier}
                    <button
                      type="button"
                      onClick={handleBackToInitial}
                      className="ml-2 text-gray-500 hover:text-gray-700 focus:outline-none"
                      aria-label="Edit identifier"
                    >
                      <Edit2 size={14} className="text-indigo-600" />
                    </button>
                  </div>
                </div>

                {/* OTP Input Fields */}
                <div className="mb-6">
                  <OtpInput
                    length={6}
                    value={otp}
                    onChange={handleOtpChange}
                    autoFocus={true}
                    className="mb-4"
                  />

                  <div className="text-sm text-gray-500 text-center">
                    Didn&apos;t receive a code?
                    <button
                      type="button"
                      onClick={resendOtp}
                      disabled={loading || (isOtpSent && otpTimer > 0)}
                      className="text-indigo-600 ml-1 hover:underline font-medium disabled:text-gray-400 disabled:hover:no-underline focus:outline-none"
                    >
                      Resend {isOtpSent && otpTimer > 0 ? `(${otpTimer}s)` : ''}
                    </button>
                  </div>
                </div>
              </div>

              {/* Verify Button */}
              <button
                type="submit"
                disabled={loading || otp.join('').length !== 6}
                className="w-full bg-indigo-600 text-white py-3 px-4 rounded-lg hover:bg-indigo-700 transition-all duration-300 font-medium disabled:opacity-70 disabled:cursor-not-allowed text-sm focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2"
              >
                {loading ? (
                  <span className="flex items-center justify-center">
                    <Loader2 className="animate-spin mr-2 h-4 w-4" />
                    Verifying...
                  </span>
                ) : (
                  <span>Verify</span>
                )}
              </button>

              <div className="text-center">
                <button
                  type="button"
                  className="text-gray-500 text-sm hover:underline focus:outline-none"
                >
                  Use another method
                </button>
              </div>
            </form>
          )}
        </div>
      </div>
    </div>
  );
};

export default LoginModal;